(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["my-setting-xieyi"],{4130:function(t,n,e){"use strict";e.r(n);var i=e("51df"),o=e.n(i);for(var u in i)["default"].indexOf(u)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(u);n["default"]=o.a},"460d":function(t,n,e){var i=e("6f60");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=e("967d").default;o("4e41f540",i,!0,{sourceMap:!1,shadowMode:!1})},"4cff":function(t,n,e){"use strict";e.r(n);var i=e("5571"),o=e("4130");for(var u in o)["default"].indexOf(u)<0&&function(t){e.d(n,t,(function(){return o[t]}))}(u);e("c74f");var a=e("828b"),c=Object(a["a"])(o["default"],i["b"],i["c"],!1,null,"546d1bc5",null,!1,i["a"],void 0);n["default"]=c.exports},"51df":function(t,n,e){"use strict";e("6a54"),Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;n.default={data:function(){return{content:""}},onLoad:function(){this.getGuize()},methods:{getGuize:function(){var t=this;this.$Request.getT("/app/common/type/177").then((function(n){0===n.code&&(t.content=n.data.value)}))}}}},5571:function(t,n,e){"use strict";e.d(n,"b",(function(){return i})),e.d(n,"c",(function(){return o})),e.d(n,"a",(function(){}));var i=function(){var t=this.$createElement,n=this._self._c||t;return n("v-uni-view",{staticClass:"home1",staticStyle:{"line-height":"26px",padding:"32upx"}},[n("v-uni-view",{staticStyle:{color:"#000000","font-size":"28upx"},domProps:{innerHTML:this._s(this.content)}})],1)},o=[]},"6f60":function(t,n,e){var i=e("c86c");n=i(!1),n.push([t.i,"uni-page-body[data-v-546d1bc5]{background:#fff}body.?%PAGE?%[data-v-546d1bc5]{background:#fff}",""]),t.exports=n},c74f:function(t,n,e){"use strict";var i=e("460d"),o=e.n(i);o.a}}]);