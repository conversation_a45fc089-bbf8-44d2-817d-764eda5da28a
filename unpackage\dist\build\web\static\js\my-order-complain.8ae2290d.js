(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["my-order-complain"],{"1eeb":function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@font-face{font-family:uniicons;font-weight:400;font-style:normal;src:url(https://img-cdn-qiniu.dcloud.net.cn/fonts/uni.ttf) format("truetype")}uni-page-body[data-v-c3fb7bda]{background-color:#f5f5f5!important}body.?%PAGE?%[data-v-c3fb7bda]{background-color:#f5f5f5!important}uni-view[data-v-c3fb7bda]{font-size:%?28?%}.release_image[data-v-c3fb7bda]{margin:%?20?%;width:95%\n\t/* margin: 0 auto; */}\n\n\n\n/*问题反馈*/.feedback-title[data-v-c3fb7bda]{display:flex;flex-direction:row;justify-content:space-between;align-items:center;padding:%?20?%;color:#8f8f94;font-size:%?28?%}.feedback-star-view.feedback-title[data-v-c3fb7bda]{justify-content:flex-start;margin:0}.feedback-body[data-v-c3fb7bda]{font-size:%?32?%;padding:%?16?%;margin:%?16?%;border-radius:%?16?%;background:#fff}.feedback-textare[data-v-c3fb7bda]{height:%?200?%;font-size:%?34?%;line-height:%?50?%;width:100%;box-sizing:border-box;padding:%?20?% %?30?% 0}.feedback-input[data-v-c3fb7bda]{font-size:%?32?%;height:%?60?%;padding:%?15?% %?20?%;line-height:%?60?%}.feedback-submit[data-v-c3fb7bda]{background:#ac75fe;color:#fff;margin:%?20?%;margin-top:%?32?%}',""]),t.exports=e},"252d":function(t,e,i){var n=i("1eeb");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("6211fe60",n,!0,{sourceMap:!1,shadowMode:!1})},"28b8":function(t,e,i){"use strict";i.r(e);var n=i("e48a"),a=i.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);e["default"]=a.a},"300b":function(t,e,i){"use strict";var n=i("252d"),a=i.n(n);a.a},3471:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){var i="undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!i){if(Array.isArray(t)||(i=(0,n.default)(t))||e&&t&&"number"===typeof t.length){i&&(t=i);var a=0,s=function(){};return{s:s,n:function(){return a>=t.length?{done:!0}:{done:!1,value:t[a++]}},e:function(t){throw t},f:s}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,d=!0,r=!1;return{s:function(){i=i.call(t)},n:function(){var t=i.next();return d=t.done,t},e:function(t){r=!0,o=t},f:function(){try{d||null==i["return"]||i["return"]()}finally{if(r)throw o}}}},i("01a2"),i("e39c"),i("bf0f"),i("844d"),i("18f7"),i("de6c"),i("7a76"),i("c9b5");var n=function(t){return t&&t.__esModule?t:{default:t}}(i("5d6b"))},"454c":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"con"},[i("v-uni-movable-area",{staticClass:"area",style:{height:t.areaHeight},on:{mouseenter:function(e){arguments[0]=e=t.$handleEvent(e),t.mouseenter.apply(void 0,arguments)},mouseleave:function(e){arguments[0]=e=t.$handleEvent(e),t.mouseleave.apply(void 0,arguments)}}},[t._l(t.imageList,(function(e,n){return[i("v-uni-movable-view",{key:e.id+"_0",staticClass:"view",style:{width:t.viewWidth+"px",height:t.viewWidth+"px","z-index":e.zIndex,opacity:e.opacity},attrs:{x:e.x,y:e.y,direction:"all",damping:40,disabled:e.disable},on:{change:function(i){arguments[0]=i=t.$handleEvent(i),t.onChange(i,e)},touchstart:function(i){arguments[0]=i=t.$handleEvent(i),t.touchstart(e)},mousedown:function(i){arguments[0]=i=t.$handleEvent(i),t.touchstart(e)},touchend:function(i){arguments[0]=i=t.$handleEvent(i),t.touchend(e)},mouseup:function(i){arguments[0]=i=t.$handleEvent(i),t.touchend(e)}}},[i("v-uni-view",{staticClass:"area-con",style:{width:t.childWidth,height:t.childWidth,transform:"scale("+e.scale+")"}},[i("v-uni-image",{staticClass:"pre-image",attrs:{src:e.src,mode:"aspectFill"}}),t.isSelect?i("v-uni-view",{staticClass:"del-con",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.delImage(e,n)},touchstart:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.delImageMp(e,n)},touchend:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.nothing()},mousedown:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.nothing()},mouseup:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.nothing()}}},[i("v-uni-view",{staticClass:"del-wrap"},[i("v-uni-image",{staticClass:"del-image",attrs:{src:"https://renwu.xiansqx.com/img/20210813/f44b954827604db8bc9c93f0ffc88ab5.png"}})],1)],1):t._e()],1)],1)]})),t.imageList.length<t.number&&t.isSelect?i("v-uni-view",{staticClass:"add",style:{top:t.add.y,left:t.add.x,width:t.viewWidth+"px",height:t.viewWidth+"px"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.addImages.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"add-wrap",style:{width:t.childWidth,height:t.childWidth}},[i("v-uni-image",{staticStyle:{width:"192rpx",height:"192rpx"},attrs:{src:"https://renwu.xiansqx.com/img/20210813/f44b954827604db8bc9c93f0ffc88ab5.png"}})],1)],1):t._e()],2)],1)},a=[]},"6c6e":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return n}));var n={uModal:i("7e01").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"page",staticStyle:{"background-color":"#F5F5F5"}},[i("v-uni-view",{staticClass:"feedback-title"},[i("v-uni-text",[t._v("投诉内容")])],1),i("v-uni-view",{staticClass:"feedback-body"},[i("v-uni-textarea",{staticClass:"feedback-textare",attrs:{placeholder:"请输入投诉内容..."},model:{value:t.sendDate.content,callback:function(e){t.$set(t.sendDate,"content",e)},expression:"sendDate.content"}})],1),i("v-uni-view",{staticClass:"feedback-title"},[i("v-uni-text",[t._v("投诉图片")])],1),i("v-uni-view",{staticClass:"release_image"},[i("shmily-drag-image",{attrs:{list:t.imageList,number:6},on:{"update:list":function(e){arguments[0]=e=t.$handleEvent(e),t.imageList=e}}})],1),i("v-uni-button",{staticClass:"feedback-submit",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.send.apply(void 0,arguments)}}},[t._v("提交投诉")]),i("u-modal",{attrs:{content:t.meContent,title:t.meTitle,"show-cancel-button":t.meShowCancel,"confirm-text":t.meConfirmText,"cancel-text":t.meCancelText},on:{cancel:function(e){arguments[0]=e=t.$handleEvent(e),t.meHandleClose.apply(void 0,arguments)},confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.meHandleBtn.apply(void 0,arguments)}},model:{value:t.meShowModel,callback:function(e){t.meShowModel=e},expression:"meShowModel"}})],1)},s=[]},a554a:function(t,e,i){"use strict";i.r(e);var n=i("454c"),a=i("28b8");for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);i("b4cd");var o=i("828b"),d=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"34dcb145",null,!1,n["a"],void 0);e["default"]=d.exports},a70f:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.con .area[data-v-34dcb145]{width:100%}.con .area .view[data-v-34dcb145]{display:flex;justify-content:center;align-items:center}.con .area .view .area-con[data-v-34dcb145]{position:relative}.con .area .view .area-con .pre-image[data-v-34dcb145]{width:100%;height:100%;background:#f5f5f5}.con .area .view .area-con .del-con[data-v-34dcb145]{position:absolute;top:%?-8?%;right:%?-4?%;padding:0 0 %?20?% %?20?%}.con .area .view .area-con .del-con .del-wrap[data-v-34dcb145]{width:%?36?%;height:%?36?%;background-color:red;border-radius:50%;display:flex;justify-content:center;align-items:center}.con .area .view .area-con .del-con .del-wrap .del-image[data-v-34dcb145]{width:%?20?%;height:%?20?%}.con .area .add[data-v-34dcb145]{position:absolute;display:flex;justify-content:center;align-items:center}.con .area .add .add-wrap[data-v-34dcb145]{display:flex;justify-content:center;align-items:center;background-color:#f5f5f5}',""]),t.exports=e},b4cd:function(t,e,i){"use strict";var n=i("c2a3"),a=i.n(n);a.a},baac:function(t,e,i){"use strict";i("6a54");var n=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(i("a554a")),s={components:{shmilyDragImage:a.default},data:function(){return{meShowModel:!1,meShowCancel:!0,meTitle:"提示",meContent:"",meConfirmText:"确认",meCancelText:"取消",meIndex:"",msgContents:["界面显示错乱","启动缓慢，卡出翔了","UI无法直视，丑哭了","偶发性崩溃"],stars:[1,2,3,4,5],imageList:[],byUserId:"",platform:1,platformId:"",sendDate:{content:"",contact:""}}},onLoad:function(t){t.byUserId&&(this.byUserId=t.byUserId),t.platform&&(this.platform=t.platform),t.platformId&&(this.platformId=t.platformId)},methods:{meHandleBtn:function(){this.meIndex},meHandleClose:function(){this.meIndex},send:function(){var t=this,e="",i=this.$queue.getData("userId");if(this.sendDate.content){if(this.$queue.getChatSearchKeys(this.sendDate.content))return uni.showToast({title:"输入内容带有非法关键字请重新输入",mask:!1,duration:1500,icon:"none"}),void this.$Request.postT("/app/risk/insertRisk?riskType=4&content="+this.sendDate.content).then((function(t){}));if(0==this.imageList.length)return uni.hideLoading(),void this.$queue.showToast("请添加图片!");for(var n=0;n<this.imageList.length;n++)e=0===n?this.imageList[n]:e+","+this.imageList[n];this.$queue.showLoading("加载中..."),this.$Request.postJson("/app/message/insertMessage",{content:this.sendDate.content,image:e,state:3,platform:this.platform,userId:i,byUserId:this.byUserId,platformId:this.platformId}).then((function(e){0===e.code?(t.$queue.showToast("投诉成功，等待平台处理！"),setTimeout((function(){uni.navigateBack()}),2e3)):(uni.hideLoading(),t.meShowModel=!0,t.meTitle="投诉失败",t.meContent=e.msg,t.meIndex="m1")}))}else uni.showToast({icon:"none",title:"请输入投诉内容"})}}};e.default=s},c2a3:function(t,e,i){var n=i("a70f");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("2ab7de9e",n,!0,{sourceMap:!1,shadowMode:!1})},e48a:function(t,e,i){"use strict";i("6a54");var n=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(i("3471"));i("64aa"),i("5c47"),i("bf0f"),i("2797"),i("bd06"),i("dd2b"),i("f7a5"),i("4100"),i("aa9c"),i("c9b5"),i("ab80");var s=n(i("b741")),o={data:function(){return{imageList:[],width:0,add:{x:0,y:0},colsValue:0,viewWidth:0,tempItem:null,timer:null,changeStatus:!0,preStatus:!0}},props:{list:{type:Array,default:function(){return[]}},number:{type:Number,default:5},imageWidth:{type:Number,default:230},cols:{type:Number,default:3},padding:{type:Number,default:10},scale:{type:Number,default:1.1},opacity:{type:Number,default:.7},isSelect:{type:Boolean,default:!0},custom:{type:Boolean,default:!1}},computed:{areaHeight:function(){return this.imageList.length<this.number?Math.ceil((this.imageList.length+1)/this.colsValue)*this.viewWidth+"px":Math.ceil(this.imageList.length/this.colsValue)*this.viewWidth+"px"},childWidth:function(){return this.viewWidth-2*this.rpx2px(this.padding)+"px"}},created:function(){this.width=uni.getSystemInfoSync().windowWidth,this.viewWidth=this.rpx2px(this.imageWidth)},mounted:function(){var t=this,e=uni.createSelectorQuery().in(this);e.select(".area").boundingClientRect((function(e){t.colsValue=Math.floor(e.width/t.viewWidth),t.cols>0&&(t.colsValue=t.cols,t.viewWidth=e.width/t.cols);var i,n=(0,a.default)(t.list);try{for(n.s();!(i=n.n()).done;){var s=i.value;t.addProperties(s)}}catch(o){n.e(o)}finally{n.f()}})),e.exec()},methods:{onChange:function(t,e){var i=this;if(e&&(e.oldX=t.detail.x,e.oldY=t.detail.y,"touch"===t.detail.source)){e.moveEnd&&(e.offset=Math.sqrt(Math.pow(e.oldX-e.absX*this.viewWidth,2)+Math.pow(e.oldY-e.absY*this.viewWidth,2)));var n=Math.floor((t.detail.x+this.viewWidth/2)/this.viewWidth);if(n>=this.colsValue)return;var s=Math.floor((t.detail.y+this.viewWidth/2)/this.viewWidth),o=this.colsValue*s+n;if(e.index!=o&&o<this.imageList.length){this.changeStatus=!1;var d,r=(0,a.default)(this.imageList);try{var c=function(){var t=d.value;e.index>o&&t.index>=o&&t.index<e.index?i.change(t,1):e.index<o&&t.index<=o&&t.index>e.index?i.change(t,-1):t.id!=e.id&&(t.offset=0,t.x=t.oldX,t.y=t.oldY,setTimeout((function(){i.$nextTick((function(){t.x=t.absX*i.viewWidth,t.y=t.absY*i.viewWidth}))}),0))};for(r.s();!(d=r.n()).done;)c()}catch(l){r.e(l)}finally{r.f()}e.index=o,e.absX=n,e.absY=s,this.sortList()}}},change:function(t,e){var i=this;t.index+=e,t.offset=0,t.x=t.oldX,t.y=t.oldY,t.absX=t.index%this.colsValue,t.absY=Math.floor(t.index/this.colsValue),setTimeout((function(){i.$nextTick((function(){t.x=t.absX*i.viewWidth,t.y=t.absY*i.viewWidth}))}),0)},touchstart:function(t){var e=this;this.imageList.forEach((function(t){t.zIndex=t.index+9})),t.zIndex=99,t.moveEnd=!0,this.tempItem=t,this.timer=setTimeout((function(){t.scale=e.scale,t.opacity=e.opacity,clearTimeout(e.timer),e.timer=null}),200)},touchend:function(t){var e=this;this.previewImage(t),t.scale=1,t.opacity=1,t.x=t.oldX,t.y=t.oldY,t.offset=0,t.moveEnd=!1,setTimeout((function(){e.$nextTick((function(){t.x=t.absX*e.viewWidth,t.y=t.absY*e.viewWidth,e.tempItem=null,e.changeStatus=!0}))}),0)},previewImage:function(t){var e=this;if(this.timer&&this.preStatus&&this.changeStatus&&t.offset<28.28){clearTimeout(this.timer),this.timer=null;var i=this.list.findIndex((function(e){return e===t.src}));uni.previewImage({urls:this.list,current:i,success:function(){e.preStatus=!1,setTimeout((function(){e.preStatus=!0}),600)}})}else this.timer&&(clearTimeout(this.timer),this.timer=null)},mouseenter:function(){this.imageList.forEach((function(t){t.disable=!1}))},mouseleave:function(){var t=this;this.tempItem&&(this.imageList.forEach((function(e){e.disable=!0,e.zIndex=e.index+9,e.offset=0,e.moveEnd=!1,e.id==t.tempItem.id&&(t.timer&&(clearTimeout(t.timer),t.timer=null),e.scale=1,e.opacity=1,e.x=e.oldX,e.y=e.oldY,t.$nextTick((function(){e.x=e.absX*t.viewWidth,e.y=e.absY*t.viewWidth,t.tempItem=null})))})),this.changeStatus=!0)},addImages:function(){var t=this;if(console.log("addImages"),this.custom)this.$emit("addImage");else{var e=this.number-this.imageList.length;uni.chooseImage({count:e,sourceType:["album","camera"],success:function(i){for(var n=e<=i.tempFilePaths.length?e:i.tempFilePaths.length,a=0;a<n;a++)t.$queue.showLoading("上传中..."),uni.uploadFile({url:t.config("APIHOST1")+"/alioss/upload",filePath:i.tempFilePaths[a],name:"file",success:function(e){t.addProperties(JSON.parse(e.data).data),uni.hideLoading()}})}})}},config:function(t){var e=null;if(t){var i=t.split(".");if(e=i.length>1?s.default[i[0]][i[1]]||null:s.default[t]||null,null==e){var n=cache.get("web_config");n&&(e=i.length>1?n[i[0]][i[1]]||null:n[t]||null)}}return e},addImage:function(t){this.addProperties(t)},delImage:function(t,e){var i=this;this.imageList.splice(e,1);var n,s=(0,a.default)(this.imageList);try{var o=function(){var e=n.value;e.index>t.index&&(e.index-=1,e.x=e.oldX,e.y=e.oldY,e.absX=e.index%i.colsValue,e.absY=Math.floor(e.index/i.colsValue),i.$nextTick((function(){e.x=e.absX*i.viewWidth,e.y=e.absY*i.viewWidth})))};for(s.s();!(n=s.n()).done;)o()}catch(d){s.e(d)}finally{s.f()}this.add.x=this.imageList.length%this.colsValue*this.viewWidth+"px",this.add.y=Math.floor(this.imageList.length/this.colsValue)*this.viewWidth+"px",this.sortList()},delImageMp:function(t,e){},sortList:function(){var t=this.imageList.slice();console.log("获取到上传图片的列表",this.imageList),t.sort((function(t,e){return t.index-e.index}));for(var e=0;e<t.length;e++)t[e]=t[e].src;console.log("list",t),this.$emit("update:list",t)},addProperties:function(t){var e=this.imageList.length%this.colsValue,i=Math.floor(this.imageList.length/this.colsValue),n=e*this.viewWidth,a=i*this.viewWidth;this.imageList.push({src:t,x:n,y:a,oldX:n,oldY:a,absX:e,absY:i,scale:1,zIndex:9,opacity:1,index:this.imageList.length,id:this.guid(),disable:!1,offset:0,moveEnd:!1}),this.add.x=this.imageList.length%this.colsValue*this.viewWidth+"px",this.add.y=Math.floor(this.imageList.length/this.colsValue)*this.viewWidth+"px",this.sortList()},nothing:function(){},rpx2px:function(t){return this.width*t/750},guid:function(){function t(){return(65536*(1+Math.random())|0).toString(16).substring(1)}return t()+t()+"-"+t()+"-"+t()+"-"+t()+"-"+t()+t()+t()}}};e.default=o},e847:function(t,e,i){"use strict";i.r(e);var n=i("6c6e"),a=i("e9c1");for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);i("300b");var o=i("828b"),d=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"c3fb7bda",null,!1,n["a"],void 0);e["default"]=d.exports},e9c1:function(t,e,i){"use strict";i.r(e);var n=i("baac"),a=i.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);e["default"]=a.a}}]);