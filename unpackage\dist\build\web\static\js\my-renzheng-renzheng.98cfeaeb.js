(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["my-renzheng-renzheng"],{"3c41":function(n,e,t){"use strict";t.d(e,"b",(function(){return a})),t.d(e,"c",(function(){return u})),t.d(e,"a",(function(){return i}));var i={uIcon:t("3688").default},a=function(){var n=this,e=n.$createElement,t=n._self._c||e;return t("v-uni-view",{staticClass:"padding-lr"},[t("v-uni-view",{staticClass:"text-bold",staticStyle:{"font-size":"44upx",margin:"42upx 5upx 0upx 0upx"}},[n._v("请选择填写的认证类型")]),t("v-uni-view",{staticStyle:{"font-size":"24upx",color:"#999999"}},[n._v("请选择您要填写编辑的认证类型")]),t("v-uni-view",{staticStyle:{"margin-top":"120upx"}},[t("v-uni-view",{staticClass:"flex justify-between align-center box padding",on:{click:function(e){arguments[0]=e=n.$handleEvent(e),n.bindGeren()}}},[t("v-uni-view",{staticClass:"text-lg text-bold"},[n._v("实名认证")]),t("v-uni-view",[t("u-icon",{attrs:{name:"arrow-right",color:"#808080",size:"28"}})],1)],1),t("v-uni-view",{staticClass:"flex justify-between align-center box margin-top-sm padding",on:{click:function(e){arguments[0]=e=n.$handleEvent(e),n.bindQe()}}},[t("v-uni-view",{staticClass:"text-lg text-bold"},[n._v("学历认证")]),t("v-uni-view",[t("u-icon",{attrs:{name:"arrow-right",color:"#808080",size:"28"}})],1)],1)],1)],1)},u=[]},"50fe":function(n,e,t){"use strict";var i=t("bdf3"),a=t.n(i);a.a},"55e9":function(n,e,t){"use strict";t.r(e);var i=t("cfc3"),a=t.n(i);for(var u in i)["default"].indexOf(u)<0&&function(n){t.d(e,n,(function(){return i[n]}))}(u);e["default"]=a.a},"8f28":function(n,e,t){"use strict";t.r(e);var i=t("3c41"),a=t("55e9");for(var u in a)["default"].indexOf(u)<0&&function(n){t.d(e,n,(function(){return a[n]}))}(u);t("50fe");var o=t("828b"),r=Object(o["a"])(a["default"],i["b"],i["c"],!1,null,"5b32e65d",null,!1,i["a"],void 0);e["default"]=r.exports},b0b4:function(n,e,t){var i=t("c86c");e=i(!1),e.push([n.i,"uni-page-body[data-v-5b32e65d]{background-color:#fff}body.?%PAGE?%[data-v-5b32e65d]{background-color:#fff}.box[data-v-5b32e65d]{width:%?670?%;height:%?120?%;background:#f2f2f2;border-radius:%?16?%}",""]),n.exports=e},bdf3:function(n,e,t){var i=t("b0b4");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[n.i,i,""]]),i.locals&&(n.exports=i.locals);var a=t("967d").default;a("761550e6",i,!0,{sourceMap:!1,shadowMode:!1})},cfc3:function(n,e,t){"use strict";t("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={data:function(){return{}},methods:{bindGeren:function(){uni.navigateTo({url:"/my/renzheng/index"})},bindQe:function(){uni.navigateTo({url:"/my/renzheng/xueli"})}}};e.default=i}}]);