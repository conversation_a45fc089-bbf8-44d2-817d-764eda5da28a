(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["my-Events-detail~pages-my-invitationUser"],{"08d7":function(t,e,n){"use strict";n.r(e);var i=n("383b"),r=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=r.a},1497:function(t,e,n){"use strict";n.r(e);var i=n("b3cf"),r=n("08d7");for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);n("3009");var a=n("828b"),u=Object(a["a"])(r["default"],i["b"],i["c"],!1,null,"8ae7e908",null,!1,i["a"],void 0);e["default"]=u.exports},1851:function(t,e,n){"use strict";var i=n("8bdb"),r=n("84d6"),o=n("1cb5");i({target:"Array",proto:!0},{fill:r}),o("fill")},2711:function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=i(n("fcf3"));n("aa9c"),n("7a76"),n("c9b5"),n("64aa"),n("1851"),n("d4b5");var o={};(function(){function t(t){var e,n,i;return t<128?[t]:t<2048?(e=192+(t>>6),n=128+(63&t),[e,n]):(e=224+(t>>12),n=128+(t>>6&63),i=128+(63&t),[e,n,i])}function e(e,n){this.typeNumber=-1,this.errorCorrectLevel=n,this.modules=null,this.moduleCount=0,this.dataCache=null,this.rsBlocks=null,this.totalDataCount=-1,this.data=e,this.utf8bytes=function(e){for(var n=[],i=0;i<e.length;i++)for(var r=e.charCodeAt(i),o=t(r),a=0;a<o.length;a++)n.push(o[a]);return n}(e),this.make()}e.prototype={constructor:e,getModuleCount:function(){return this.moduleCount},make:function(){this.getRightType(),this.dataCache=this.createData(),this.createQrcode()},makeImpl:function(t){this.moduleCount=4*this.typeNumber+17,this.modules=new Array(this.moduleCount);for(var e=0;e<this.moduleCount;e++)this.modules[e]=new Array(this.moduleCount);this.setupPositionProbePattern(0,0),this.setupPositionProbePattern(this.moduleCount-7,0),this.setupPositionProbePattern(0,this.moduleCount-7),this.setupPositionAdjustPattern(),this.setupTimingPattern(),this.setupTypeInfo(!0,t),this.typeNumber>=7&&this.setupTypeNumber(!0),this.mapData(this.dataCache,t)},setupPositionProbePattern:function(t,e){for(var n=-1;n<=7;n++)if(!(t+n<=-1||this.moduleCount<=t+n))for(var i=-1;i<=7;i++)e+i<=-1||this.moduleCount<=e+i||(this.modules[t+n][e+i]=0<=n&&n<=6&&(0==i||6==i)||0<=i&&i<=6&&(0==n||6==n)||2<=n&&n<=4&&2<=i&&i<=4)},createQrcode:function(){for(var t=0,e=0,n=null,i=0;i<8;i++){this.makeImpl(i);var r=a.getLostPoint(this);(0==i||t>r)&&(t=r,e=i,n=this.modules)}this.modules=n,this.setupTypeInfo(!1,e),this.typeNumber>=7&&this.setupTypeNumber(!1)},setupTimingPattern:function(){for(var t=8;t<this.moduleCount-8;t++)null==this.modules[t][6]&&(this.modules[t][6]=t%2==0,null==this.modules[6][t]&&(this.modules[6][t]=t%2==0))},setupPositionAdjustPattern:function(){for(var t=a.getPatternPosition(this.typeNumber),e=0;e<t.length;e++)for(var n=0;n<t.length;n++){var i=t[e],r=t[n];if(null==this.modules[i][r])for(var o=-2;o<=2;o++)for(var u=-2;u<=2;u++)this.modules[i+o][r+u]=-2==o||2==o||-2==u||2==u||0==o&&0==u}},setupTypeNumber:function(t){for(var e=a.getBCHTypeNumber(this.typeNumber),n=0;n<18;n++){var i=!t&&1==(e>>n&1);this.modules[Math.floor(n/3)][n%3+this.moduleCount-8-3]=i,this.modules[n%3+this.moduleCount-8-3][Math.floor(n/3)]=i}},setupTypeInfo:function(t,e){for(var i=n[this.errorCorrectLevel]<<3|e,r=a.getBCHTypeInfo(i),o=0;o<15;o++){var u=!t&&1==(r>>o&1);o<6?this.modules[o][8]=u:o<8?this.modules[o+1][8]=u:this.modules[this.moduleCount-15+o][8]=u;u=!t&&1==(r>>o&1);o<8?this.modules[8][this.moduleCount-o-1]=u:o<9?this.modules[8][15-o-1+1]=u:this.modules[8][15-o-1]=u}this.modules[this.moduleCount-8][8]=!t},createData:function(){var t=new f,n=this.typeNumber>9?16:8;t.put(4,4),t.put(this.utf8bytes.length,n);for(var i=0,r=this.utf8bytes.length;i<r;i++)t.put(this.utf8bytes[i],8);t.length+4<=8*this.totalDataCount&&t.put(0,4);while(t.length%8!=0)t.putBit(!1);while(1){if(t.length>=8*this.totalDataCount)break;if(t.put(e.PAD0,8),t.length>=8*this.totalDataCount)break;t.put(e.PAD1,8)}return this.createBytes(t)},createBytes:function(t){for(var e=0,n=0,i=0,r=this.rsBlock.length/3,o=new Array,u=0;u<r;u++)for(var s=this.rsBlock[3*u+0],c=this.rsBlock[3*u+1],f=this.rsBlock[3*u+2],d=0;d<s;d++)o.push([f,c]);for(var h=new Array(o.length),g=new Array(o.length),p=0;p<o.length;p++){var m=o[p][0],v=o[p][1]-m;n=Math.max(n,m),i=Math.max(i,v),h[p]=new Array(m);for(u=0;u<h[p].length;u++)h[p][u]=255&t.buffer[u+e];e+=m;var x=a.getErrorCorrectPolynomial(v),T=new l(h[p],x.getLength()-1),y=T.mod(x);g[p]=new Array(x.getLength()-1);for(u=0;u<g[p].length;u++){var b=u+y.getLength()-g[p].length;g[p][u]=b>=0?y.get(b):0}}var w=new Array(this.totalDataCount),C=0;for(u=0;u<n;u++)for(p=0;p<o.length;p++)u<h[p].length&&(w[C++]=h[p][u]);for(u=0;u<i;u++)for(p=0;p<o.length;p++)u<g[p].length&&(w[C++]=g[p][u]);return w},mapData:function(t,e){for(var n=-1,i=this.moduleCount-1,r=7,o=0,u=this.moduleCount-1;u>0;u-=2){6==u&&u--;while(1){for(var s=0;s<2;s++)if(null==this.modules[i][u-s]){var l=!1;o<t.length&&(l=1==(t[o]>>>r&1));var c=a.getMask(e,i,u-s);c&&(l=!l),this.modules[i][u-s]=l,r--,-1==r&&(o++,r=7)}if(i+=n,i<0||this.moduleCount<=i){i-=n,n=-n;break}}}}},e.PAD0=236,e.PAD1=17;for(var n=[1,0,3,2],i={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7},a={PATTERN_POSITION_TABLE:[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],G15:1335,G18:7973,G15_MASK:21522,getBCHTypeInfo:function(t){var e=t<<10;while(a.getBCHDigit(e)-a.getBCHDigit(a.G15)>=0)e^=a.G15<<a.getBCHDigit(e)-a.getBCHDigit(a.G15);return(t<<10|e)^a.G15_MASK},getBCHTypeNumber:function(t){var e=t<<12;while(a.getBCHDigit(e)-a.getBCHDigit(a.G18)>=0)e^=a.G18<<a.getBCHDigit(e)-a.getBCHDigit(a.G18);return t<<12|e},getBCHDigit:function(t){var e=0;while(0!=t)e++,t>>>=1;return e},getPatternPosition:function(t){return a.PATTERN_POSITION_TABLE[t-1]},getMask:function(t,e,n){switch(t){case i.PATTERN000:return(e+n)%2==0;case i.PATTERN001:return e%2==0;case i.PATTERN010:return n%3==0;case i.PATTERN011:return(e+n)%3==0;case i.PATTERN100:return(Math.floor(e/2)+Math.floor(n/3))%2==0;case i.PATTERN101:return e*n%2+e*n%3==0;case i.PATTERN110:return(e*n%2+e*n%3)%2==0;case i.PATTERN111:return(e*n%3+(e+n)%2)%2==0;default:throw new Error("bad maskPattern:"+t)}},getErrorCorrectPolynomial:function(t){for(var e=new l([1],0),n=0;n<t;n++)e=e.multiply(new l([1,u.gexp(n)],0));return e},getLostPoint:function(t){for(var e=t.getModuleCount(),n=0,i=0,r=0;r<e;r++)for(var o=0,a=t.modules[r][0],u=0;u<e;u++){var s=t.modules[r][u];if(u<e-6&&s&&!t.modules[r][u+1]&&t.modules[r][u+2]&&t.modules[r][u+3]&&t.modules[r][u+4]&&!t.modules[r][u+5]&&t.modules[r][u+6]&&(u<e-10?t.modules[r][u+7]&&t.modules[r][u+8]&&t.modules[r][u+9]&&t.modules[r][u+10]&&(n+=40):u>3&&t.modules[r][u-1]&&t.modules[r][u-2]&&t.modules[r][u-3]&&t.modules[r][u-4]&&(n+=40)),r<e-1&&u<e-1){var l=0;s&&l++,t.modules[r+1][u]&&l++,t.modules[r][u+1]&&l++,t.modules[r+1][u+1]&&l++,0!=l&&4!=l||(n+=3)}a^s?o++:(a=s,o>=5&&(n+=3+o-5),o=1),s&&i++}for(u=0;u<e;u++)for(o=0,a=t.modules[0][u],r=0;r<e;r++){s=t.modules[r][u];r<e-6&&s&&!t.modules[r+1][u]&&t.modules[r+2][u]&&t.modules[r+3][u]&&t.modules[r+4][u]&&!t.modules[r+5][u]&&t.modules[r+6][u]&&(r<e-10?t.modules[r+7][u]&&t.modules[r+8][u]&&t.modules[r+9][u]&&t.modules[r+10][u]&&(n+=40):r>3&&t.modules[r-1][u]&&t.modules[r-2][u]&&t.modules[r-3][u]&&t.modules[r-4][u]&&(n+=40)),a^s?o++:(a=s,o>=5&&(n+=3+o-5),o=1)}var c=Math.abs(100*i/e/e-50)/5;return n+=10*c,n}},u={glog:function(t){if(t<1)throw new Error("glog("+t+")");return u.LOG_TABLE[t]},gexp:function(t){while(t<0)t+=255;while(t>=256)t-=255;return u.EXP_TABLE[t]},EXP_TABLE:new Array(256),LOG_TABLE:new Array(256)},s=0;s<8;s++)u.EXP_TABLE[s]=1<<s;for(s=8;s<256;s++)u.EXP_TABLE[s]=u.EXP_TABLE[s-4]^u.EXP_TABLE[s-5]^u.EXP_TABLE[s-6]^u.EXP_TABLE[s-8];for(s=0;s<255;s++)u.LOG_TABLE[u.EXP_TABLE[s]]=s;function l(t,e){if(void 0==t.length)throw new Error(t.length+"/"+e);var n=0;while(n<t.length&&0==t[n])n++;this.num=new Array(t.length-n+e);for(var i=0;i<t.length-n;i++)this.num[i]=t[i+n]}l.prototype={get:function(t){return this.num[t]},getLength:function(){return this.num.length},multiply:function(t){for(var e=new Array(this.getLength()+t.getLength()-1),n=0;n<this.getLength();n++)for(var i=0;i<t.getLength();i++)e[n+i]^=u.gexp(u.glog(this.get(n))+u.glog(t.get(i)));return new l(e,0)},mod:function(t){var e=this.getLength(),n=t.getLength();if(e-n<0)return this;for(var i=new Array(e),r=0;r<e;r++)i[r]=this.get(r);while(i.length>=n){var o=u.glog(i[0])-u.glog(t.get(0));for(r=0;r<t.getLength();r++)i[r]^=u.gexp(u.glog(t.get(r))+o);while(0==i[0])i.shift()}return new l(i,0)}};var c=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]];function f(){this.buffer=new Array,this.length=0}e.prototype.getRightType=function(){for(var t=1;t<41;t++){var e=c[4*(t-1)+this.errorCorrectLevel];if(void 0==e)throw new Error("bad rs block @ typeNumber:"+t+"/errorCorrectLevel:"+this.errorCorrectLevel);for(var n=e.length/3,i=0,r=0;r<n;r++){var o=e[3*r+0],a=e[3*r+2];i+=a*o}var u=t>9?2:1;if(this.utf8bytes.length+u<i||40==t){this.typeNumber=t,this.rsBlock=e,this.totalDataCount=i;break}}},f.prototype={get:function(t){var e=Math.floor(t/8);return this.buffer[e]>>>7-t%8&1},put:function(t,e){for(var n=0;n<e;n++)this.putBit(t>>>e-n-1&1)},putBit:function(t){var e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),t&&(this.buffer[e]|=128>>>this.length%8),this.length++}};var d=[];o=function(t){if(this.options={text:"",size:256,correctLevel:3,background:"#ffffff",foreground:"#000000",pdground:"#000000",image:"",imageSize:30,canvasId:t.canvasId,context:t.context,usingComponents:t.usingComponents,showLoading:t.showLoading,loadingText:t.loadingText},"string"===typeof t&&(t={text:t}),t)for(var n in t)this.options[n]=t[n];for(var i=null,o=(n=0,d.length);n<o;n++)if(d[n].text==this.options.text&&d[n].text.correctLevel==this.options.correctLevel){i=d[n].obj;break}n==o&&(i=new e(this.options.text,this.options.correctLevel),d.push({text:this.options.text,correctLevel:this.options.correctLevel,obj:i}));var a=function(t){var e=t.options;return e.pdground&&(t.row>1&&t.row<5&&t.col>1&&t.col<5||t.row>t.count-6&&t.row<t.count-2&&t.col>1&&t.col<5||t.row>1&&t.row<5&&t.col>t.count-6&&t.col<t.count-2)?e.pdground:e.foreground};(function(t){t.showLoading&&uni.showLoading({title:t.loadingText,mask:!0});for(var e=uni.createCanvasContext(t.canvasId,t.context),n=i.getModuleCount(),r=t.size,o=t.imageSize,s=(r/n).toPrecision(4),l=(r/n).toPrecision(4),c=0;c<n;c++)for(var f=0;f<n;f++){var d=Math.ceil((f+1)*s)-Math.floor(f*s),h=Math.ceil((c+1)*s)-Math.floor(c*s),g=a({row:c,col:f,count:n,options:t});e.setFillStyle(i.modules[c][f]?g:t.background),e.fillRect(Math.round(f*s),Math.round(c*l),d,h)}if(t.image){var p=Number(((r-o)/2).toFixed(2)),m=Number(((r-o)/2).toFixed(2));(function(e,n,i,r,o,a,u,s,l){e.setLineWidth(u),e.setFillStyle(t.background),e.setStrokeStyle(t.background),e.beginPath(),e.moveTo(n+a,i),e.arcTo(n+r,i,n+r,i+a,a),e.arcTo(n+r,i+o,n+r-a,i+o,a),e.arcTo(n,i+o,n,i+o-a,a),e.arcTo(n,i,n+a,i,a),e.closePath(),s&&e.fill(),l&&e.stroke()})(e,p,m,o,o,2,6,!0,!0),e.drawImage(t.image,p,m,o,o)}setTimeout((function(){e.draw(!0,(function(){setTimeout((function(){uni.canvasToTempFilePath({width:t.width,height:t.height,destWidth:t.width,destHeight:t.height,canvasId:t.canvasId,quality:Number(1),success:function(e){t.cbResult&&(u(e.tempFilePath)?u(e.apFilePath)?t.cbResult(e.tempFilePath):t.cbResult(e.apFilePath):t.cbResult(e.tempFilePath))},fail:function(e){t.cbResult&&t.cbResult(e)},complete:function(){uni.hideLoading()}},t.context)}),t.text.length+100)}))}),t.usingComponents?0:150)})(this.options);var u=function(t){var e=(0,r.default)(t),n=!1;return"number"==e&&""==String(t)||"undefined"==e?n=!0:"object"==e?"{}"!=JSON.stringify(t)&&"[]"!=JSON.stringify(t)&&null!=t||(n=!0):"string"==e?""!=t&&"undefined"!=t&&"null"!=t&&"{}"!=t&&"[]"!=t||(n=!0):"function"==e&&(n=!1),n}},o.prototype.clear=function(t){var e=uni.createCanvasContext(this.options.canvasId,this.options.context);e.clearRect(0,0,this.options.size,this.options.size),e.draw(!1,(function(){t&&t()}))}})();var a=o;e.default=a},3009:function(t,e,n){"use strict";var i=n("6cbd"),r=n.n(i);r.a},"383b":function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=i(n("fcf3"));n("64aa"),n("d4b5");var o,a=i(n("2711")),u={name:"tki-qrcode",props:{cid:{type:String,default:"tki-qrcode-canvas"},size:{type:Number,default:200},unit:{type:String,default:"upx"},show:{type:Boolean,default:!0},val:{type:String,default:""},background:{type:String,default:"#ffffff"},foreground:{type:String,default:"#000000"},pdground:{type:String,default:"#000000"},icon:{type:String,default:""},iconSize:{type:Number,default:40},lv:{type:Number,default:3},onval:{type:Boolean,default:!1},loadMake:{type:Boolean,default:!1},usingComponents:{type:Boolean,default:!0},showLoading:{type:Boolean,default:!0},loadingText:{type:String,default:"二维码生成中"}},data:function(){return{result:""}},methods:{_makeCode:function(){var t=this;this._empty(this.val)?uni.showToast({title:"二维码内容不能为空",icon:"none",duration:2e3}):o=new a.default({context:t,canvasId:t.cid,usingComponents:t.usingComponents,loadingText:t.loadingText,text:t.val,size:t.cpSize,background:t.background,foreground:t.foreground,pdground:t.pdground,correctLevel:t.lv,image:t.icon,imageSize:t.iconSize,cbResult:function(e){t._result(e)}})},_clearCode:function(){this._result(""),o.clear()},_saveCode:function(){""!=this.result&&uni.saveImageToPhotosAlbum({filePath:this.result,success:function(){uni.showToast({title:"二维码保存成功",icon:"success",duration:2e3})}})},_result:function(t){this.result=t,this.$emit("result",t)},_empty:function(t){var e=(0,r.default)(t),n=!1;return"number"==e&&""==String(t)||"undefined"==e?n=!0:"object"==e?"{}"!=JSON.stringify(t)&&"[]"!=JSON.stringify(t)&&null!=t||(n=!0):"string"==e?""!=t&&"undefined"!=t&&"null"!=t&&"{}"!=t&&"[]"!=t||(n=!0):"function"==e&&(n=!1),n}},watch:{size:function(t,e){var n=this;t==e||this._empty(t)||(this.cSize=t,this._empty(this.val)||setTimeout((function(){n._makeCode()}),100))},val:function(t,e){var n=this;this.onval&&(t==e||this._empty(t)||setTimeout((function(){n._makeCode()}),0))}},computed:{cpSize:function(){return"upx"==this.unit?uni.upx2px(this.size):this.size}},mounted:function(){var t=this;this.loadMake&&(this._empty(this.val)||setTimeout((function(){t._makeCode()}),0))}};e.default=u},"558b":function(t,e,n){"use strict";n.r(e);var i=n("84ba"),r=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=r.a},6079:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticStyle:{background:"#FFFFFF"}},[t.tempFilePath?n("v-uni-image",{staticClass:"is-response",attrs:{"lazy-load":!0,src:t.tempFilePath,mode:"widthFix"},on:{longpress:function(e){arguments[0]=e=t.$handleEvent(e),t.toSave(t.tempFilePath)}}}):n("v-uni-canvas",{style:{width:t.canvasW+"px",height:t.canvasH+"px"},attrs:{"canvas-id":t.CanvasID}})],1)},r=[]},"6cbd":function(t,e,n){var i=n("7ec8");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var r=n("967d").default;r("2934e820",i,!0,{sourceMap:!1,shadowMode:!1})},"7ec8":function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,".tki-qrcode[data-v-8ae7e908]{position:relative}.tki-qrcode-canvas[data-v-8ae7e908]{position:fixed;top:%?-99999?%;left:%?-99999?%;z-index:-99999}",""]),t.exports=e},"84ba":function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("bf0f");var r,o=i(n("2634")),a=i(n("2fdc")),u={name:"wm-poster",props:{CanvasID:{Type:String,default:"PosterCanvas"},imgSrc:{Type:String,default:""},QrSrc:{Type:String,default:""},Title:{Type:String,default:""},TitleColor:{Type:String,default:"#000000"},LineType:{Type:[String,Boolean],default:!0},PriceTxt:{Type:String,default:""},PriceColor:{Type:String,default:"#e31d1a"},OriginalTxt:{Type:String,default:""},OriginalColor:{Type:String,default:"#b8b8b8"},Width:{Type:String,default:700},CanvasBg:{Type:String,default:"#ffffff"},Referrer:{Type:String,default:""},ViewDetails:{Type:String,default:"长按或扫描识别二维码"}},data:function(){return{loading:!1,tempFilePath:"",canvasW:0,canvasH:0,canvasImgSrc:"",ctx:null}},methods:{toSave:function(t){},OnCanvas:function(){var t=this;return(0,a.default)((0,o.default)().mark((function e(){var n,i,a,u,s,l,c,f,d,h,g,p,m;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.loading=!0,r.ctx=uni.createCanvasContext(r.CanvasID,t),n=uni.upx2px(r.Width),i=uni.upx2px(30),a=uni.upx2px(150),u=0,e.next=6,r.getImageInfo({imgSrc:r.imgSrc});case 6:return s=e.sent,e.next=9,r.getImageInfo({imgSrc:r.QrSrc});case 9:l=e.sent,c=[s.width,s.height],f=[l.width,l.height],d=n-2*i-8,c[0]!=d&&(c[1]=Math.floor(d/c[0]*c[1]),c[0]=d),f[0]!=a&&(f[1]=Math.floor(a/f[0]*f[1]),f[0]=a),r.canvasW=n,r.canvasH=c[1]+f[1]+68,r.ctx.setFillStyle(r.CanvasBg),r.ctx.fillRect(0,0,n,r.canvasH),r.ctx.drawImage(s.path,i,i,c[0],c[1]),r.ctx.setFontSize(uni.upx2px(32)),r.ctx.setFillStyle(r.TitleColor),h=0,g=c[1]+2*i+10,p=1,m=0;case 26:if(!(m<r.Title.length)){e.next=48;break}if(u+=r.ctx.measureText(r.Title[m]).width,!(u>c[0])){e.next=44;break}if(2!=p||!r.LineType){e.next=37;break}return r.ctx.fillText(r.Title.substring(h,m-8)+"...",i,g),u=0,h=m,p++,e.abrupt("break",48);case 37:r.ctx.fillText(r.Title.substring(h,m),i,g),u=0,g+=20,h=m,p++;case 42:e.next=45;break;case 44:m==r.Title.length-1&&(r.ctx.fillText(r.Title.substring(h,m+1),i,g),u=0);case 45:m++,e.next=26;break;case 48:g+=uni.upx2px(20),r.ctx.drawImage(l.path,c[0]-f[0]+i,g,f[0],f[1]),r.ctx.setFillStyle(r.TitleColor),r.ctx.setFontSize(uni.upx2px(30)),r.ctx.fillText(r.Referrer,i,g+f[1]/2),r.ctx.setFillStyle(r.OriginalColor),r.ctx.setFontSize(uni.upx2px(24)),r.ctx.fillText(r.ViewDetails,i,g+f[1]/2+20),setTimeout((function(){r.ctx.draw(!0,(function(t){r.getNewImage()}))}),600);case 57:case"end":return e.stop()}}),e)})))()},getImageInfo:function(t){return(0,a.default)((0,o.default)().mark((function e(){var n;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=t.imgSrc,e.abrupt("return",new Promise((function(t,e){uni.getImageInfo({src:n,success:function(e){t(e)},fail:function(t){e(t),r.$queue.showToast("海报生成失败"),uni.hideLoading()}})})));case 2:case"end":return e.stop()}}),e)})))()},getNewImage:function(){uni.canvasToTempFilePath({canvasId:r.CanvasID,quality:1,complete:function(t){r.tempFilePath=t.tempFilePath,r.$emit("success",t),r.loading=!1,r.$queue.showToast("长按图片保存海报"),uni.hideLoading()}},this)}},mounted:function(){r=this,this.OnCanvas()}};e.default=u},b134:function(t,e,n){"use strict";n.r(e);var i=n("d63f"),r=n("e318");for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);var a=n("828b"),u=Object(a["a"])(r["default"],i["b"],i["c"],!1,null,"8d3efd5a",null,!1,i["a"],void 0);e["default"]=u.exports},b3cf:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticClass:"tki-qrcode"},[e("v-uni-canvas",{staticClass:"tki-qrcode-canvas",style:{width:this.cpSize+"px",height:this.cpSize+"px"},attrs:{"canvas-id":this.cid}}),e("v-uni-image",{directives:[{name:"show",rawName:"v-show",value:this.show,expression:"show"}],style:{width:this.cpSize+"px",height:this.cpSize+"px"},attrs:{src:this.result}})],1)},r=[]},b929:function(t,e,n){"use strict";n.r(e);var i=n("6079"),r=n("558b");for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);var a=n("828b"),u=Object(a["a"])(r["default"],i["b"],i["c"],!1,null,"24604135",null,!1,i["a"],void 0);e["default"]=u.exports},d63f:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticStyle:{background:"#FFFFFF"}},[t.tempFilePath?n("v-uni-image",{staticClass:"is-response",attrs:{"lazy-load":!0,src:t.tempFilePath,mode:"widthFix"},on:{longpress:function(e){arguments[0]=e=t.$handleEvent(e),t.toSave(t.tempFilePath)}}}):n("v-uni-canvas",{style:{width:t.canvasW+"px",height:t.canvasH+"px"},attrs:{"canvas-id":t.CanvasID}})],1)},r=[]},e318:function(t,e,n){"use strict";n.r(e);var i=n("e646"),r=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=r.a},e646:function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("bf0f");var r,o=i(n("2634")),a=i(n("2fdc")),u={name:"wm-poster",props:{CanvasID:{Type:String,default:"PosterCanvas"},imgSrc:{Type:String,default:""},QrSrc:{Type:String,default:""},Title:{Type:String,default:"相亲"},TitleColor:{Type:String,default:"#000000"},LineType:{Type:[String,Boolean],default:!0},PriceTxt:{Type:String,default:""},PriceColor:{Type:String,default:"#e31d1a"},OriginalTxt:{Type:String,default:""},OriginalColor:{Type:String,default:"#b8b8b8"},Width:{Type:String,default:700},CanvasBg:{Type:String,default:"#ffffff"},Referrer:{Type:String,default:"相亲精选好物"},ViewDetails:{Type:String,default:"长按或扫描识别二维码领券"}},data:function(){return{loading:!1,tempFilePath:"",canvasW:0,canvasH:0,canvasImgSrc:"",ctx:null}},methods:{toSave:function(t){},OnCanvas:function(){var t=this;return(0,a.default)((0,o.default)().mark((function e(){var n,i,a,u,s,l,c,f,d,h,g,p,m;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.loading=!0,r.ctx=uni.createCanvasContext(r.CanvasID,t),n=uni.upx2px(r.Width),i=uni.upx2px(30),a=uni.upx2px(150),u=0,e.next=6,r.getImageInfo({imgSrc:r.imgSrc});case 6:return s=e.sent,e.next=9,r.getImageInfo({imgSrc:r.QrSrc});case 9:l=e.sent,c=[s.width,s.height],f=[l.width,l.height],d=n-2*i,c[0]!=d&&(c[1]=Math.floor(d/c[0]*c[1]),c[0]=d),f[0]!=a&&(f[1]=Math.floor(a/f[0]*f[1]),f[0]=a),r.canvasW=n,r.canvasH=c[1]+f[1]+128,r.ctx.setFillStyle(r.CanvasBg),r.ctx.fillRect(0,0,n,r.canvasH),r.ctx.drawImage(s.path,i,i,c[0],c[1]),r.ctx.setFontSize(uni.upx2px(32)),r.ctx.setFillStyle(r.TitleColor),h=0,g=c[1]+2*i+10,p=1,m=0;case 26:if(!(m<r.Title.length)){e.next=48;break}if(u+=r.ctx.measureText(r.Title[m]).width,!(u>c[0])){e.next=44;break}if(2!=p||!r.LineType){e.next=37;break}return r.ctx.fillText(r.Title.substring(h,m-8)+"...",i,g),u=0,h=m,p++,e.abrupt("break",48);case 37:r.ctx.fillText(r.Title.substring(h,m),i,g),u=0,g+=20,h=m,p++;case 42:e.next=45;break;case 44:m==r.Title.length-1&&(r.ctx.fillText(r.Title.substring(h,m+1),i,g),u=0);case 45:m++,e.next=26;break;case 48:u=i,g+=uni.upx2px(60),1==p&&(g+=20),""!=r.PriceTxt&&(r.ctx.setFillStyle(r.PriceColor),r.ctx.setFontSize(uni.upx2px(38)),r.ctx.fillText("券后价 ￥"+r.PriceTxt,u,g),u+=r.ctx.measureText("券后价 ￥"+r.PriceTxt).width+uni.upx2px(10)),""!=r.PriceTxt&&""!=r.OriginalTxt&&(r.ctx.setFillStyle(r.OriginalColor),r.ctx.setFontSize(uni.upx2px(24)),r.ctx.fillText(r.OriginalTxt,u,g)),r.ctx.strokeStyle=r.OriginalColor,r.ctx.moveTo(u,g-uni.upx2px(10)),r.ctx.lineTo(u+r.ctx.measureText(r.OriginalTxt).width,g-uni.upx2px(10)),r.ctx.stroke(),g+=uni.upx2px(20),r.ctx.drawImage(l.path,c[0]-f[0]+i,g,f[0],f[1]),r.ctx.setFillStyle(r.TitleColor),r.ctx.setFontSize(uni.upx2px(30)),r.ctx.fillText(r.Referrer,i,g+f[1]/2),r.ctx.setFillStyle(r.OriginalColor),r.ctx.setFontSize(uni.upx2px(24)),r.ctx.fillText(r.ViewDetails,i,g+f[1]/2+20),setTimeout((function(){r.ctx.draw(!0,(function(t){r.getNewImage()}))}),600);case 66:case"end":return e.stop()}}),e)})))()},getImageInfo:function(t){return(0,a.default)((0,o.default)().mark((function e(){var n;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=t.imgSrc,e.abrupt("return",new Promise((function(t,e){uni.getImageInfo({src:n,success:function(e){t(e)},fail:function(t){e(t),r.$queue.showToast("海报生成失败"),uni.hideLoading()}})})));case 2:case"end":return e.stop()}}),e)})))()},getNewImage:function(){uni.canvasToTempFilePath({canvasId:r.CanvasID,quality:1,complete:function(t){r.tempFilePath=t.tempFilePath,r.$emit("success",t),r.loading=!1,r.$queue.showToast("长按图片保存海报"),uni.hideLoading()}},this)}},mounted:function(){r=this,this.OnCanvas()}};e.default=u}}]);