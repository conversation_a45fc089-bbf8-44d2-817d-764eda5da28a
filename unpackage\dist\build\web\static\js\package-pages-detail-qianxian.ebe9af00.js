(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["package-pages-detail-qianxian"],{"0136":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return n}));var n={uNavbar:i("ddec").default},a=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{},[e("u-navbar",{attrs:{title:"红娘牵线","is-back":!0,"border-bottom":!1,background:this.background,"title-color":"#111224"}}),e("v-uni-view",{staticClass:"bgjb"}),e("v-uni-view",{staticClass:"cenoten"},[e("v-uni-view",{staticClass:"box"},[e("v-uni-view",{staticClass:"flex align-center justify-between"},[e("v-uni-view",{staticClass:"flex align-center"},[e("v-uni-image",{staticStyle:{width:"90rpx",height:"90rpx","border-radius":"55rpx"},attrs:{src:i("0eeb")}}),e("v-uni-view",{staticClass:"margin-left-xs"},[e("v-uni-view",{staticClass:"text-lg"},[this._v("昵称")]),e("v-uni-view",{staticClass:"margin-top-xs",staticStyle:{color:"#A6A6A6"}},[this._v("个人标签")])],1)],1),e("v-uni-view",{staticClass:"flex align-center"},[e("v-uni-view",[e("v-uni-image",{staticStyle:{width:"67rpx",height:"67rpx"},attrs:{src:i("d6de")}})],1),e("v-uni-view",{staticClass:"margin-left-sm"},[e("v-uni-image",{staticStyle:{width:"67rpx",height:"67rpx"},attrs:{src:i("1b31")}})],1)],1)],1),e("v-uni-view",{staticClass:"remkbg"},[e("v-uni-view",{staticClass:"triangle"}),e("v-uni-view",{staticClass:"tit"},[this._v("TA的成功按例")]),e("v-uni-view",{staticClass:"coyt"},[e("v-uni-text"),this._v("恭喜会员502*45与410*55牵线成功！")],1)],1)],1)],1)],1)},r=[]},"0eeb":function(t,e,i){t.exports=i.p+"static/logo.png"},"1b31":function(t,e,i){t.exports=i.p+"package/pages/static/wx.png"},2189:function(t,e,i){t.exports=i.p+"static/images/bgImg.png"},2838:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return n}));var n={uIcon:i("3688").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{},[i("v-uni-view",{staticClass:"u-navbar",class:{"u-navbar-fixed":t.isFixed,"u-border-bottom":t.borderBottom},style:[t.navbarStyle]},[i("v-uni-view",{staticClass:"u-status-bar",style:{height:t.statusBarHeight+"px"}}),i("v-uni-view",{staticClass:"u-navbar-inner",style:[t.navbarInnerStyle]},[t.isBack?i("v-uni-view",{staticClass:"u-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"u-icon-wrap"},[i("u-icon",{attrs:{name:t.backIconName,color:t.backIconColor,size:t.backIconSize}})],1),t.backText?i("v-uni-view",{staticClass:"u-icon-wrap u-back-text u-line-1",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()],1):t._e(),t.title?i("v-uni-view",{staticClass:"u-navbar-content-title",style:[t.titleStyle]},[i("v-uni-view",{staticClass:"u-title u-line-1",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),i("v-uni-view",{staticClass:"u-slot-content"},[t._t("default")],2),i("v-uni-view",{staticClass:"u-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?i("v-uni-view",{staticClass:"u-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+t.statusBarHeight+"px"}}):t._e()],1)},r=[]},"2ec5":function(t,e,i){"use strict";t.exports=function(t,e){return e||(e={}),t=t&&t.__esModule?t.default:t,"string"!==typeof t?t:(/^['"].*['"]$/.test(t)&&(t=t.slice(1,-1)),e.hash&&(t+=e.hash),/["'() \t\n]/.test(t)||e.needQuotes?'"'.concat(t.replace(/"/g,'\\"').replace(/\n/g,"\\n"),'"'):t)}},"2eef":function(t,e,i){"use strict";var n=i("50b0"),a=i.n(n);a.a},"320c":function(t,e,i){"use strict";i.r(e);var n=i("c4d4"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},"50b0":function(t,e,i){var n=i("630f");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("18d454d8",n,!0,{sourceMap:!1,shadowMode:!1})},"5d1b":function(t,e,i){"use strict";i.r(e);var n=i("0136"),a=i("cd3e");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);i("2eef");var s=i("828b"),u=Object(s["a"])(a["default"],n["b"],n["c"],!1,null,"268e7bdf",null,!1,n["a"],void 0);e["default"]=u.exports},"630f":function(t,e,i){var n=i("c86c"),a=i("2ec5"),r=i("2189");e=n(!1);var s=a(r);e.push([t.i,"uni-page-body[data-v-268e7bdf]{background:#f8fafc}body.?%PAGE?%[data-v-268e7bdf]{background:#f8fafc}.bgjb[data-v-268e7bdf]{width:100%;height:%?531?%;background-image:url("+s+");background-size:100% 100%;position:fixed;top:%?80?%;left:0;right:0;z-index:0}.cenoten[data-v-268e7bdf]{position:relative;z-index:99}.box[data-v-268e7bdf]{margin:%?25?% %?30?%;background:#fff;border-radius:%?24?%;padding:%?30?%}.remkbg[data-v-268e7bdf]{background-color:#f8f8f8;border-radius:%?24?%;padding:%?30?%;margin-top:%?25?%;position:relative}.remkbg .triangle[data-v-268e7bdf]{width:0;height:0;border-left:%?16?% solid transparent;border-right:%?16?% solid transparent;border-bottom:%?25?% solid #f8f8f8;position:absolute;top:%?-16?%;left:%?45?%}.remkbg .tit[data-v-268e7bdf]{font-size:%?30?%;font-weight:600}.remkbg .coyt[data-v-268e7bdf]{display:flex;align-items:center;margin-top:%?20?%}.remkbg .coyt uni-text[data-v-268e7bdf]{display:inline-flex;width:%?8?%;height:%?8?%;border-radius:%?88?%;background:#f99db5;margin-right:%?10?%}",""]),t.exports=e},a34e:function(t,e,i){var n=i("edc5");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("b709bde6",n,!0,{sourceMap:!1,shadowMode:!1})},c4d4:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa");var n=uni.getSystemInfoSync(),a={},r={name:"u-navbar",props:{height:{type:[String,Number],default:""},backIconColor:{type:String,default:"#606266"},backIconName:{type:String,default:"nav-back"},backIconSize:{type:[String,Number],default:"44"},backText:{type:String,default:""},backTextStyle:{type:Object,default:function(){return{color:"#606266"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleColor:{type:String,default:"#606266"},titleBold:{type:Boolean,default:!1},titleSize:{type:[String,Number],default:32},isBack:{type:[Boolean,String],default:!0},background:{type:Object,default:function(){return{background:"#ffffff"}}},isFixed:{type:Boolean,default:!0},immersive:{type:Boolean,default:!1},borderBottom:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},customBack:{type:Function,default:null}},data:function(){return{menuButtonInfo:a,statusBarHeight:n.statusBarHeight}},computed:{navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),t},titleStyle:function(){var t={};return t.left=(n.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(n.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44}},created:function(){},methods:{goBack:function(){"function"===typeof this.customBack?this.customBack.bind(this.$u.$parent.call(this))():uni.navigateBack()}}};e.default=r},cbe8:function(t,e,i){"use strict";var n=i("a34e"),a=i.n(n);a.a},cd3e:function(t,e,i){"use strict";i.r(e);var n=i("f39b"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},d6de:function(t,e,i){t.exports=i.p+"package/pages/static/phone.png"},ddec:function(t,e,i){"use strict";i.r(e);var n=i("2838"),a=i("320c");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);i("cbe8");var s=i("828b"),u=Object(s["a"])(a["default"],n["b"],n["c"],!1,null,"1d7f90d0",null,!1,n["a"],void 0);e["default"]=u.exports},edc5:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.u-navbar[data-v-1d7f90d0]{width:100%}.u-navbar-fixed[data-v-1d7f90d0]{position:fixed;left:0;right:0;top:0;z-index:991}.u-status-bar[data-v-1d7f90d0]{width:100%}.u-navbar-inner[data-v-1d7f90d0]{display:flex;flex-direction:row;justify-content:space-between;position:relative;align-items:center}.u-back-wrap[data-v-1d7f90d0]{display:flex;flex-direction:row;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.u-back-text[data-v-1d7f90d0]{padding-left:%?4?%;font-size:%?30?%}.u-navbar-content-title[data-v-1d7f90d0]{display:flex;flex-direction:row;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0}.u-navbar-centent-slot[data-v-1d7f90d0]{flex:1}.u-title[data-v-1d7f90d0]{line-height:%?60?%;font-size:%?32?%;flex:1}.u-navbar-right[data-v-1d7f90d0]{flex:1;display:flex;flex-direction:row;align-items:center;justify-content:flex-end}.u-slot-content[data-v-1d7f90d0]{flex:1;display:flex;flex-direction:row;align-items:center}',""]),t.exports=e},f39b:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={data:function(){return{background:{backgroundImage:"linear-gradient(90deg, #E4E7F8 0%, #F1E3F4 46%, #FDE1EF 100%)"}}}}}}]);