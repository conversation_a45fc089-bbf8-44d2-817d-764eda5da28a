(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["my-order-index"],{"0bca":function(t,e,A){"use strict";var a=A("36e5"),n=A.n(a);n.a},"36e5":function(t,e,A){var a=A("376a");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=A("967d").default;n("f3a519e6",a,!0,{sourceMap:!1,shadowMode:!1})},"376a":function(t,e,A){var a=A("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */\r\n/*\r\nsticky生效条件：\r\n1、父元素不能overflow:hidden或者overflow:auto属性。(mescroll-body设置:sticky="true"即可, mescroll-uni本身没有设置overflow)\r\n2、必须指定top、bottom、left、right4个值之一，否则只会处于相对定位\r\n3、父元素的高度不能低于sticky元素的高度\r\n4、sticky元素仅在其父元素内生效,所以父元素必须是 mescroll\r\n*/.sticky-tabs[data-v-73a520e8]{z-index:990;position:-webkit-sticky;position:sticky;top:var(--window-top)}.mescroll-uni .sticky-tabs[data-v-73a520e8],[data-v-73a520e8] .mescroll-uni .sticky-tabs{top:0}.demo-tip[data-v-73a520e8]{padding:%?18?%;font-size:%?24?%;text-align:center}',""]),t.exports=e},"3a86":function(t,e,A){"use strict";A.r(e);var a=A("d68d"),n=A("f058");for(var r in n)["default"].indexOf(r)<0&&function(t){A.d(e,t,(function(){return n[t]}))}(r);A("0bca");var s=A("828b"),o=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"73a520e8",null,!1,a["a"],void 0);e["default"]=o.exports},4688:function(t,e,A){"use strict";A.r(e);var a=A("89d2"),n=A.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){A.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},5371:function(t,e,A){var a=A("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.u-avatar[data-v-21bdd9ea]{display:inline-flex;align-items:center;justify-content:center;font-size:%?28?%;color:#606266;border-radius:10px;position:relative}.u-avatar__img[data-v-21bdd9ea]{width:100%;height:100%}.u-avatar__sex[data-v-21bdd9ea]{position:absolute;width:%?32?%;color:#fff;height:%?32?%;display:flex;flex-direction:row;justify-content:center;align-items:center;border-radius:%?100?%;top:5%;z-index:1;right:-7%;border:1px #fff solid}.u-avatar__sex--man[data-v-21bdd9ea]{background-color:#2979ff}.u-avatar__sex--woman[data-v-21bdd9ea]{background-color:#fa3534}.u-avatar__sex--none[data-v-21bdd9ea]{background-color:#f90}.u-avatar__level[data-v-21bdd9ea]{position:absolute;width:%?32?%;color:#fff;height:%?32?%;display:flex;flex-direction:row;justify-content:center;align-items:center;border-radius:%?100?%;bottom:5%;z-index:1;right:-7%;border:1px #fff solid;background-color:#f90}',""]),t.exports=e},"5c16":function(t,e,A){"use strict";var a=A("d487"),n=A.n(a);n.a},"667c":function(t,e,A){"use strict";A("6a54");var a=A("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,A("c223"),A("bf0f"),A("2797");var n=a(A("b7c7")),r=a(A("39d8")),s=a(A("3145")),o=a(A("7a77")),i=a(A("72e2")),c=a(A("2bdc")),l={mixins:[s.default],components:{mescrollBody:o.default,meTabs:i.default,empty:c.default},data:function(){var t;return{meShowModel:!1,meShowCancel:!0,meTitle:"提示",meContent:"",meConfirmText:"确认",meCancelText:"取消",meIndex:"",infoId:"",goods:[],tabs:[{title:"全部",status:""},{title:"待付款",status:"0"},{title:"待服务",status:"1"},{title:"进行中",status:"4"},{title:"已完成",status:"2"}],tabIndex:0,page:1,limit:10,userId:0,status:1,nickName:"",avatar:"",customStyle:{color:"#AC75FE",backgroundColor:"#FFFFFF",border:"50rpx",width:"180rpx",height:"65rpx",margin:"0 0 0 20rpx"},customStyle1:(t={color:"#999999",border:"1rpx solid #999999"},(0,r.default)(t,"border","50rpx"),(0,r.default)(t,"width","180rpx"),(0,r.default)(t,"height","65rpx"),(0,r.default)(t,"margin","0 0 0 20rpx"),t)}},onLoad:function(t){console.log(t),t.index?this.tabIndex=t.index:this.tabIndex=0,this.$queue.showLoading("加载中..."),this.userId=uni.getStorageSync("userId"),this.nickName=uni.getStorageSync("nickName")},onShow:function(){this.mescroll.resetUpScroll()},methods:{downCallback:function(){this.mescroll.resetUpScroll()},upCallback:function(t){var e=this,A=this.tabs[this.tabIndex].status,a={status:A,page:t.num,limit:t.size};this.$Request.get("/app/orders/selectMyOrder",a).then((function(A){e.mescroll.endBySize(A.data.list.length,A.data.totalCount),1==t.num&&(e.goods=[]),e.goods=[].concat((0,n.default)(e.goods),(0,n.default)(A.data.list)),e.goods.forEach((function(t){switch(t.state){case"0":t.statusName="待付款";break;case"1":t.statusName="待服务";break;case"4":t.statusName="进行中";break;case"2":t.statusName="已完成";break;case"3":t.statusName="已取消";break}})),e.mescroll.endSuccess(A.data.list.length),uni.hideLoading()})).catch((function(){e.mescroll.endErr()}))},tabChange:function(){this.goods=[],this.mescroll.resetUpScroll()},cancelOrder:function(t){this.meShowModel=!0,this.meTitle="提示",this.meContent="确认取消订单吗?",this.meIndex="m2",this.infoId=t.ordersId},cancel:function(t,e){var A="";4==e?A="确认完成订单吗?":3==e&&(A="确认取消订单吗?"),this.meShowModel=!0,this.meTitle="提示",this.meContent=A,this.meIndex="m3",this.infoId=t.ordersId},changeOrderState:function(t,e){var A=this,a={id:t,status:e};A.$Request.get("/app/orders/cancelOrder",a).then((function(t){0==t.code?A.mescroll.resetUpScroll():A.$queue.showToast(t.msg)}))},meHandleBtn:function(){var t=this;if("m1"==this.meIndex){var e={id:t.infoId};t.$Request.get("/app/orders/deleteOrder",e).then((function(e){0==e.code&&(uni.showToast({title:"删除成功"}),t.mescroll.resetUpScroll())}))}"m2"==this.meIndex&&t.$Request.get("/app/orders/queryOrders",{id:t.infoId}).then((function(e){if(0==e.code){var A=e.data;switch(A.state){case"0":t.changeOrderState(t.infoId,"3");break;case"1":t.changeOrderState(t.infoId,"3");break;case"2":t.$queue.showToast("订单已完成啦"),t.mescroll.resetUpScroll();break;case"3":t.$queue.showToast("订单已被取消"),t.mescroll.resetUpScroll();break}}})),"m3"==this.meIndex&&t.$Request.get("/app/orders/queryOrders",{id:t.infoId}).then((function(e){if(0==e.code){var A=e.data;switch(A.state){case"0":t.changeOrderState(t.infoId,"3");break;case"1":t.changeOrderState(t.infoId,"3");break;case"4":t.changeOrderState(t.infoId,"2");break;case"2":t.$queue.showToast("很抱歉订单已完成啦，无法操作"),t.mescroll.resetUpScroll();break;case"3":t.$queue.showToast("很抱歉订单已被取消，无法操作"),t.mescroll.resetUpScroll();break}}}))},meHandleClose:function(){this.meIndex},delOrder:function(t){this.meShowModel=!0,this.meTitle="提示",this.meContent="确定删除订单吗?",this.meIndex="m1",this.infoId=t.ordersId},clickItem:function(t){console.log("点击",t),uni.navigateTo({url:"/my/order/pay?id="+t.ordersId+"&isTrue=1"})},goMsg:function(t){var e=this;if(t.userId){var A={userId:this.userId,focusedUserId:t.ordersUserId};this.$Request.postJson("/app/chat/insertChatConversation",A).then((function(A){if(0==A.code){var a=e.userId==A.data.userId?A.data.focusedUserId:e.userId;uni.navigateTo({url:"/pages/msg/chat?chatConversationId="+A.data.chatConversationId+"&byUserId="+a+"&byNickName="+t.userName})}else e.$queue.showToast(A.msg)}))}else uni.showToast({title:"技能订单已下架",icon:"none"})},goNav:function(t){uni.navigateTo({url:t})}}};e.default=l},"822e":function(t,e,A){"use strict";A.r(e);var a=A("c436"),n=A("4688");for(var r in n)["default"].indexOf(r)<0&&function(t){A.d(e,t,(function(){return n[t]}))}(r);A("5c16");var s=A("828b"),o=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"21bdd9ea",null,!1,a["a"],void 0);e["default"]=o.exports},"89d2":function(t,e,A){"use strict";A("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,A("64aa");var a="data:image/jpg;base64,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",n={name:"u-avatar",props:{bgColor:{type:String,default:"transparent"},src:{type:String,default:""},size:{type:[String,Number],default:"default"},mode:{type:String,default:"circle"},text:{type:String,default:""},imgMode:{type:String,default:"aspectFill"},index:{type:[String,Number],default:""},sexIcon:{type:String,default:"man"},levelIcon:{type:String,default:"level"},levelBgColor:{type:String,default:""},sexBgColor:{type:String,default:""},showSex:{type:Boolean,default:!1},showLevel:{type:Boolean,default:!1}},data:function(){return{error:!1,avatar:this.src?this.src:a}},watch:{src:function(t){t?(this.avatar=t,this.error=!1):(this.avatar=a,this.error=!0)}},computed:{wrapStyle:function(){var t={};return t.height="large"==this.size?"120rpx":"default"==this.size?"90rpx":"mini"==this.size?"70rpx":this.size+"rpx",t.width=t.height,t.flex="0 0 ".concat(t.height),t.backgroundColor=this.bgColor,t.borderRadius="circle"==this.mode?"500px":"5px",this.text&&(t.padding="0 6rpx"),t},imgStyle:function(){var t={};return t.borderRadius="circle"==this.mode?"500px":"5px",t},uText:function(){return String(this.text)[0]},uSexStyle:function(){var t={};return this.sexBgColor&&(t.backgroundColor=this.sexBgColor),t},uLevelStyle:function(){var t={};return this.levelBgColor&&(t.backgroundColor=this.levelBgColor),t}},methods:{loadError:function(){this.error=!0,this.avatar=a},click:function(){this.$emit("click",this.index)}}};e.default=n},c436:function(t,e,A){"use strict";A.d(e,"b",(function(){return n})),A.d(e,"c",(function(){return r})),A.d(e,"a",(function(){return a}));var a={uIcon:A("3688").default},n=function(){var t=this,e=t.$createElement,A=t._self._c||e;return A("v-uni-view",{staticClass:"u-avatar",style:[t.wrapStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.click.apply(void 0,arguments)}}},[!t.uText&&t.avatar?A("v-uni-image",{staticClass:"u-avatar__img",style:[t.imgStyle],attrs:{src:t.avatar,mode:t.imgMode},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.loadError.apply(void 0,arguments)}}}):t.uText?A("v-uni-text",{staticClass:"u-line-1",style:{fontSize:"38rpx"}},[t._v(t._s(t.uText))]):t._t("default"),t.showSex?A("v-uni-view",{staticClass:"u-avatar__sex",class:["u-avatar__sex--"+t.sexIcon],style:[t.uSexStyle]},[A("u-icon",{attrs:{name:t.sexIcon,size:"20"}})],1):t._e(),t.showLevel?A("v-uni-view",{staticClass:"u-avatar__level",style:[t.uLevelStyle]},[A("u-icon",{attrs:{name:t.levelIcon,size:"20"}})],1):t._e()],2)},r=[]},d487:function(t,e,A){var a=A("5371");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=A("967d").default;n("61deeba9",a,!0,{sourceMap:!1,shadowMode:!1})},d68d:function(t,e,A){"use strict";A.d(e,"b",(function(){return n})),A.d(e,"c",(function(){return r})),A.d(e,"a",(function(){return a}));var a={uAvatar:A("822e").default,uButton:A("ddc5").default,uModal:A("7e01").default},n=function(){var t=this,e=t.$createElement,A=t._self._c||e;return A("v-uni-view",[A("v-uni-view",{staticClass:"sticky-tabs"},[A("me-tabs",{attrs:{nameKey:"title",tabs:t.tabs},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.tabChange.apply(void 0,arguments)}},model:{value:t.tabIndex,callback:function(e){t.tabIndex=e},expression:"tabIndex"}})],1),A("mescroll-body",{ref:"mescrollRef",attrs:{sticky:!0},on:{init:function(e){arguments[0]=e=t.$handleEvent(e),t.mescrollInit.apply(void 0,arguments)},down:function(e){arguments[0]=e=t.$handleEvent(e),t.downCallback.apply(void 0,arguments)},up:function(e){arguments[0]=e=t.$handleEvent(e),t.upCallback.apply(void 0,arguments)}}},[t._l(t.goods,(function(e,a){return t.goods.length>0?A("v-uni-view",{key:a,staticClass:"margin-sm padding bg ",staticStyle:{"border-radius":"24rpx"},on:{click:function(A){arguments[0]=A=t.$handleEvent(A),t.clickItem(e)}}},[A("v-uni-view",{staticClass:"flex justify-between"},[A("v-uni-view",{staticStyle:{color:"#AC75FE"}},[t._v(t._s(e.statusName))]),A("v-uni-view",{staticStyle:{color:"#999999"}},[t._v(t._s(e.updateTime))])],1),A("v-uni-view",{staticClass:" u-flex u-p-t-30"},[A("v-uni-view",{staticClass:"u-m-r-10"},[A("u-avatar",{attrs:{src:e.avatar?e.avatar:"../../static/logo.png",mode:"square",size:"100"}})],1),A("v-uni-view",{staticClass:"u-flex-1 text-white margin-left-xs"},[A("v-uni-view",{staticClass:"text-30  text-bold"},[t._v(t._s(e.userName))]),A("v-uni-view",{staticClass:"text-26 margin-top-xs u-tips-color flex justify-between"},[A("v-uni-view",{staticStyle:{color:"#999999"}},[t._v(t._s(e.gameName)+"/"+t._s(e.orderNumber)+t._s(e.unit))]),A("v-uni-view",{staticClass:"text-white"},[t._v("实付："),A("v-uni-text",{staticClass:"text-lg",staticStyle:{color:"#FF6F1B"}},[t._v(t._s(e.payMoney)+"币")])],1)],1)],1)],1),A("v-uni-view",{staticClass:"flex justify-end u-p-t-20"},[0==e.state?A("u-button",{attrs:{"custom-style":t.customStyle1,shape:"circle"},on:{click:function(A){arguments[0]=A=t.$handleEvent(A),t.cancelOrder(e)}}},[t._v("取消订单")]):t._e(),0==e.state?A("u-button",{attrs:{"custom-style":t.customStyle,shape:"circle"},on:{click:function(A){arguments[0]=A=t.$handleEvent(A),t.goNav("/my/order/pay?id="+e.ordersId+"&isTrue=0")}}},[t._v("去支付")]):t._e(),4==e.state?A("u-button",{attrs:{"custom-style":t.customStyle,shape:"circle"},on:{click:function(A){arguments[0]=A=t.$handleEvent(A),t.cancel(e,4)}}},[t._v("订单完成")]):t._e(),2==e.state&&0==e.commentCount?A("u-button",{attrs:{"custom-style":t.customStyle,shape:"circle"},on:{click:function(A){arguments[0]=A=t.$handleEvent(A),t.goNav("/pages/index/game/orderDet?id="+e.orderTakingId+"&num="+e.orderNumber)}}},[t._v("再来一单")]):t._e(),2==e.state&&0==e.commentCount?A("u-button",{attrs:{"custom-style":t.customStyle,shape:"circle"},on:{click:function(A){arguments[0]=A=t.$handleEvent(A),t.goNav("/my/order/feedback?id="+e.orderTakingId+"&ordersId="+e.ordersId)}}},[t._v("去评价")]):t._e(),3==e.state?A("u-button",{attrs:{"custom-style":t.customStyle,shape:"circle"},on:{click:function(A){arguments[0]=A=t.$handleEvent(A),t.delOrder(e)}}},[t._v("删除订单")]):t._e(),1==e.state?A("u-button",{attrs:{"custom-style":t.customStyle1,shape:"circle"},on:{click:function(A){arguments[0]=A=t.$handleEvent(A),t.cancel(e,3)}}},[t._v("取消订单")]):t._e(),1==e.state?A("u-button",{attrs:{"custom-style":t.customStyle,shape:"circle"},on:{click:function(A){arguments[0]=A=t.$handleEvent(A),t.goNav("/my/order/pay?id="+e.ordersId+"&isTrue=1")}}},[t._v("查看详情")]):t._e(),4==e.state?A("u-button",{attrs:{"custom-style":t.customStyle,shape:"circle"},on:{click:function(A){arguments[0]=A=t.$handleEvent(A),t.goMsg(e)}}},[t._v("联系TA")]):t._e()],1)],1):t._e()})),0==t.goods.length?A("empty"):t._e()],2),A("u-modal",{attrs:{content:t.meContent,title:t.meTitle,"show-cancel-button":t.meShowCancel,"confirm-text":t.meConfirmText,"cancel-text":t.meCancelText},on:{cancel:function(e){arguments[0]=e=t.$handleEvent(e),t.meHandleClose.apply(void 0,arguments)},confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.meHandleBtn.apply(void 0,arguments)}},model:{value:t.meShowModel,callback:function(e){t.meShowModel=e},expression:"meShowModel"}})],1)},r=[]},f058:function(t,e,A){"use strict";A.r(e);var a=A("667c"),n=A.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){A.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a}}]);