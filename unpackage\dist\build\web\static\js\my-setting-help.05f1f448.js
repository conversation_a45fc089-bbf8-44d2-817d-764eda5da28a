(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["my-setting-help"],{"76f7":function(t,n,e){"use strict";e("6a54"),Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;n.default={data:function(){return{content:""}},onLoad:function(){this.getGuize()},methods:{getGuize:function(){var t=this;this.$Request.getT("/app/common/type/175").then((function(n){0===n.code&&(t.content=n.data.value)}))}}}},a071:function(t,n,e){"use strict";e.r(n);var i=e("ca27"),u=e("ba62");for(var o in u)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return u[t]}))}(o);var a=e("828b"),c=Object(a["a"])(u["default"],i["b"],i["c"],!1,null,"db304f50",null,!1,i["a"],void 0);n["default"]=c.exports},ba62:function(t,n,e){"use strict";e.r(n);var i=e("76f7"),u=e.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(o);n["default"]=u.a},ca27:function(t,n,e){"use strict";e.d(n,"b",(function(){return i})),e.d(n,"c",(function(){return u})),e.d(n,"a",(function(){}));var i=function(){var t=this.$createElement,n=this._self._c||t;return n("v-uni-view",{staticClass:"home1",staticStyle:{"font-size":"14px","line-height":"26px",padding:"32upx",background:"#FFFFFF",color:"#000000"}},[n("v-uni-view",{staticStyle:{color:"#000000","font-size":"28upx"},domProps:{innerHTML:this._s(this.content)}})],1)},u=[]}}]);