{"@platforms": ["android", "iPhone", "iPad"], "id": "__UNI__B825803", "name": "幸福汇", "version": {"name": "1.0.1", "code": 101}, "description": "", "launch_path": "__uniappview.html", "developer": {"name": "", "email": "", "url": ""}, "permissions": {"Payment": {}, "Share": {}, "OAuth": {}, "Maps": {"coordType": "gcj02"}, "VideoPlayer": {}, "UniNView": {"description": "UniNView原生渲染"}}, "plus": {"useragent": {"value": "uni-app", "concatenate": true}, "splashscreen": {"autoclose": true, "delay": 0, "target": "id:1", "waiting": true}, "popGesture": "close", "launchwebview": {"id": "1", "kernel": "WKWebview"}, "statusbar": {"immersed": "supportedDevice", "style": "dark", "background": "#FFFFFF"}, "privacy": {"prompt": "template", "template": {"title": "服务协议和隐私政策", "message": "　　请你务必审慎阅读、充分理解“服务协议”和“隐私政策”各条款，包括但不限于：为了更好的向你提供服务，我们需要收集你的设备标识、操作日志等信息用于分析、优化应用性能。<br/>　　你可阅读<a href=\"http://xiangqin.unvue.cn/my/setting/xieyi\">《服务协议》</a>和<a href=\"http://xiangqin.unvue.cn/my/setting/mimi\">《隐私政策》</a>了解详细信息。如果你同意，请点击下面同意按钮开始接受我们的服务。", "buttonAccept": "同意", "buttonRefuse": "不同意"}}, "usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "distribute": {"icons": {"android": {"hdpi": "icon-android-hdpi.png", "xhdpi": "icon-android-xhdpi.png", "xxhdpi": "icon-android-xxhdpi.png", "xxxhdpi": "icon-android-xxxhdpi.png"}, "ios": {"appstore": "unpackage/res/icons/1024x1024.png", "ipad": {"app": "unpackage/res/icons/76x76.png", "app@2x": "unpackage/res/icons/152x152.png", "notification": "unpackage/res/icons/20x20.png", "notification@2x": "unpackage/res/icons/40x40.png", "proapp@2x": "unpackage/res/icons/167x167.png", "settings": "unpackage/res/icons/29x29.png", "settings@2x": "unpackage/res/icons/58x58.png", "spotlight": "unpackage/res/icons/40x40.png", "spotlight@2x": "unpackage/res/icons/80x80.png"}, "iphone": {"app@2x": "unpackage/res/icons/120x120.png", "app@3x": "unpackage/res/icons/180x180.png", "notification@2x": "unpackage/res/icons/40x40.png", "notification@3x": "unpackage/res/icons/60x60.png", "settings@2x": "unpackage/res/icons/58x58.png", "settings@3x": "unpackage/res/icons/87x87.png", "spotlight@2x": "unpackage/res/icons/80x80.png", "spotlight@3x": "unpackage/res/icons/120x120.png"}, "prerendered": "false"}}, "google": {"autoSdkPermissions": true, "permissions": ["<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.INTERNET\"/>", "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>"], "packagename": "xingfuhui.com", "custompermissions": true}, "apple": {"dSYMs": false, "devices": "universal"}, "plugins": {"ad": {}, "audio": {"mp3": {"description": "Android平台录音支持MP3格式文件"}}, "maps": {"amap": {"appkey_android": "36d71986db0b3ada5470f2c8c4586db2", "appkey_ios": "16357572a10630ce4894a69934298e1b", "name": "amapB6suWzr5h"}, "description": "地图插件"}, "oauth": {"weixin": {"UniversalLinks": "https://tk.gomyorder.cn/", "appid": "wxdbd888f4bc8f08cd", "appsecret": "d73468b34d33de7e31b0e508e7acf130"}}, "payment": {"alipay": {"__platform__": ["android"]}, "weixin": {"UniversalLinks": "https://tk.gomyorder.cn/", "__platform__": ["android"], "appid": "wxdbd888f4bc8f08cd"}}, "share": {"weixin": {"UniversalLinks": "https://tk.gomyorder.cn/", "appid": "wxdbd888f4bc8f08cd"}}}, "orientation": "portrait-primary"}, "uniStatistics": {"enable": false}, "allowsInlineMediaPlayback": true, "safearea": {"background": "#FFFFFF", "bottom": {"offset": "auto"}}, "uni-app": {"compilerVersion": "4.57", "control": "uni-v3", "nvueCompiler": "uni-app", "renderer": "auto", "nvue": {"flex-direction": "column"}, "nvueLaunchMode": "normal"}, "tabBar": {"color": "#999999", "selectedColor": "#FF76A1", "backgroundColor": "#FFFFFF", "borderStyle": "rgba(0,0,0,0.4)", "list": [{"pagePath": "pages/index/index", "iconPath": "static/tabbar/index.png", "selectedIconPath": "static/tabbar/index_.png", "text": "首页", "visible": true}, {"pagePath": "pages/index/details", "iconPath": "static/tabbar/shequ.png", "selectedIconPath": "static/tabbar/shequ_.png", "text": "动态"}, {"pagePath": "my/hongniang/admin", "iconPath": "static/tabbar/worker.png", "selectedIconPath": "static/tabbar/worker_.png", "text": "工作台", "visible": false}, {"pagePath": "pages/hongniang/index", "iconPath": "static/tabbar/hn.png", "selectedIconPath": "static/tabbar/hn_.png", "text": "红娘", "visible": true}, {"pagePath": "pages/my/index", "iconPath": "static/tabbar/my.png", "selectedIconPath": "static/tabbar/my_.png", "text": "我的"}], "height": "50px", "child": ["lauchwebview"], "selected": 0}, "launch_path": "__uniappview.html", "adid": "125581290303"}}