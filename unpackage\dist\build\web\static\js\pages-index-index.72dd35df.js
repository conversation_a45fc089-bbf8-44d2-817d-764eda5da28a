(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-index-index"],{"0ee8":function(t,i,e){var n=e("c86c");i=n(!1),i.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.box-empty[data-v-c38e7f98]{width:100%;height:70vh;display:flex;align-content:center;justify-content:center;flex-wrap:wrap}.box-empty uni-image[data-v-c38e7f98]{width:%?378?%}.box-empty .box-empty-txt[data-v-c38e7f98]{width:100%;color:#999;text-align:center;margin-top:%?20?%}.box[data-v-c38e7f98]{width:%?686?%;height:70vh;position:relative}.box .box-item[data-v-c38e7f98]{transition:width .3s ease;width:%?686?%;height:70vh;position:absolute;border-radius:%?20?%;top:0}.box .box-item-info[data-v-c38e7f98]{position:absolute;bottom:%?30?%;left:%?30?%;width:%?626?%;height:auto}.box .box-item-info-name[data-v-c38e7f98]{width:100%}.box .box-item-info-name-l[data-v-c38e7f98]{font-size:%?42?%;color:#fff;font-weight:700}.box .box-item-info-name-r[data-v-c38e7f98]{color:#fff;font-size:%?26?%}.box .box-item-info-label[data-v-c38e7f98]{width:100%;margin-top:%?20?%}.box .box-item-info-label-item[data-v-c38e7f98]{background-color:#fffffd;color:#999;font-size:%?24?%;padding:%?10?% %?16?%;border-radius:%?10?%;margin-right:%?10?%}.box .box-item-info-jianjie[data-v-c38e7f98]{padding:%?10?% %?20?%;margin-top:%?20?%;background-color:#ffe7ef;width:100%;border-radius:%?30?%}.box .box-item-info-jianjie .box-item-info-jianjies[data-v-c38e7f98]{margin-left:%?10?%;color:#000;font-size:%?25?%;width:calc(100% - %?50?%);overflow:hidden;white-space:nowrap;text-overflow:ellipsis;-o-text-overflow:ellipsis}.box .box-item-info-btn[data-v-c38e7f98]{width:100%;margin-top:%?42?%}.box .box-item-info-btn-l[data-v-c38e7f98]{width:%?100?%;height:%?100?%;border-radius:50%;background-color:#ccc}.box .box-item-info-btn-r[data-v-c38e7f98]{width:%?100?%;height:%?100?%;border-radius:50%;background:linear-gradient(0deg,#ff6f9c,#ffa8c7);margin-left:%?210?%}.max-width[data-v-c38e7f98]{width:%?686?%;height:70vh;left:0;margin-top:%?40?%}.max-width .box-items[data-v-c38e7f98]{width:%?686?%;height:70vh;position:relative}.max-width uni-image[data-v-c38e7f98]{width:%?686?%;height:70vh;border-radius:%?20?%}.mod-width[data-v-c38e7f98]{width:%?676?%;height:70vh;left:%?6?%;margin-top:%?20?%}.mod-width .box-items[data-v-c38e7f98]{width:%?686?%;height:70vh;position:relative}.mod-width uni-image[data-v-c38e7f98]{width:%?676?%;height:70vh;border-radius:%?20?%}.min-width[data-v-c38e7f98]{height:70vh;width:%?666?%;left:%?12?%}.min-width .box-items[data-v-c38e7f98]{width:%?686?%;height:70vh;position:relative}.min-width uni-image[data-v-c38e7f98]{width:%?666?%;height:70vh;border-radius:%?20?%}',""]),t.exports=i},"0f18":function(t,i,e){"use strict";var n=e("4194"),a=e.n(n);a.a},1161:function(t,i,e){"use strict";e.r(i);var n=e("227a"),a=e("21a1");for(var s in a)["default"].indexOf(s)<0&&function(t){e.d(i,t,(function(){return a[t]}))}(s);e("0f18");var o=e("828b"),c=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"2406ad96",null,!1,n["a"],void 0);i["default"]=c.exports},1982:function(t,i,e){"use strict";e.d(i,"b",(function(){return a})),e.d(i,"c",(function(){return s})),e.d(i,"a",(function(){return n}));var n={uIcon:e("3688").default},a=function(){var t=this,i=t.$createElement,n=t._self._c||i;return n("v-uni-view",t._l(t.lists,(function(i,a){return n("v-uni-view",{key:a,staticClass:"card-box",class:0==a?"max-width":1==a?"mod-width":"min-width",style:{"z-index":t.zIndexs["zIndex"+(a+1)]+100,left:t.lefts["left"+(a+1)],top:-t.zIndexs["zIndex"+(a+1)]*t.offsetY-t.offsetY+"px"},attrs:{src:i.userImg,animation:t.animations["animation"+(a+1)]},on:{touchend:function(i){arguments[0]=i=t.$handleEvent(i),t.moveEnd(i,a+1)},click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.slideclick(i)}}},[n("v-uni-image",{staticClass:"main-img",attrs:{mode:"aspectFill",src:i.userImg},on:{touchstart:function(i){arguments[0]=i=t.$handleEvent(i),t.moveStart(i,a+1)},touchmove:function(i){arguments[0]=i=t.$handleEvent(i),t.move(i,a+1)}}}),n("v-uni-view",{staticClass:"bottom"},[n("v-uni-view",{staticClass:"flex align-center justify-between"},[n("v-uni-view",{staticClass:"flex align-center "},[n("v-uni-view",{staticClass:"yhm"},[t._v(t._s(i.realName))]),n("v-uni-image",{staticStyle:{width:"40rpx",height:"40rpx"},attrs:{src:e("d7d2")}})],1),n("v-uni-view",{staticClass:"wezhi"},["市辖区"!=i.locationCity?n("v-uni-text",[t._v(t._s(i.locationCity))]):n("v-uni-text",[t._v(t._s(i.locationProvince))]),t._v(t._s(i.locationCounty))],1)],1),n("v-uni-view",{staticClass:"flex align-center margin-top-sm"},[1==i.sex?n("v-uni-view",{staticClass:"sexicon"},[n("u-icon",{attrs:{name:"man",color:"#FFFFFF"}}),t._v(t._s(i.age)+"岁")],1):t._e(),2==i.sex?n("v-uni-view",{staticClass:"sexicons"},[n("u-icon",{attrs:{name:"woman",color:"#FFFFFF"}}),t._v(t._s(i.age)+"岁")],1):t._e(),n("v-uni-view",{staticClass:"labe"},[t._v(t._s(i.education))]),1==i.marriageStatus?n("v-uni-view",{staticClass:"labe"},[t._v("未婚")]):t._e(),2==i.marriageStatus?n("v-uni-view",{staticClass:"labe"},[t._v("离异")]):t._e(),n("v-uni-view",{staticClass:"labe"},[t._v(t._s(i.userHeight)+"CM")])],1),n("v-uni-view",{staticClass:"remk"},[n("u-icon",{attrs:{name:"/static/images/index/xinxin.png",color:"#FF749F",size:"35"}}),n("v-uni-text",[t._v(t._s(i.idealAspect))])],1),n("v-uni-view",{staticClass:"flex align-center justify-between padding-lr-xl margin-top-xl"},[n("v-uni-view",{staticClass:"gunbi",on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.onclickImg()}}},[n("u-icon",{attrs:{name:"close",color:"#FFFFFF",size:"45"}})],1),n("v-uni-view",{staticClass:"xihuan",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.slideclick(i,2,a)}}},[n("u-icon",{attrs:{name:"/static/images/index/xihuan.png",color:"#FFFFFF",size:"50"}})],1)],1)],1),0==t.lists.length?n("v-uni-view",{staticClass:"centre"},[n("v-uni-image",{attrs:{src:e("e003"),mode:""}}),n("v-uni-view",{staticClass:"tips"},[t._v("暂无数据")])],1):t._e()],1)})),1)},s=[]},2189:function(t,i,e){t.exports=e.p+"static/images/bgImg.png"},"21a1":function(t,i,e){"use strict";e.r(i);var n=e("6116"),a=e.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){e.d(i,t,(function(){return n[t]}))}(s);i["default"]=a.a},"227a":function(t,i,e){"use strict";e.d(i,"b",(function(){return a})),e.d(i,"c",(function(){return s})),e.d(i,"a",(function(){return n}));var n={uIcon:e("3688").default,uModal:e("7e01").default,uPopup:e("0347").default,uPicker:e("df41").default},a=function(){var t=this,i=t.$createElement,n=t._self._c||i;return n("v-uni-view",{},["是"==t.indexSelect?n("v-uni-view",[n("v-uni-view",{staticClass:"bgs"},[n("v-uni-image",{staticStyle:{width:"100%",height:"189px"},attrs:{src:e("2189")}})],1),n("v-uni-view",{staticClass:"content"},[n("v-uni-view",{staticClass:"headtop"},[n("v-uni-view",{staticClass:"flex align-center"},t._l(t.listtab,(function(i,e){return n("v-uni-view",{key:e,staticClass:"headleft",class:t.listIndex==e?"actleft":"",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.bindisttab(e)}}},[t._v(t._s(i.name))])})),1),n("v-uni-view",{staticClass:"headright",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.opensx.apply(void 0,arguments)}}},[n("v-uni-image",{staticStyle:{width:"36rpx",height:"40rpx"},attrs:{src:e("42c7")}})],1)],1),n("v-uni-view",{staticClass:"my-box"},[n("sliderSwiper",{attrs:{list:t.listImg},on:{closeSlider:function(i){arguments[0]=i=t.$handleEvent(i),t.closeSlider.apply(void 0,arguments)},clickSlider:function(i){arguments[0]=i=t.$handleEvent(i),t.clickSlider.apply(void 0,arguments)}}})],1)],1)],1):n("v-uni-view",{staticStyle:{"padding-bottom":"120rpx"}},[n("v-uni-view",{staticClass:"swiper-banner"},[n("v-uni-swiper",{staticClass:"swiper-container",staticStyle:{height:"420rpx"},attrs:{autoplay:!0,interval:4e3,circular:!0,"indicator-dots":!1,"indicator-active-color":"#f2ad44","indicator-color":"#cccccc"}},[t._l(t.bannerImg,(function(i,e){return[i?n("v-uni-swiper-item",{key:e+"_0",staticClass:"swiper-wrapper",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goNav(i)}}},[n("v-uni-image",{staticStyle:{width:"100%",height:"420rpx"},attrs:{"lazy-load":"true","fade-show":"true",src:i.imageUrl,mode:"aspectFill"}})],1):t._e()]}))],2)],1),n("v-uni-view",{staticClass:"flex margin-lr justify-between align-center",staticStyle:{"margin-top":"-30rpx"}},t._l(t.navlist,(function(i,e){return n("v-uni-view",{key:e,on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goNav(i)}}},[n("v-uni-image",{staticStyle:{width:"160rpx",height:"160rpx"},attrs:{src:i.imageUrl}})],1)})),1),n("v-uni-view",{staticClass:"headtops"},[n("v-uni-view",{staticClass:"flex align-center"},t._l(t.listtab,(function(i,e){return n("v-uni-view",{key:e,staticClass:"headleft",class:t.listIndex==e?"actleft":"",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.bindisttab(e)}}},[t._v(t._s(i.name)),t.listIndex==e?n("v-uni-view",{staticClass:"line"}):t._e()],1)})),1),n("v-uni-view",{staticClass:"headright",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.opensx.apply(void 0,arguments)}}},[n("v-uni-image",{staticStyle:{width:"36rpx",height:"40rpx"},attrs:{src:e("42c7")}})],1)],1),n("v-uni-view",{staticClass:"litix"},t._l(t.listImg,(function(i,a){return n("v-uni-view",{key:a,staticClass:"listiem",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goDetail(i.userId)}}},[n("v-uni-image",{staticClass:"main-img",attrs:{mode:"aspectFill",src:i.userImg?i.userImg:"../../static/logo.png"}}),n("v-uni-view",{staticClass:"margin-left-sm flex-sub"},[n("v-uni-view",{staticClass:"flex align-center justify-between"},[n("v-uni-view",{staticClass:"flex align-center "},[n("v-uni-view",{staticClass:"yhm"},[t._v(t._s(i.realName))]),n("v-uni-image",{staticStyle:{width:"40rpx",height:"40rpx"},attrs:{src:e("d7d2")}})],1),n("v-uni-view",{staticClass:"wezhi"},["市辖区"!=i.locationCity?n("v-uni-text",[t._v(t._s(i.locationCity))]):n("v-uni-text",[t._v(t._s(i.locationProvince))]),t._v(t._s(i.locationCounty))],1)],1),n("v-uni-view",{staticClass:"flex align-center margin-tb-xs"},[1==i.sex?n("v-uni-view",{staticClass:"sexicon"},[n("u-icon",{attrs:{name:"man",color:"#FFFFFF",size:"18"}}),t._v(t._s(i.age)+"岁")],1):t._e(),2==i.sex?n("v-uni-view",{staticClass:"sexicons"},[n("u-icon",{attrs:{name:"woman",color:"#FFFFFF",size:"18"}}),t._v(t._s(i.age)+"岁")],1):t._e(),n("v-uni-view",{staticClass:"labe"},[t._v(t._s(i.education))]),1==i.marriageStatus?n("v-uni-view",{staticClass:"labe"},[t._v("未婚")]):t._e(),2==i.marriageStatus?n("v-uni-view",{staticClass:"labe"},[t._v("离异")]):t._e(),n("v-uni-view",{staticClass:"labe"},[t._v(t._s(i.userHeight)+"CM")])],1),n("v-uni-view",{staticClass:"remk"},[t._v(t._s(i.idealAspect))])],1)],1)})),1)],1),n("u-modal",{attrs:{content:t.meContent,title:t.meTitle,"show-cancel-button":t.meShowCancel,"confirm-text":t.meConfirmText,"cancel-text":t.meCancelText},on:{cancel:function(i){arguments[0]=i=t.$handleEvent(i),t.meHandleClose.apply(void 0,arguments)},confirm:function(i){arguments[0]=i=t.$handleEvent(i),t.meHandleBtn.apply(void 0,arguments)}},model:{value:t.meShowModel,callback:function(i){t.meShowModel=i},expression:"meShowModel"}}),n("u-popup",{attrs:{mode:"bottom","border-radius":"24",closeable:!0,"close-icon":"close-circle",height:"800rpx","close-icon-size":"45"},model:{value:t.show,callback:function(i){t.show=i},expression:"show"}},[n("v-uni-view",{staticClass:" ",staticStyle:{padding:"40rpx 40rpx 230rpx 40rpx"}},[n("v-uni-view",{staticClass:"text-center tites1"},[t._v("筛选")]),n("v-uni-view",{staticStyle:{"margin-top":"80rpx"}},[n("v-uni-view",{staticClass:"tites"},[t._v("性别")]),n("v-uni-view",{staticClass:"padding-tb-sm flex"},[n("v-uni-view",{staticClass:"xlbox",class:-1==t.sex?"xlboxAct":"",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.changsex(-1)}}},[t._v("不限")]),n("v-uni-view",{staticClass:"xlbox",class:1==t.sex?"xlboxAct":"",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.changsex(1)}}},[t._v("男")]),n("v-uni-view",{staticClass:"xlbox",class:2==t.sex?"xlboxAct":"",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.changsex(2)}}},[t._v("女")])],1)],1),n("v-uni-view",{staticClass:"margin-top"},[n("v-uni-view",{staticClass:"tites"},[t._v("年龄")]),n("v-uni-view",{staticClass:"padding-tb-sm flex flex-wrap"},t._l(t.agelist,(function(i,e){return n("v-uni-view",{key:e,staticClass:"xlbox margin-bottom-sm",class:t.ageIndex==e?"xlboxAct":"",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.changAge(e,i)}}},[0!=i.minMoney&&0!=i.maxMoney?n("v-uni-text",[t._v(t._s(i.minMoney)+"-"+t._s(i.maxMoney))]):t._e(),0==i.minMoney?n("v-uni-text",[t._v(t._s(i.maxMoney))]):t._e(),0==i.maxMoney?n("v-uni-text",[t._v(t._s(i.minMoney))]):t._e()],1)})),1)],1),n("v-uni-view",{staticClass:"margin-top-xs"},[n("v-uni-view",{staticClass:"tites"},[t._v("身高")]),n("v-uni-view",{staticClass:"padding-tb-sm flex flex-wrap"},t._l(t.heightlist,(function(i,e){return n("v-uni-view",{key:e,staticClass:"xlbox margin-bottom-sm",class:t.heightIndex==e?"xlboxAct":"",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.changHeight(e,i)}}},[0!=i.minMoney&&0!=i.maxMoney?n("v-uni-text",[t._v(t._s(i.minMoney)+"-"+t._s(i.maxMoney))]):t._e(),0==i.minMoney?n("v-uni-text",[t._v(t._s(i.maxMoney))]):t._e(),0==i.maxMoney?n("v-uni-text",[t._v(t._s(i.minMoney))]):t._e()],1)})),1)],1),n("v-uni-view",{staticClass:"margin-top-xs"},[n("v-uni-view",{staticClass:"tites"},[t._v("学历")]),n("v-uni-view",{staticClass:"padding-tb-sm flex flex-wrap"},t._l(t.xllist,(function(i,e){return n("v-uni-view",{key:e,staticClass:"xlbox margin-bottom-sm",class:t.xllistIndex==e?"xlboxAct":"",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.changexl(e)}}},[t._v(t._s(i.name))])})),1)],1),n("v-uni-view",{staticClass:"margin-top-xs"},[n("v-uni-view",{staticClass:"tites"},[t._v("推荐地区")]),n("v-uni-view",{staticClass:"padding-tb-sm flex"},[n("v-uni-view",{staticClass:"xlbox",class:0==t.dqIndex?"xlboxAct":"",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.changesq(0)}}},[t._v("不限")]),n("v-uni-view",{staticClass:"xlbox",class:1==t.dqIndex?"xlboxAct":"",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.changesq(1)}}},[t._v(t._s(t.city?t.city:"选择地区"))])],1)],1),n("v-uni-view",{staticClass:"flex align-center justify-between tanbers"},[n("v-uni-view",{staticClass:"btn",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.chongzhi.apply(void 0,arguments)}}},[t._v("重置")]),n("v-uni-view",{staticClass:"btn",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.shaixuan.apply(void 0,arguments)}}},[t._v("保存")])],1)],1)],1),n("u-picker",{attrs:{mode:"region",params:t.params},on:{confirm:function(i){arguments[0]=i=t.$handleEvent(i),t.confirmdq.apply(void 0,arguments)},cancel:function(i){arguments[0]=i=t.$handleEvent(i),t.canceldq.apply(void 0,arguments)}},model:{value:t.dqshow,callback:function(i){t.dqshow=i},expression:"dqshow"}}),n("u-modal",{attrs:{content:t.meContent,title:t.meTitle,"show-cancel-button":t.meShowCancel,"confirm-text":t.meConfirmText,"cancel-text":t.meCancelText},on:{cancel:function(i){arguments[0]=i=t.$handleEvent(i),t.meHandleClose.apply(void 0,arguments)},confirm:function(i){arguments[0]=i=t.$handleEvent(i),t.meHandleBtn.apply(void 0,arguments)}},model:{value:t.meShowModel,callback:function(i){t.meShowModel=i},expression:"meShowModel"}})],1)},s=[]},2601:function(t,i,e){"use strict";e.d(i,"b",(function(){return n})),e.d(i,"c",(function(){return a})),e.d(i,"a",(function(){}));var n=function(){var t=this.$createElement,i=this._self._c||t;return i("v-uni-view",{staticClass:"page-box"},[i("v-uni-view",{staticClass:"centre"},[i("v-uni-image",{attrs:{src:e("e003"),mode:""}}),i("v-uni-view",{staticClass:"tips"},[this._v(this._s(this.content))])],1)],1)},a=[]},"2bdc":function(t,i,e){"use strict";e.r(i);var n=e("2601"),a=e("ea9e");for(var s in a)["default"].indexOf(s)<0&&function(t){e.d(i,t,(function(){return a[t]}))}(s);e("6594");var o=e("828b"),c=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"4fb1bbd1",null,!1,n["a"],void 0);i["default"]=c.exports},"38d0":function(t,i,e){var n=e("7ea9");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=e("967d").default;a("0c1dbf9a",n,!0,{sourceMap:!1,shadowMode:!1})},"401e":function(t,i,e){"use strict";e.r(i);var n=e("ce54"),a=e.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){e.d(i,t,(function(){return n[t]}))}(s);i["default"]=a.a},4194:function(t,i,e){var n=e("7704");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=e("967d").default;a("4b4d6f58",n,!0,{sourceMap:!1,shadowMode:!1})},"42c7":function(t,i){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAoCAYAAACWwljjAAAAAXNSR0IArs4c6QAABNlJREFUWEftmEtsG1UUhs8548x4jAJBAaEKFkApJUTAAspDCCGxY8FDJaZVgACJmgVSINkkinGUG42dCKUhFRGtCFTqCiFFQizCAikbXhLvbojEW6A2bmmLFJFge2zPPei4E8txHT/SOGHB7GY8vvebc8/5z38vjo6OLgOAhYgMO3wx8+eolPpEa30/IjbtMM9ZZj6CIyMjdxDR6wDwCADQDkGd01q/ZhjGCRQApdQ9zPw2M9+FiPln23Ux8woiHltdXZ2cmpq6sAZEWutHEfEIM+/eRqgMALyXy+Wi8Xj8lAShEI2+vj6rpaXlGSIaA4AbGh0hRPQAYCGXy/XFYrGf1+ZbtzxKqSu11n2IOAAArY2CYmZGxJNa617Hcb4tnueSfIlEIteZpvkqM/cAQGiroQQGAH4BgFeI6COllK4I5Cf5jcw8CQBPAkBgi6FOa61HDMN4VyklObTu2rCiotFoGxEdQ8SHtlAOLmitp5aXl4/OzMz8Xe5DK5W4iOYDzPwWM7dfbuUx8z8A8A4RjSulzm0U9YqaEw6Hjba2tscR8TAi3iS5uJnlQ8Ss1vp9z/OG4vH4H5XGqDqBUiqotX4eAEYRcdcmgDQifuy67ksTExM/VPt/VSA/yVsAoB8AXmbmq6sNuva7X1HfM/Mhx3G+AoCqDbwmIJkgEonsMk1zFACeZeYraoCScv7d17T5xcVFbG9vLzufUipXVhirTRKNRvcYhjHLzA/XkOTLzBxLp9OzoVDoes/z7jQMQ5p3se7IPWWz2dPJZPLr6enpVM0REtihoaGrbNsWfepiZqvSBzDzEjM/5nleIhAIHAeAe4kuNRN+oYg2dTuOc7IuoOHh4VbTNA8DQCcAmBWrBXHJ87wniOg8My8g4p6N3mfm81rrzlgstlA3kGVZ08x8oBoQACS01h2ZTGbJtu15ALitgmxIhLocx/m0kUDiALvS6fQXtm2/CAD3IaLhV14+WALoL+NvAPCGUupsw4AQ8c9cLtcbCAQkOsFKjTqVSmVt216RRttQIK31ISL6sLSjX5ZSF/9ZkrrWHJIIAUAvAMwLkFKKRIvKwczNzYlZy18NixAzC1CPeB7P8/Yi4q1iZYiooNZaa8khuZdm+41SKtlIoDNEdDCTyfxomuZxZt5Xrsr8/WAim832jI+Pf9cwIL/s9xPRmf+UDhHREiLOM3PbRSu9foecf8B8alt1KBgMdhPRg/6SFXqZ3w/FIost2V4dcl037xAsyyokteu6aFkWplIp938dUkqV3b0kEgmcnZ0VP5SPXMOqrESHZJNwOxGh1rqwZH6SC0OCiL7clA6ZpiknJQerdXtmlnI/4HneT01NTScQ8e5yVeY/W8pkMt1165BS6hpmngGAjho2kGI/atYhIupUStXth641TfMoMz9Vg4XN+6EiHZL2UTZFxF0CwHNjY2Of1ZVDckLS2toaBoBBrfXNiFjJ7Bf8UDAYfIGI9klUi/1QPokvCuOvyWTyzcnJyfr8kAwwMDBg27a9NxAIPA0A++XophxYqR9yXdcWzdnAemSUUitSaXVFqHiwwcHB5lAotJuZw7KEpWCl9qPajmZT9qPcoAJmWdYtRNRRAvaX53niGD9omEGr9JX9/f0tzc3NcoAqG8kwIsrZYY9UTq3Rkff+BRUb2QJ91rf5AAAAAElFTkSuQmCC"},"50cc":function(t,i,e){"use strict";e.r(i);var n=e("ca46"),a=e("8cbe");for(var s in a)["default"].indexOf(s)<0&&function(t){e.d(i,t,(function(){return a[t]}))}(s);e("bfa4");var o=e("828b"),c=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"c38e7f98",null,!1,n["a"],void 0);i["default"]=c.exports},5280:function(t,i,e){"use strict";e("6a54"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,e("bd06"),e("aa9c"),e("fd3c"),e("f7a5");var n={name:"sliderSiper",props:{list:{type:Array,default:function(){return[]}}},data:function(){return{lists:[],y:0,yNum:200}},watch:{list:function(t){this.list.length>0?this.spliceList():this.lists=[]}},methods:{clickSlider:function(t){this.$emit("clickSlider",t)},saveUserFilter:function(t){var i=this,e={filterUserId:t};this.$Request.postT("/app/userFilter/saveUserFilter",e).then((function(t){0==t.code?(i.lists.shift(),i.setListItem()):uni.showToast({title:t.msg,icon:"none"})}))},closeCard:function(t){uni.getStorageSync("token")?this.saveUserFilter(t):this.$emit("closeSlider",!1)},moveStart:function(t,i){t.preventDefault(),this.y=t.touches[0].clientY,this.lists[i].moving=!0},moverIng:function(t,i){var e=t.touches[0].clientY;if(!e||uni.getStorageSync("token")){if(t.preventDefault(),this.lists[i].moving){var n=t.touches[0].clientY,a=this.y;n<a&&(this.lists[i].y1=t.touches[0].clientY-this.y,this.$forceUpdate())}}else this.$emit("closeSlider",!1)},setListItem:function(){if(this.lists[this.lists.length-1]){var t=this.lists[this.lists.length-1].userId,i=this.list.findIndex((function(i){return i.userId==t}));-1!=i&&i!=this.list.length-1&&this.lists.push(this.list[i+1])}},moveEnd:function(t,i){var e=this;t.preventDefault(),this.lists[i].moving&&(this.lists[i].moving=!1,this.lists[i].y1<-this.yNum?(this.lists[i].y1=0,this.lists[i].transition=!0,this.lists.shift(),this.setListItem(),setTimeout((function(){e.lists[i].transition=!1}),300)):(this.y=0,this.lists[i].y1=0,this.$nextTick((function(){e.lists[i].transition=!0,e.$forceUpdate(),setTimeout((function(){e.lists[i].transition=!1}),300)}))))},spliceList:function(){this.list.length>0?(this.list.map((function(t){t.y1=0,t.moving=!1,t.transition=!1})),this.lists=this.list.slice(0,3)):this.lists=[]}}};i.default=n},6116:function(t,i,e){"use strict";e("6a54");var n=e("f5bd").default;Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,e("64aa"),e("aa9c"),e("5ef2"),e("fd3c"),e("bf0f"),e("2797");var a=n(e("2bdc")),s=n(e("802b")),o=n(e("50cc")),c={components:{vastwuImgbox:s.default,empty:a.default,sliderSwiper:o.default},data:function(){return{background:{backgroundImage:"linear-gradient(90deg, #E4E7F8 0%, #F1E3F4 46%, #FDE1EF 100%)"},backgrounds:{backgroundColor:"transparent"},backgroundsl:{backgroundColor:"#FFFFFF"},listtab:[{name:"推荐",id:1},{name:"附近",id:2}],listIndex:0,meShowModel:!1,meShowCancel:!0,meTitle:"提示",meContent:"",meConfirmText:"确认",meCancelText:"取消",meIndex:"",list:[],listImg:[],show:!1,agelist:[],ageIndex:-1,heightlist:[],heightIndex:-1,popupcm:"",xllist:[],xllistIndex:-1,dqIndex:0,dqshow:!1,params:{province:!0,city:!0,area:!1},city:"",province:"",minAge:"",maxAge:"",mixHeight:"",maxHeight:"",sxIstrue:0,HeightIstrue:0,statusStauts:"",formData:{},sauto:!1,firstlogin:!0,arr:[],showModal:!0,sex:"-1",indexSelect:"否",fixeds:!1,bannerImg:[],navlist:[]}},onShareAppMessage:function(t){return{path:"/pages/index/index?invitation="+this.invitationCode,title:this.tuiguang,imageUrl:this.bgImg}},onShareTimeline:function(t){return{path:"/pages/index/index?invitation="+this.invitationCode,title:this.tuiguang,imageUrl:this.bgImg}},onPageScroll:function(t){console.log(t.scrollTop),Number(t.scrollTop)>=80?this.fixeds=!0:this.fixeds=!1},onLoad:function(t){this.indexSelect=this.$queue.getData("indexSelect"),this.invitationCode=this.$queue.getData("invitationCode"),this.shangxianSelect=this.$queue.getData("shangxianSelect"),this.myId=this.$queue.getData("userId");var i=this;i.$Request.getT("/app/common/type/292").then((function(t){0==t.code&&t.data&&t.data.value&&i.arr.push(t.data.value)})),i.myId&&(i.$Request.getT("/app/common/type/116").then((function(t){0===t.code&&t.data&&t.data.value&&(i.tuiguang=t.data.value)})),i.$Request.getT("/app/common/type/313").then((function(t){0===t.code&&t.data&&t.data.value&&(i.bgImg=t.data.value)}))),uni.getLocation({type:"gcj02",geocode:!0,success:function(t){console.log(t,"地理位置"),i.latitude=t.latitude,i.longitude=t.longitude,uni.setStorageSync("latitude",t.latitude),uni.setStorageSync("longitude",t.longitude),i.selectCity(i.longitude,i.latitude)},fail:function(){console.log("获取地址失败")}}),t.invitation&&i.$queue.setData("inviterCode",t.invitation),uni.getStorageSync("sex")&&(i.sex=1==uni.getStorageSync("sex")?2:1),i.getlist()},onShow:function(){var t=this;uni.getStorageSync("sex")&&(t.sex=1==uni.getStorageSync("sex")?2:1),uni.$once("closeCard",(function(i){t.getlist()})),uni.getStorageSync("token")&&(t.getRenZheng(),t.getIsVip()),t.myId=uni.getStorageSync("userId"),t.getjingang(),t.getBnner(),t.getxllist(),t.getAge(),t.getHeight()},methods:{goNav:function(t){t.name;var i=t.url;i&&(-1!==i.indexOf("/pages/")||-1!==i.indexOf("/my/")||-1!==i.indexOf("/package/")?"/pages/hongniang/index"==i?uni.switchTab({url:i}):uni.navigateTo({url:i}):i&&(window.location.href=i))},getjingang:function(){var t=this;this.$Request.getT("/app/banner/selectBannerList?classify=2").then((function(i){0==i.code&&(t.navlist=i.data)}))},getBnner:function(){var t=this;this.$Request.getT("/app/banner/selectBannerList?classify=8").then((function(i){0==i.code&&(t.bannerImg=i.data)}))},closeSlider:function(){console.log("bbbbbbbbbbbbbbb"),this.meShowModel=!0,this.meTitle="提示",this.meContent="您还未登录,请先登录",this.meIndex="m1",this.meShowCancel=!0},goDetail:function(t){uni.getStorageSync("token")?uni.navigateTo({url:"/package/pages/game/detail?byUserId="+t}):(this.meShowModel=!0,this.meTitle="提示",this.meContent="您还未登录,请先登录",this.meIndex="m1",this.meShowCancel=!0)},clickSlider:function(t){uni.navigateTo({url:"/package/pages/game/detail?byUserId="+t})},getRenZheng:function(){var t=this;this.$Request.get("/app/userCertification/getMyUserCertification",{authType:1}).then((function(i){0==i.code&&i.data?0==i.data.status?(t.statusStauts=1,uni.setStorageSync("statusStauts",t.statusStauts)):1==i.data.status?(t.statusStauts=2,uni.setStorageSync("statusStauts",t.statusStauts)):2==i.data.status&&(t.statusStauts=3,uni.setStorageSync("statusStauts",t.statusStauts)):(t.statusStauts=0,uni.setStorageSync("statusStauts",t.statusStauts))}))},opensx:function(){this.myId?this.show=!0:(this.meShowModel=!0,this.meTitle="提示",this.meContent="您还未登录,请先登录",this.meIndex="m1",this.meShowCancel=!0)},changAge:function(t,i){this.ageIndex=t,this.minAge=i.minMoney,this.maxAge=i.maxMoney},changHeight:function(t,i){this.heightIndex=t,this.mixHeight=i.minMoney,this.maxHeight=i.maxMoney},bindisttab:function(t){this.listIndex=t,this.listImg=[],this.list=[],this.getlist()},canceldq:function(t){this.dqIndex=0,this.city="",this.province=""},confirmdq:function(t){console.log(t),this.city=t.city.label,this.province=t.province.label},changesq:function(t){this.dqIndex=t,1==this.dqIndex?this.dqshow=!0:(this.city="",this.province="")},changsex:function(t){this.sex=t},changexl:function(t){this.xllistIndex=t},getAge:function(){var t=this;this.$Request.get("/app/screenMoney/getScreenMoneyList?type=1").then((function(i){uni.hideLoading(),0===i.code?t.agelist=i.data.records:uni.showToast({title:i.msg,icon:"none"})}))},getHeight:function(){var t=this;this.$Request.get("/app/screenMoney/getScreenMoneyList?type=2").then((function(i){uni.hideLoading(),0===i.code?t.heightlist=i.data.records:uni.showToast({title:i.msg,icon:"none"})}))},getxllist:function(){var t=this;this.$Request.getT("/app/dict/getDictList?name=学历").then((function(i){if(0==i.code){var e=[];i.data.map((function(t){var i={name:"不限",id:"",isTrue:!1};i.name=t.value,i.isTrue=!1,e.push(i)})),t.xllist=e}}))},shaixuan:function(){this.show=!1,this.sxIstrue=1,this.getlist()},chongzhi:function(){this.show=!1,this.sex="-1",this.xllistIndex=-1,this.sxIstrue=0,this.ageIndex=-1,this.heightIndex=-1,this.dqIndex=0,this.city="",this.province="",this.getlist()},onclickImg:function(t){this.myId?this.list.length<=1?uni.showToast({title:"暂无更多推荐数据",icon:"none"}):this.$refs.vastwuImgbox.action(t):(this.meShowModel=!0,this.meTitle="提示",this.meContent="您还未登录,请先登录",this.meIndex="m1",this.meShowCancel=!0)},meHandleBtn:function(){"m1"==this.meIndex?uni.navigateTo({url:"/pages/public/login"}):"m3"==this.meIndex?uni.navigateTo({url:"/my/vip/index"}):"m2"==this.meIndex?(console.log("确认"),this.showModal=!1):"m4"==this.meIndex?uni.navigateTo({url:"/my/setting/userinfo"}):"m6"==this.meIndex?uni.navigateTo({url:"/my/renzheng/index"}):"m9"==this.meIndex&&uni.navigateTo({url:"/pages/my/userinfo"})},meHandleClose:function(){(this.meIndex="m2")&&(this.showModal=!0)},selectCity:function(t,i){var e=this;this.$Request.get("/app/Login/selectCity?lat="+i+"&lng="+t).then((function(t){0==t.code&&(e.city=t.data.city,uni.setStorageSync("city",t.data.city))}))},openMsg:function(){console.log("订阅消息");var t=this;uni.getSetting({withSubscriptions:!0,success:function(i){console.log(i.subscriptionsSetting,"------------------"),i.subscriptionsSetting.itemSettings?(uni.setStorageSync("sendMsg",!0),uni.openSetting({success:function(t){console.log(t.authSetting)}})):(console.log(99999),uni.setStorageSync("sendMsg",!1),t.meShowModel=!0,t.meTitle="提示",t.meContent="为了更好的体验,请绑定消息推送",t.meConfirmText="确认",t.meIndex="m2")}})},getUserInfo:function(){var t=this,i=uni.getStorageSync("userId");this.$Request.get("/app/userData/getUserDataInfo?byUserId="+i).then((function(i){0==i.code&&i.data&&(t.formData=i.data),uni.hideLoading()}))},checkEmptyOrNullProperties:function(t){var i=[];for(var e in t)t.hasOwnProperty(e)&&(null!==t[e]&&""!==t[e]||i.push(e));return i},getIsVip:function(){var t=this;this.$Request.get("/app/user/selectUserById").then((function(i){0==i.code&&(i.data.isVip&&1==i.data.isVip?(t.isVip=!0,uni.setStorageSync("isVIP",t.isVip)):(t.isVip=!1,uni.setStorageSync("isVIP",t.isVip)))}))},getlist:function(){var t=this;console.log(this.sex,"*********");var i=this.listtab[this.listIndex].id,e={sortType:i,locationCity:1==this.sxIstrue||0!=this.listIndex?this.city:"",minAge:-1!=this.ageIndex?this.minAge:"",maxAge:-1!=this.ageIndex?this.maxAge:"",minUserHeight:-1!=this.heightIndex?this.mixHeight:"",maxUserHeight:-1!=this.heightIndex?this.maxHeight:"",excludeUserId:this.myId?this.myId:"",education:-1!=this.xllistIndex?this.xllist[this.xllistIndex].name:"",sex:-1!=this.sex?this.sex:"",isFilter:1};this.$Request.get("/app/userData/getAppUserDataList",e).then((function(i){0==i.code&&(i.data.records.length>0?(i.data.records.forEach((function(t,i){if(t.userImg){var e=t.userImg.split(",");t.userImg=e[0]}})),t.listImg=i.data.records):t.listImg=[],t.$nextTick((function(){t.$forceUpdate()})))}))}}};i.default=c},6594:function(t,i,e){"use strict";var n=e("6ee7"),a=e.n(n);a.a},"6ee7":function(t,i,e){var n=e("bafb");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=e("967d").default;a("72fad1e5",n,!0,{sourceMap:!1,shadowMode:!1})},7704:function(t,i,e){var n=e("c86c");i=n(!1),i.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */uni-page-body[data-v-2406ad96]{background-color:#f7f7f7}body.?%PAGE?%[data-v-2406ad96]{background-color:#f7f7f7}[data-v-2406ad96] uni-slider{margin:10px 0!important}[data-v-2406ad96] uni-slider .uni-slider-handle-wrapper{height:%?20?%!important}.bgs[data-v-2406ad96]{width:100%;height:%?346?%;position:fixed;top:0;left:0;right:0;z-index:9}.content[data-v-2406ad96]{position:relative;z-index:99}.headtop[data-v-2406ad96]{display:flex;align-items:center;justify-content:space-between;padding:%?35?% %?30?%;position:fixed;top:%?0?%;left:0;right:0;z-index:9999;background:linear-gradient(90deg,#e4e7f8,#f1e3f4 46%,#fde1ef)}.headtop .headleft[data-v-2406ad96]{font-size:%?34?%;font-family:PingFang SC;font-weight:500;color:#999;margin-right:%?68?%}.headtop .actleft[data-v-2406ad96]{font-size:%?38?%;font-family:PingFang SC;font-weight:500;color:#333}.my-box[data-v-2406ad96]{width:%?686?%;margin:%?0?% auto;position:fixed;top:%?130?%;left:50%;-webkit-transform:translate(-50%);transform:translate(-50%)}.tites1[data-v-2406ad96]{font-size:%?32?%;font-family:PingFang SC;font-weight:800;color:#333;position:fixed;top:0;left:0;right:0;background:#fff;padding:%?30?% 0}.tites[data-v-2406ad96]{font-size:%?32?%;font-family:PingFang SC;font-weight:800;color:#333}.xlbox[data-v-2406ad96]{height:%?80?%;background:#f7f7f7;border-radius:%?12?%;padding:0 %?30?%;display:flex;align-items:center;font-size:%?26?%;font-family:PingFang SC;font-weight:500;color:#666;margin-right:%?20?%}.xlboxAct[data-v-2406ad96]{background-color:rgba(181,203,255,.5);color:#6265ff}.btn[data-v-2406ad96]{width:48%;height:%?100?%;background:linear-gradient(90deg,#787afd,#6265ff);box-shadow:%?3?% %?6?% %?12?% %?0?% #d3d4ff;border-radius:%?24?%;font-size:%?32?%;font-family:PingFang SC;font-weight:700;color:#fff;display:flex;align-items:center;justify-content:center}.tanbers[data-v-2406ad96]{position:fixed;bottom:0;left:0;right:0;z-index:999;padding:%?30?%;background-color:#fff}.headtops[data-v-2406ad96]{display:flex;align-items:center;justify-content:space-between;padding:%?20?% %?30?% 0}.headtops .headleft[data-v-2406ad96]{font-size:%?34?%;font-family:PingFang SC;font-weight:500;color:#999;margin-right:%?68?%}.headtops .line[data-v-2406ad96]{width:%?20?%;margin:0 auto;height:%?6?%;background:#010101;border-radius:%?50?%}.headtops .actleft[data-v-2406ad96]{font-size:%?38?%;font-family:PingFang SC;font-weight:500;color:#010101}.litix .listiem[data-v-2406ad96]{background:#fff;border-radius:%?32?%;margin:%?20?% %?30?%;padding:%?30?% %?20?%;display:flex}.litix .listiem .main-img[data-v-2406ad96]{width:%?150?%;height:%?150?%;border-radius:50%}.litix .listiem .yhm[data-v-2406ad96]{font-size:%?32?%;font-family:PingFang SC;font-weight:700;color:#333;margin-right:%?10?%}.litix .listiem .wezhi[data-v-2406ad96]{font-family:PingFang SC;font-weight:500;font-size:%?24?%;color:#999}.litix .listiem .sexicon[data-v-2406ad96]{background:#38caff;border-radius:%?10?%;font-size:%?24?%;font-family:PingFang SC;font-weight:500;color:#fff;padding:%?4?% %?10?%;margin-right:%?10?%}.litix .listiem .sexicons[data-v-2406ad96]{background:#fbe2f4;border-radius:%?10?%;font-size:%?24?%;font-family:PingFang SC;font-weight:500;color:#fff;padding:%?4?% %?10?%;margin-right:%?10?%}.litix .listiem .labe[data-v-2406ad96]{background:#f2f2f2;border-radius:%?10?%;font-size:%?24?%;font-family:PingFang SC;font-weight:500;color:#999;padding:%?4?% %?10?%;margin-right:%?15?%}.litix .listiem .remk[data-v-2406ad96]{font-family:PingFang SC;font-weight:500;font-size:%?24?%;color:#666;margin-top:%?15?%}',""]),t.exports=i},"7ea9":function(t,i,e){var n=e("c86c");i=n(!1),i.push([t.i,".card-box[data-v-4a7df42e]{position:absolute;height:%?1098?%;width:%?686?%;top:0;transition:z-index 2s easc;border-radius:%?20?%}.max-width[data-v-4a7df42e]{width:%?686?%}.mod-width[data-v-4a7df42e]{width:%?651?%;margin-left:%?15?%}.min-width[data-v-4a7df42e]{width:%?619?%;margin-left:%?33?%}.main-img[data-v-4a7df42e]{width:100%;height:100%;border-radius:%?32?%}\n/* .lookmore {\n\tposition: absolute;\n\twidth: 136rpx;\n\theight: 53rpx;\n\tbottom: 95rpx;\n\tleft: 38%;\n\tz-index: 110;\n} */.bottom[data-v-4a7df42e]{width:100%;padding:0 %?30?%;position:absolute;\n  /* width: 136rpx;\n\theight: 53rpx; */bottom:%?30?%;\n  /* left: 38%; */z-index:110;color:#fff}.gunbi[data-v-4a7df42e]{width:%?100?%;height:%?100?%;\n  /* background: #CCCBD2; */background:linear-gradient(0deg,#c7c7cf,#d3d2d7);border-radius:50%;display:flex;align-items:center;justify-content:center}.xihuan[data-v-4a7df42e]{width:%?100?%;height:%?100?%;background:linear-gradient(0deg,#ff6f9c,#ffa8c7);border-radius:50%;display:flex;align-items:center;justify-content:center}.yhm[data-v-4a7df42e]{font-size:%?42?%;font-family:PingFang SC;font-weight:700;color:#fff;margin-right:%?20?%}.wezhi[data-v-4a7df42e]{font-size:%?26?%;font-family:PingFang SC;font-weight:500;color:#fff}.sexicon[data-v-4a7df42e]{background:#38caff;border-radius:%?10?%;font-size:%?24?%;font-family:PingFang SC;font-weight:500;color:#fff;padding:%?4?% %?10?%;margin-right:%?10?%}.sexicons[data-v-4a7df42e]{background:#fbe2f4;border-radius:%?10?%;font-size:%?24?%;font-family:PingFang SC;font-weight:500;color:#fff;padding:%?4?% %?10?%;margin-right:%?10?%}.labe[data-v-4a7df42e]{background:#fffffd;border-radius:%?10?%;font-size:%?24?%;font-family:PingFang SC;font-weight:500;color:#999;padding:%?4?% %?10?%;margin-right:%?15?%}.remk[data-v-4a7df42e]{width:%?620?%;height:%?58?%;background:#ffe7ef;border-radius:%?29?%;color:#333;display:flex;align-items:center;padding:0 %?10?% 0 %?30?%;margin-top:%?20?%}.remk uni-text[data-v-4a7df42e]{width:100%;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;margin-left:%?10?%}",""]),t.exports=i},"802b":function(t,i,e){"use strict";e.r(i);var n=e("1982"),a=e("401e");for(var s in a)["default"].indexOf(s)<0&&function(t){e.d(i,t,(function(){return a[t]}))}(s);e("d498");var o=e("828b"),c=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"4a7df42e",null,!1,n["a"],void 0);i["default"]=c.exports},"8cbe":function(t,i,e){"use strict";e.r(i);var n=e("5280"),a=e.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){e.d(i,t,(function(){return n[t]}))}(s);i["default"]=a.a},bafb:function(t,i,e){var n=e("c86c");i=n(!1),i.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.page-box[data-v-4fb1bbd1]{position:relative;height:100vh;background:#fff}.centre[data-v-4fb1bbd1]{position:absolute;left:0;top:0;right:0;bottom:0;margin:auto;height:%?400?%;text-align:center;font-size:%?32?%}.centre uni-image[data-v-4fb1bbd1]{width:%?414?%;height:%?269?%;margin:0 auto %?20?%}.centre .tips[data-v-4fb1bbd1]{font-size:%?32?%;color:#999;margin-top:%?20?%}.centre .btn[data-v-4fb1bbd1]{margin:%?80?% auto;width:%?600?%;border-radius:%?32?%;line-height:%?90?%;color:#fff;font-size:%?34?%;background:#7075fe}',""]),t.exports=i},bfa4:function(t,i,e){"use strict";var n=e("c5d0"),a=e.n(n);a.a},c5d0:function(t,i,e){var n=e("0ee8");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=e("967d").default;a("efca114e",n,!0,{sourceMap:!1,shadowMode:!1})},ca46:function(t,i,e){"use strict";e.d(i,"b",(function(){return a})),e.d(i,"c",(function(){return s})),e.d(i,"a",(function(){return n}));var n={uIcon:e("3688").default},a=function(){var t=this,i=t.$createElement,n=t._self._c||i;return n("v-uni-view",[n("v-uni-view",{staticClass:"box"},[t._l(t.lists,(function(i,a){return n("v-uni-view",{key:a,staticClass:"box-item",class:0==a?"max-width":1==a?"mod-width":"min-width",style:{zIndex:100-a,transform:"translateY("+i.y1+"px)",transition:i.transition?"transform 0.3s ease":"none"},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.clickSlider(i.userId)},touchstart:function(i){arguments[0]=i=t.$handleEvent(i),t.moveStart(i,a)},touchmove:function(i){arguments[0]=i=t.$handleEvent(i),t.moverIng(i,a)},touchend:function(i){arguments[0]=i=t.$handleEvent(i),t.moveEnd(i,a)}}},[n("v-uni-image",{attrs:{src:i.userImg,mode:"aspectFill"}}),n("v-uni-view",{staticClass:"box-item-info"},[n("v-uni-view",{staticClass:"box-item-info-name flex align-center justify-between"},[n("v-uni-view",{staticClass:"box-item-info-name-l flex align-center"},[t._v(t._s(i.realName)),n("v-uni-image",{staticStyle:{width:"40rpx",height:"40rpx","margin-left":"10rpx"},attrs:{src:e("d7d2")}})],1),n("v-uni-view",{staticClass:"box-item-info-name-r"},["市辖区"!=i.locationCity?n("v-uni-text",[t._v(t._s(i.locationCity))]):n("v-uni-text",[t._v(t._s(i.locationProvince))]),t._v(t._s(i.locationCounty))],1)],1),n("v-uni-view",{staticClass:"box-item-info-label flex align-center flex-wrap"},[n("v-uni-view",{staticClass:"box-item-info-label-item",staticStyle:{"background-color":"#38CAFF",color:"#ffffff"}},[n("u-icon",{staticStyle:{"margin-right":"6rpx"},attrs:{name:1==i.sex?"man":"woman",color:"#FFFFFF"}}),t._v(t._s(i.age)+"岁")],1),n("v-uni-view",{staticClass:"box-item-info-label-item"},[t._v(t._s(i.education))]),n("v-uni-view",{staticClass:"box-item-info-label-item"},[t._v(t._s(2==i.marriageStatus?"离异":"未婚"))]),n("v-uni-view",{staticClass:"box-item-info-label-item"},[t._v(t._s(i.userHeight)+"CM")])],1),n("v-uni-view",{staticClass:"box-item-info-jianjie flex align-center"},[n("u-icon",{attrs:{name:"/static/images/index/xinxin.png",color:"#FF749F",size:"35"}}),n("v-uni-view",{staticClass:"box-item-info-jianjies"},[t._v(t._s(i.idealAspect?i.idealAspect:"暂无"))])],1),n("v-uni-view",{staticClass:"box-item-info-btn flex align-center justify-center"},[n("v-uni-view",{staticClass:"box-item-info-btn-l flex align-center justify-center",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.closeCard(i.userId)}}},[n("u-icon",{attrs:{name:"close",color:"#FFFFFF",size:"45"}})],1),n("v-uni-view",{staticClass:"box-item-info-btn-r flex align-center justify-center"},[n("u-icon",{attrs:{name:"/static/images/index/xihuan.png",color:"#FFFFFF",size:"50"}})],1)],1)],1)],1)})),0==t.lists.length?n("v-uni-view",{staticClass:"box-empty"},[n("v-uni-image",{attrs:{src:e("e003"),mode:"widthFix"}}),n("v-uni-view",{staticClass:"box-empty-txt"},[t._v("暂无数据")])],1):t._e()],2)],1)},s=[]},cbf7:function(t,i,e){"use strict";e("6a54"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var n={props:{content:{type:String,default:"暂无内容"}}};i.default=n},ce54:function(t,i,e){"use strict";e("6a54"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,e("64aa"),e("dd2b"),e("bd06"),e("aa9c");var n={props:{list:{type:Array,required:!0,default:[]},listImg:{type:Array,required:!0,default:[]},offsetX:{type:Number,default:10},offsetY:{type:Number,default:10},auto:{type:Boolean,default:!1},isQh:{type:Boolean,default:!1},dur:{type:Number,default:1e3}},data:function(){return{lists:[],currentIndex:1,animations:{},zIndexs:{},lefts:{},widths:{},running:!1,touching:!1,x:0,y:0,x1:0,y1:0}},created:function(){for(var t=this,i=0;i<this.lists.length;i++)this.$set(this.animations,"animation".concat(i+1),uni.createAnimation({duration:300,timingFunction:"ease"})),this.$set(this.widths,"zIndex".concat(i+1),-(i+1)),this.$set(this.zIndexs,"zIndex".concat(i+1),-(i+1)),this.$set(this.lefts,"left".concat(i+1),"".concat(-this.zIndexs["zIndex".concat(i+1)]*this.offsetX-this.offsetX,"px")),this.$set(this.widths,"zIndex".concat(i+1),-(i+1));this.auto&&setInterval((function(){t.running||t.touching||t.action(t.currentIndex)}),this.dur)},watch:{listImg:function(){},list:function(){this.lists=this.list;for(var t=0;t<this.lists.length;t++)this.$set(this.animations,"animation".concat(t+1),uni.createAnimation({duration:300,timingFunction:"ease"})),this.$set(this.widths,"zIndex".concat(t+1),-(t+1)),this.$set(this.zIndexs,"zIndex".concat(t+1),-(t+1)),this.$set(this.lefts,"left".concat(t+1),"".concat(-this.zIndexs["zIndex".concat(t+1)]*this.offsetX-this.offsetX,"px")),this.$set(this.widths,"zIndex".concat(t+1),-(t+1))},isQh:function(t,i){console.log(t,"1111111111"),console.log(i,"1111111111"),this.isQh&&(this.running||this.touching||this.action(this.currentIndex))}},methods:{onclickImg:function(){this.$emit("onclickImg",this.currentIndex),this.action(this.currentIndex)},move:function(t,i){this.x1=t.touches[0].pageX,this.y1=t.touches[0].pageY,-1==this.zIndexs["zIndex".concat(i)]&&this.x-this.x1>0&&(this.lefts["left".concat(i)]="".concat(-this.zIndexs["zIndex".concat(i)]*this.offsetX-this.offsetX)-(this.x-this.x1)+"px")},moveEnd:function(t,i){if(console.log(t,i),this.touching=!1,this.x-this.x1>100&&0!=this.x&&0!=this.x1){if(-1!=this.zIndexs["zIndex".concat(i)]||this.running)return;this.action(i)}else this.lefts["left".concat(i)]="".concat(-this.zIndexs["zIndex".concat(i)]*this.offsetX-this.offsetX,"px"),this.x=0,this.x1=0,this.running=!1},moveStart:function(t,i){this.touching=!0,this.x=t.touches[0].pageX,this.y=t.touches[0].pageY},action:function(t){var i=this;i.running=!0,setTimeout((function(){i.lists.splice(0,1);var t=i.listImg.findIndex((function(t){return t.dataId==i.lists[i.list.length-1].dataId}));-1!=t&&i.listImg.length>t+1&&i.lists.push(i.listImg[t+1]);for(var e=0;e<i.lists.length;e++)i.lefts["left".concat(e+1)]="".concat(-i.zIndexs["zIndex".concat(e+1)]*i.offsetX-i.offsetX,"px");for(var n=0;n<i.lists.length;n++)i.$set(i.animations,"animation".concat(n+1),uni.createAnimation({duration:300,timingFunction:"ease"}));i.running=!1,i.x=0,i.x1=0,i.$forceUpdate()}),300),i.$nextTick((function(){i.$forceUpdate()}))},slideclick:function(t,i,e){var n={item:t,index:i,currentIndex:this.currentIndex};this.$emit("slideclick",n)}}};i.default=n},d498:function(t,i,e){"use strict";var n=e("38d0"),a=e.n(n);a.a},d7d2:function(t,i){t.exports="data:image/png;base64,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"},e003:function(t,i,e){t.exports=e.p+"static/images/empty.png"},ea9e:function(t,i,e){"use strict";e.r(i);var n=e("cbf7"),a=e.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){e.d(i,t,(function(){return n[t]}))}(s);i["default"]=a.a}}]);