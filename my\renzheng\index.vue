<template>
	<view v-if="shangxianSelect != '否'">
		<view class="text-center text-red bg-white">{{form.remark}}</view>
		<view class="padding">
			<view class="text-black padding bg radius">
				<u-form :model="form" ref="uForm" label-position="top" :label-style='lableStyle'>
					<u-form-item label="真实姓名" :border-bottom='true'>
						<u-input placeholderStyle="color:#999999" :customStyle="customStyle3" placeholder="请输入真实姓名"
							v-model="form.realName" />
					</u-form-item>
					<view class="margin-tb-xl"></view>
					<u-form-item label="身份证号码" :border-bottom='false'>
						<u-input placeholder="请输入身份证号码" placeholderStyle="color:#999999" :customStyle="customStyle3"
							v-model="form.idNumber" height="60" maxlength="18"/>
					</u-form-item>
				</u-form>
			</view>
			<!-- <view class="text-white padding bg radius margin-top">
				<view class="">上传身份证正面</view>
				<view class="margin-top flex align-center justify-center"
					style="background: #F5F5F5; width: 150rpx;height: 150rpx;position: relative;">
					<view class="text-center" @click="addImages(1)" v-if="!form.idCardFront">
						<image src="../static/photo.png" mode="widthFix" style="width: 54rpx;"></image>
						<view class="text-sm text-gray margin-top-xs">添加图片</view>
					</view>
					<image @click="disabled? '':addImages(1)" v-else :src="form.idCardFront"
						style="width: 100%;height: 150rpx;"></image>
				</view>

				<view class=" margin-top">上传身份证反面</view>
				<view class="margin-top flex align-center justify-center"
					style="background: #F5F5F5; width: 150rpx;height: 150rpx;position: relative;">
					<view class="text-center" @click="addImages(2)" v-if="!form.idCardVerso">
						<image src="../static/photo.png" mode="widthFix" style="width: 54rpx;"></image>
						<view class="text-sm text-gray margin-top-xs">添加图片</view>
					</view>
					<image @click="disabled? '':addImages(2)" v-else :src="form.idCardVerso"
						style="width: 100%;height: 150rpx;"></image>
				</view>
			</view> -->


			<view style="margin-top: 60rpx;">
				<!-- <u-button v-if="!disabled" @click="submit" class="margin-top" :custom-style="customStyle" shape="square"
					:hair-line="false">提交审核</u-button> -->
					<view class="buttoms" v-if="!disabled" @click="submit">提交审核</view>
			</view>
		</view>
	</view>
</template>

<script>
	import configdata from '../../common/config.js';
	export default {
		data() {
			return {
				shangxianSelect: '否',
				form: {
					id: '',
					authType:1,
					userType: 1,
					realName: '',
					idNumber: '',
					idCardFront: '',
					idCardVerso: '',
					remark: '',
					// details: '',
					// lifePhoto: '', //生活照
					// skillImg: '' //技能照
				},
				skillImg: [],
				lifeImg: [],
				disabled: true,
				lableStyle: {
					color: '#000000',
					fontSize: '28upx'
				},
				customStyle: {
					backgroundColor: 'linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);',
					color: '#FFFFFF',
					border: 0
				},
				customStyle3: {
					color: '#000000',
					border: 0
				},
				count: ''
			}
		},
		onLoad() {
			// #ifdef MP-WEIXIN
			this.shangxianSelect = this.$queue.getData('shangxianSelect');
			// #endif
			// #ifndef MP-WEIXIN
			this.shangxianSelect = '是';
			// #endif
			this.getUserInfo()
		},
		methods: {
			// 上传主页图删除
			removeImg(index, ind) {
				if (ind == 1) {
					this.skillImg.splice(index, 1)
				} else if (ind == 2) {
					this.lifeImg.splice(index, 1)
				}
			},
			submit() {
				console.log(this.form)
				this.form.skillImg = this.skillImg
				this.form.skillImg = this.form.skillImg.toString()
				this.form.lifePhoto = this.lifeImg
				this.form.lifePhoto = this.form.lifePhoto.toString()
				if (!this.form.realName) {
					uni.showToast({
						title: '请输入真实姓名',
						icon: 'none',
						duration: 1000
					})
					return;
				}
				if (!this.form.idNumber) {
					uni.showToast({
						title: '请输入身份证号',
						icon: 'none',
						duration: 1000
					})
					return;
				}
				let regX = /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
				if (!regX.test(this.form.idNumber)) {
					uni.showToast({
						title: '请输入正确的身份证号',
						icon: 'none',
						duration: 1000
					})
					return;
				}
				// if (!this.form.idCardFront) {
				// 	uni.showToast({
				// 		title: '请上传身份证正面',
				// 		icon: 'none',
				// 		duration: 1000
				// 	})
				// 	return;
				// }
				// if (!this.form.idCardVerso) {
				// 	uni.showToast({
				// 		title: '请上传身份证反面',
				// 		icon: 'none',
				// 		duration: 1000
				// 	})
				// 	return;
				// }
				uni.showLoading({
					title: '提交中...',
				})
				this.$Request.postJson("/app/userCertification/saveUserCertification", this.form).then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						uni.showToast({
							title: '认证提交成功！',
							icon: 'none'
						})
						setTimeout(function() {
							uni.navigateBack()
						}, 1000)
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					}
					
				});
			},
			getUserInfo() {
				this.$Request.get("/app/userCertification/getMyUserCertification",{
					authType:1
				}).then(res => {
					if (res.code == 0 && res.data) {
						this.form.realName = res.data.realName ? res.data.realName : ''
						this.form.idNumber = res.data.idNumber ? res.data.idNumber : ''
						this.form.idCardFront = res.data.idCardFront ? res.data.idCardFront : ''
						this.form.idCardVerso = res.data.idCardVerso ? res.data.idCardVerso : ''
						this.form.id = res.data.id ? res.data.id : ''
						
						if (res.data.isSubmit == 0) {
							if (res.data.status == 0 || res.data.status == 1) {
								this.disabled = true
							}else {
								this.disabled = false
							}
						} else {
							this.disabled = true
						}

						if (res.data.status == 2) {
							this.form.remark = res.data.remark ? res.data.remark : ''
						}
					}else{
						this.disabled = false
					}
				});
			},
			addImages(e) {
				let that = this
				uni.chooseImage({
					count: 1,
					sourceType: ['album', 'camera'],
					success: res => {
						for (let i = 0; i < 1; i++) {
							this.$queue.showLoading("上传中...");
							uni.uploadFile({ // 上传接口
								url: that.config("APIHOST1") + '/alioss/upload', //真实的接口地址
								filePath: res.tempFilePaths[i],
								name: 'file',
								success: (uploadFileRes) => {
									if (e == 1) {
										this.form.idCardFront = JSON.parse(uploadFileRes.data).data
									} else {
										this.form.idCardVerso = JSON.parse(uploadFileRes.data).data
									}

									uni.hideLoading();
								}
							});
						}
					}
				})
			},
			config: function(name) {
				var info = null;
				if (name) {
					var name2 = name.split("."); //字符分割
					if (name2.length > 1) {
						info = configdata[name2[0]][name2[1]] || null;
					} else {
						info = configdata[name] || null;
					}
					if (info == null) {
						let web_config = cache.get("web_config");
						if (web_config) {
							if (name2.length > 1) {
								info = web_config[name2[0]][name2[1]] || null;
							} else {
								info = web_config[name] || null;
							}
						}
					}
				}
				return info;
			},
		}
	}
</script>

<style scoped>
	.bg {
		background-color: #FFFFFF;
	}

	/deep/.u-form-item {
		padding: 0 !important;
		line-height: 10px !important;
	}
	.buttoms{
		background: linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);
		padding: 20rpx 0;
		border-radius: 12rpx;
		color: #FFFFFF;
		text-align: center;
		
	}
</style>