(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["my-gird-mylike"],{2601:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){}));var a=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticClass:"page-box"},[e("v-uni-view",{staticClass:"centre"},[e("v-uni-image",{attrs:{src:n("e003"),mode:""}}),e("v-uni-view",{staticClass:"tips"},[this._v(this._s(this.content))])],1)],1)},i=[]},"2bdc":function(t,e,n){"use strict";n.r(e);var a=n("2601"),i=n("ea9e");for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);n("6594");var o=n("828b"),s=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"4fb1bbd1",null,!1,a["a"],void 0);e["default"]=s.exports},"2db5":function(t,e,n){"use strict";var a=n("4329"),i=n.n(a);i.a},"2f3f":function(t,e,n){"use strict";n.r(e);var a=n("32e0"),i=n("8346");for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);n("2db5");var o=n("828b"),s=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"046b370f",null,!1,a["a"],void 0);e["default"]=s.exports},"30f7":function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},n("7a76"),n("c9b5")},"32e0":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){return a}));var a={uIcon:n("3688").default},i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",[0!=t.list.length?n("v-uni-view",{staticClass:"margin-lr flex flex-wrap margin-top justify-between"},t._l(t.list,(function(e,a){return e.userData?n("v-uni-view",{key:a,staticClass:"likebox",on:{click:function(n){n.stopPropagation(),arguments[0]=n=t.$handleEvent(n),t.godetail(e)}}},[n("v-uni-image",{attrs:{src:e.userData.userImg?e.userData.userImg:"../../static/logo.png"}}),n("v-uni-view",{staticClass:"padding-tb-xs"},[n("v-uni-view",{staticClass:"title"},[t._v(t._s(e.userData.realName)),1==e.userData.sex?n("v-uni-view",{staticClass:"sex"},[n("u-icon",{attrs:{name:"man",color:"#FFFFFF",size:"20"}})],1):t._e(),2==e.userData.sex?n("v-uni-view",{staticClass:"sex"},[n("u-icon",{attrs:{name:"woman",color:"#FFFFFF",size:"20"}})],1):t._e()],1),n("v-uni-view",{staticClass:"tit"},[t._v(t._s(e.userData.age)+"岁/"+t._s(e.userData.userHeight)+"cm/"),"市辖区"!=e.userData.locationCity?n("v-uni-text",[t._v(t._s(e.userData.locationCity))]):n("v-uni-text",[t._v(t._s(e.userData.locationProvince))])],1),n("v-uni-view",{staticStyle:{width:"100%",height:"1px",background:"#F2F2F2"}}),1!=e.isGetPhone?n("v-uni-view",{staticClass:"chat"},[n("u-icon",{attrs:{name:"chat-fill",color:"#6367FF",size:"40"}}),n("v-uni-text",[t._v("获取联系方式")])],1):n("v-uni-view",{staticClass:"chat",on:{click:function(n){n.stopPropagation(),arguments[0]=n=t.$handleEvent(n),t.callPhone(e)}}},[n("u-icon",{attrs:{name:"chat-fill",color:"#6367FF",size:"40"}}),n("v-uni-text",[t._v("联系TA")])],1)],1)],1):t._e()})),1):t._e(),0==t.list.length?n("empty"):t._e()],1)},r=[]},4329:function(t,e,n){var a=n("7fa5");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=n("967d").default;i("643d0ba8",a,!0,{sourceMap:!1,shadowMode:!1})},4733:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if(Array.isArray(t))return(0,a.default)(t)};var a=function(t){return t&&t.__esModule?t:{default:t}}(n("8d0b"))},6594:function(t,e,n){"use strict";var a=n("6ee7"),i=n.n(a);i.a},"6ee7":function(t,e,n){var a=n("bafb");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=n("967d").default;i("72fad1e5",a,!0,{sourceMap:!1,shadowMode:!1})},"7fa5":function(t,e,n){var a=n("c86c");e=a(!1),e.push([t.i,"uni-page-body[data-v-046b370f]{background:#f2f6fc}body.?%PAGE?%[data-v-046b370f]{background:#f2f6fc}.likebox[data-v-046b370f]{width:%?333?%;background:#fff;border-radius:%?16?% %?16?% %?16?% %?24?%}.likebox uni-image[data-v-046b370f]{width:%?333?%;height:%?340?%;border-radius:%?16?% %?16?% %?0?% %?0?%}.likebox .title[data-v-046b370f]{font-size:%?32?%;font-family:PingFang SC;font-weight:700;color:#292929;padding:0 %?20?%;display:flex;align-items:center;max-width:%?200?%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.likebox .title .sex[data-v-046b370f]{width:%?32?%;height:%?32?%;background:#38caff;border-radius:%?16?%;display:flex;align-items:center;justify-content:center;margin-left:%?10?%}.likebox .title .sexs[data-v-046b370f]{width:%?32?%;height:%?32?%;background:#fbe2f4;border-radius:%?16?%;display:flex;align-items:center;justify-content:center;margin-left:%?10?%}.likebox .tit[data-v-046b370f]{font-size:%?26?%;font-family:PingFang SC;font-weight:500;color:#999;padding:%?10?% %?20?%}.likebox .chat[data-v-046b370f]{font-size:%?26?%;font-family:PingFang SC;padding-bottom:%?10?%;color:#686cff;display:flex;align-items:center;justify-content:center;margin:%?20?% 0}.likebox .chat uni-text[data-v-046b370f]{margin-left:%?8?%}",""]),t.exports=e},8346:function(t,e,n){"use strict";n.r(e);var a=n("fd64"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=i.a},b7c7:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,a.default)(t)||(0,i.default)(t)||(0,r.default)(t)||(0,o.default)()};var a=s(n("4733")),i=s(n("d14d")),r=s(n("5d6b")),o=s(n("30f7"));function s(t){return t&&t.__esModule?t:{default:t}}},bafb:function(t,e,n){var a=n("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.page-box[data-v-4fb1bbd1]{position:relative;height:100vh;background:#fff}.centre[data-v-4fb1bbd1]{position:absolute;left:0;top:0;right:0;bottom:0;margin:auto;height:%?400?%;text-align:center;font-size:%?32?%}.centre uni-image[data-v-4fb1bbd1]{width:%?414?%;height:%?269?%;margin:0 auto %?20?%}.centre .tips[data-v-4fb1bbd1]{font-size:%?32?%;color:#999;margin-top:%?20?%}.centre .btn[data-v-4fb1bbd1]{margin:%?80?% auto;width:%?600?%;border-radius:%?32?%;line-height:%?90?%;color:#fff;font-size:%?34?%;background:#7075fe}',""]),t.exports=e},cbf7:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={props:{content:{type:String,default:"暂无内容"}}};e.default=a},d14d:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)},n("01a2"),n("e39c"),n("bf0f"),n("844d"),n("18f7"),n("de6c"),n("08eb")},e003:function(t,e,n){t.exports=n.p+"static/images/empty.png"},ea9e:function(t,e,n){"use strict";n.r(e);var a=n("cbf7"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=i.a},fd64:function(t,e,n){"use strict";n("6a54");var a=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=a(n("b7c7"));n("bf0f"),n("2797"),n("c223");var r=a(n("2bdc")),o={components:{empty:r.default},data:function(){return{list:[],count:0,page:1,limit:10}},onLoad:function(){},onShow:function(){this.getlist()},methods:{callPhone:function(t){this.$Request.getT("/app/userGetPhoneRecord/getPostPushPhone?userId="+t.userData.userId).then((function(t){if(0==t.code){var e=t.data;uni.makePhoneCall({phoneNumber:e})}}))},godetail:function(t){uni.navigateTo({url:"/package/pages/game/detail?byUserId="+t.userData.userId})},getlist:function(){var t=this;uni.getStorageSync("userId");this.$queue.showLoading("加载中...");var e={page:this.page,limit:this.limit,type:2};this.$Request.getT("/app/scFollow/getMyScFollowList",e).then((function(e){0===e.code&&e.data&&(e.data.records.forEach((function(t){if(t.userData&&t.userData.userImg){var e=t.userData.userImg.split(",");t.userData.userImg=e[0]}})),1==t.page?t.list=e.data.records:t.list=[].concat((0,i.default)(t.list),(0,i.default)(e.data.records)),t.count=e.data.totalCount),uni.stopPullDownRefresh(),uni.hideLoading()}))}},onReachBottom:function(){this.list.length==this.count?uni.showToast({title:"已经到底了",icon:"none"}):(this.page=this.page+1,this.getlist())},onPullDownRefresh:function(){this.page=1,this.getlist()}};e.default=o}}]);