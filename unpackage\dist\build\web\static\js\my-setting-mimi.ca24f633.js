(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["my-setting-mimi"],{3780:function(t,n,e){"use strict";e.r(n);var a=e("6ab2"),i=e("800f");for(var o in i)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(o);e("6a37");var u=e("828b"),c=Object(u["a"])(i["default"],a["b"],a["c"],!1,null,"ee532da4",null,!1,a["a"],void 0);n["default"]=c.exports},"5aca":function(t,n,e){var a=e("c86c");n=a(!1),n.push([t.i,"uni-page-body[data-v-ee532da4]{background:#fff}body.?%PAGE?%[data-v-ee532da4]{background:#fff}",""]),t.exports=n},"6a37":function(t,n,e){"use strict";var a=e("f15c"),i=e.n(a);i.a},"6ab2":function(t,n,e){"use strict";e.d(n,"b",(function(){return a})),e.d(n,"c",(function(){return i})),e.d(n,"a",(function(){}));var a=function(){var t=this.$createElement,n=this._self._c||t;return n("v-uni-view",{staticClass:"home1",staticStyle:{"font-size":"14px","line-height":"26px",padding:"32upx",color:"#000000"}},[n("v-uni-view",{staticStyle:{color:"#000000","font-size":"28upx"},domProps:{innerHTML:this._s(this.content)}})],1)},i=[]},7719:function(t,n,e){"use strict";e("6a54"),Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;n.default={data:function(){return{content:""}},onLoad:function(){this.getGuize()},methods:{getGuize:function(){var t=this;this.$Request.getT("/app/common/type/176").then((function(n){0===n.code&&(t.content=n.data.value)}))}}}},"800f":function(t,n,e){"use strict";e.r(n);var a=e("7719"),i=e.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(o);n["default"]=i.a},f15c:function(t,n,e){var a=e("5aca");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=e("967d").default;i("0b64aec6",a,!0,{sourceMap:!1,shadowMode:!1})}}]);