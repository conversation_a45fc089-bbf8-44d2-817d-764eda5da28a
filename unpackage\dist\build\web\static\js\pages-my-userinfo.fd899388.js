(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-my-userinfo"],{"0822":function(e,a,t){"use strict";t.r(a);var n=t("13aa"),i=t("704d");for(var o in i)["default"].indexOf(o)<0&&function(e){t.d(a,e,(function(){return i[e]}))}(o);t("6892");var u=t("828b"),s=Object(u["a"])(i["default"],n["b"],n["c"],!1,null,"22eb0cee",null,!1,n["a"],void 0);a["default"]=s.exports},"0eeb":function(e,a,t){e.exports=t.p+"static/logo.png"},"13aa":function(e,a,t){"use strict";t.d(a,"b",(function(){return i})),t.d(a,"c",(function(){return o})),t.d(a,"a",(function(){return n}));var n={uModal:t("7e01").default},i=function(){var e=this,a=e.$createElement,n=e._self._c||a;return n("v-uni-view",[n("v-uni-view",{staticClass:"usermain"},[n("v-uni-view",{staticClass:"usermain-item "},[n("v-uni-view",[e._v("头像")]),n("v-uni-view",{on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.uploadImg()}}},[null==e.avatar?n("v-uni-image",{staticStyle:{width:"111rpx",height:"111rpx","border-radius":"50%"},attrs:{src:t("0eeb"),mode:""}}):n("v-uni-image",{staticStyle:{width:"111rpx",height:"111rpx","border-radius":"50%"},attrs:{src:e.avatar,mode:""}})],1)],1),n("v-uni-view",{staticClass:"usermain-item item-padding "},[n("v-uni-view",[e._v("用户名")]),n("v-uni-view",[n("v-uni-view",{staticClass:"cu-form-group"},[n("v-uni-input",{attrs:{type:"nickname",placeholder:"请输入用户名"},model:{value:e.userName,callback:function(a){e.userName=a},expression:"userName"}})],1)],1)],1),n("v-uni-view",{staticClass:"usermain-item item-padding "},[n("v-uni-view",[e._v("手机")]),n("v-uni-view",[n("v-uni-view",{staticClass:"cu-form-group"},[n("v-uni-input",{attrs:{disabled:e.disableds,placeholder:"请输入联系电话"},model:{value:e.phone,callback:function(a){e.phone=a},expression:"phone"}})],1)],1)],1)],1),n("v-uni-view",{staticClass:"footer-btn"},[n("v-uni-view",{staticClass:"usermain-btn",on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.messagebtn()}}},[e._v("保存")])],1),n("u-modal",{attrs:{content:e.meContent,title:e.meTitle,"show-cancel-button":e.meShowCancel,"confirm-text":e.meConfirmText,"cancel-text":e.meCancelText},on:{cancel:function(a){arguments[0]=a=e.$handleEvent(a),e.meHandleClose.apply(void 0,arguments)},confirm:function(a){arguments[0]=a=e.$handleEvent(a),e.meHandleBtn.apply(void 0,arguments)}},model:{value:e.meShowModel,callback:function(a){e.meShowModel=a},expression:"meShowModel"}})],1)},o=[]},6892:function(e,a,t){"use strict";var n=t("726f"),i=t.n(n);i.a},"704d":function(e,a,t){"use strict";t.r(a);var n=t("eec5"),i=t.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){t.d(a,e,(function(){return n[e]}))}(o);a["default"]=i.a},"726f":function(e,a,t){var n=t("c43c");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var i=t("967d").default;i("408e4460",n,!0,{sourceMap:!1,shadowMode:!1})},c43c:function(e,a,t){var n=t("c86c");a=n(!1),a.push([e.i,"uni-page-body[data-v-22eb0cee]{background:#fff}body.?%PAGE?%[data-v-22eb0cee]{background:#fff}uni-button[data-v-22eb0cee]::after{border:none}.usermain[data-v-22eb0cee]{background:#fff;color:#333}.usermain-item[data-v-22eb0cee]{display:flex;align-items:center;margin:0 %?40?%;padding:%?10?% 0;justify-content:space-between;border-bottom:%?1?% solid #e8e8e8\n\t/* border-bottom: 2rpx solid #f2f2f2; */}.usermain-item.item-padding[data-v-22eb0cee]{\n\t/* padding: 0; */}.cu-form-group[data-v-22eb0cee]{padding:0;background:#fff;text-align:right}.cu-form-group uni-input[data-v-22eb0cee]{background:#fff;font-size:%?28?%;color:#000}.footer-btn[data-v-22eb0cee]{margin-top:%?150?%}.footer-btn .usermain-btn[data-v-22eb0cee]{color:#fff;background:#7075fe;text-align:center;width:%?450?%;height:%?80?%;font-size:%?28?%;line-height:%?80?%;margin:0 auto;border-radius:%?40?%}",""]),e.exports=a},eec5:function(e,a,t){"use strict";t("6a54");var n=t("f5bd").default;Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var i=n(t("b741")),o={data:function(){return{meShowModel:!1,meShowCancel:!0,meTitle:"提示",meContent:"",meConfirmText:"确认",meCancelText:"取消",meIndex:"",phone:"",avatar:"../../static/logo.png",userName:"",nickName:"",userId:"",realName:"",weChatId:"",password:"",platform:"",createTime:"",money:"",jiFen:"",status:"",zhiFuBao:"",zhiFuBaoName:"",sex:1,age:0,disabled:!0,disableds:!1}},onLoad:function(e){var a=this;this.getUserInfo(),this.$Request.getT("/app/common/type/188").then((function(e){0==e.code&&e.data&&e.data.value&&("是"==e.data.value?a.disableds=!0:a.disableds=!1)}))},methods:{onChooseAvatar:function(e){console.log(e.detail.avatarUrl);var a=this,t=uni.getStorageSync("token");uni.showLoading({title:"上传中..."});uni.getStorageSync("userId");uni.uploadFile({url:a.config("APIHOST1")+"/alioss/upload",filePath:e.detail.avatarUrl,header:{token:t},name:"file",success:function(e){var t=JSON.parse(e.data);a.avatar=t.data,uni.hideLoading()}})},goMyAddress:function(){uni.navigateTo({url:"../jifen/myaddress"})},uploadImg:function(){var e=uni.getStorageSync("token");if(e){var a=this,t=null;uni.chooseImage({count:1,sizeType:["original","compressed"],sourceType:["album"],success:function(e){uni.showLoading({title:"上传中..."});var n=uni.getStorageSync("token");uni.getStorageSync("userId");uni.uploadFile({url:a.config("APIHOST1")+"/alioss/upload",filePath:e.tempFilePaths[0],header:{token:n},name:"file",success:function(e){t=JSON.parse(e.data),a.avatar=t.data,uni.hideLoading()}})}})}else this.goLoginInfo()},config:function(e){var a=null;if(e){var t=e.split(".");if(a=t.length>1?i.default[t[0]][t[1]]||null:i.default[e]||null,null==a){var n=cache.get("web_config");n&&(a=t.length>1?n[t[0]][t[1]]||null:n[e]||null)}}return a},getUserInfo:function(){var e=this;uni.getStorageSync("userId");this.$Request.get("/app/user/selectUserById").then((function(a){0==a.code&&(e.$queue.setData("avatar",a.data.avatar),e.$queue.setData("userId",a.data.userId),e.$queue.setData("userName",a.data.userName),e.$queue.setData("phone",a.data.phone),e.phone=a.data.phone,e.avatar=a.data.avatar,e.userName=a.data.userName,null==e.userName?e.userName=a.data.nickName:e.userName=a.data.userName),uni.hideLoading()}))},meHandleBtn:function(){"m1"==this.meIndex&&this.$Request.postJson("/app/user/updateUser",{userName:this.userName,avatar:this.avatar,phone:this.phone,sex:this.sex,age:this.age}).then((function(e){0===e.code?(uni.showToast({title:"保存成功",icon:"none"}),setTimeout((function(){uni.navigateBack()}),1e3)):uni.showToast({title:e.msg,icon:"none"})}))},meHandleClose:function(){this.meIndex},messagebtn:function(){this.avatar?this.phone?(this.meShowModel=!0,this.meTitle="温馨提示",this.meContent="确定保存信息？",this.meIndex="m1",this.meShowCancel=!0):uni.showToast({title:"联系电话不能为空",icon:"none"}):uni.showToast({title:"头像不能为空",icon:"none"})}}};a.default=o}}]);