(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["package-pages-detail-goreport"],{2601:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticClass:"page-box"},[e("v-uni-view",{staticClass:"centre"},[e("v-uni-image",{attrs:{src:a("e003"),mode:""}}),e("v-uni-view",{staticClass:"tips"},[this._v(this._s(this.content))])],1)],1)},i=[]},"2bdc":function(t,e,a){"use strict";a.r(e);var n=a("2601"),i=a("ea9e");for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);a("6594");var s=a("828b"),c=Object(s["a"])(i["default"],n["b"],n["c"],!1,null,"4fb1bbd1",null,!1,n["a"],void 0);e["default"]=c.exports},"3b17":function(t,e,a){var n=a("5763");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("967d").default;i("777b1420",n,!0,{sourceMap:!1,shadowMode:!1})},5763:function(t,e,a){var n=a("c86c");e=n(!1),e.push([t.i,"uni-page-body[data-v-ff63b5ec]{background-color:#f5f5f5}body.?%PAGE?%[data-v-ff63b5ec]{background-color:#f5f5f5}.list-item[data-v-ff63b5ec]{background-color:#fff;border-radius:%?16?%;margin:%?20?% %?20?% %?20?% %?20?%}.img1 uni-image[data-v-ff63b5ec]{width:100%;height:%?400?%}.img2[data-v-ff63b5ec]{display:flex;flex-wrap:wrap}.img2 uni-image[data-v-ff63b5ec]{width:%?210?%;height:%?210?%;margin-top:%?15?%;margin-right:%?5?%;margin-left:%?15?%}",""]),t.exports=e},"58c0":function(t,e,a){"use strict";a("6a54");var n=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("c223"),a("bf0f"),a("2797"),a("aa9c");var i=n(a("2bdc")),o={components:{empty:i.default},data:function(){return{meShowModel:!1,meShowCancel:!0,meTitle:"提示",meContent:"",meConfirmText:"确认",meCancelText:"取消",meIndex:"",infoId:"",showFlag:!0,page:1,size:5,userId:"",type:3,loadingType:0,scrollTop:!1,matchdata:[],totalPage:0,contentText:{contentdown:"上拉显示更多",contentrefresh:"正在加载...",contentnomore:"没有更多数据了"}}},onLoad:function(){},onShow:function(){this.showFlag&&(uni.showLoading({title:"加载中..."}),this.mymatch())},onPullDownRefresh:function(){this.page=1,this.mymatch(this.tabIndex)},onReachBottom:function(){this.page<this.totalPage&&(this.page=this.page+1,this.mymatch(this.tabIndex))},methods:{saveImg:function(t,e){this.showFlag=!1;var a=t;uni.previewImage({urls:a,current:a[e]})},mymatch:function(){var t=this;this.loadingType=1;this.$queue.getData("userId");this.$Request.getT("/app/message/selectMessageByUserId?page=".concat(this.page,"&limit=").concat(this.size,"&state=3")).then((function(e){uni.stopPullDownRefresh(),uni.hideLoading(),0===e.code?(t.totalPage=e.data.totalPage,1===t.page&&(t.matchdata=[]),e.data.list.forEach((function(e){e.image&&(e.image=e.image.split(",")),t.matchdata.push(e)})),e.data.list.length===t.size?t.loadingType=0:t.loadingType=3):(t.loadingType=2,uni.hideLoading())}))},meHandleBtn:function(){var t=this;"m1"==this.meIndex&&t.$Request.getT("/userReport/delete?id=".concat(t.infoId)).then((function(e){0===e.status&&(t.$queue.showToast("删除成功"),t.page=1,t.matchdata=[],t.mymatch())}))},meHandleClose:function(){this.meIndex},disposeDelete:function(t){this.meShowModel=!0,this.meTitle="删除提醒",this.meContent="确定要删除此条信息？",this.meIndex="m1",this.infoId=t.id}}};e.default=o},6594:function(t,e,a){"use strict";var n=a("6ee7"),i=a.n(n);i.a},"6ee7":function(t,e,a){var n=a("bafb");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("967d").default;i("72fad1e5",n,!0,{sourceMap:!1,shadowMode:!1})},bad0:function(t,e,a){"use strict";var n=a("3b17"),i=a.n(n);i.a},bafb:function(t,e,a){var n=a("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.page-box[data-v-4fb1bbd1]{position:relative;height:100vh;background:#fff}.centre[data-v-4fb1bbd1]{position:absolute;left:0;top:0;right:0;bottom:0;margin:auto;height:%?400?%;text-align:center;font-size:%?32?%}.centre uni-image[data-v-4fb1bbd1]{width:%?414?%;height:%?269?%;margin:0 auto %?20?%}.centre .tips[data-v-4fb1bbd1]{font-size:%?32?%;color:#999;margin-top:%?20?%}.centre .btn[data-v-4fb1bbd1]{margin:%?80?% auto;width:%?600?%;border-radius:%?32?%;line-height:%?90?%;color:#fff;font-size:%?34?%;background:#7075fe}',""]),t.exports=e},c617:function(t,e,a){"use strict";a.r(e);var n=a("58c0"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=i.a},cbf7:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={props:{content:{type:String,default:"暂无内容"}}};e.default=n},e003:function(t,e,a){t.exports=a.p+"static/images/empty.png"},ea9e:function(t,e,a){"use strict";a.r(e);var n=a("cbf7"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=i.a},f5b5:function(t,e,a){"use strict";a.r(e);var n=a("f9be"),i=a("c617");for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);a("bad0");var s=a("828b"),c=Object(s["a"])(i["default"],n["b"],n["c"],!1,null,"ff63b5ec",null,!1,n["a"],void 0);e["default"]=c.exports},f9be:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return n}));var n={uModal:a("7e01").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"bonus-main"},[t._l(t.matchdata,(function(e,n){return t.matchdata.length>0?a("v-uni-view",{key:n,staticClass:"list-item",attrs:{"show-arrow":!1}},[a("v-uni-view",{staticClass:"list-item-wrap ",class:e.image&&e.image.length>1?"img2":"img1",staticStyle:{"background-color":"#FFFFFF"}},[t._l(e.image,(function(n,i){return a("v-uni-view",{key:i,class:e.image.length>1?"img2":"img1",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.saveImg(e.image,i)}}},[a("v-uni-image",{attrs:{src:n,mode:"aspectFill"}})],1)})),a("v-uni-view",{staticStyle:{padding:"20rpx 30rpx","background-color":"#FFFFFF"}},[a("v-uni-view",{staticClass:"list-title",staticStyle:{color:"#000000"}},[t._v("投诉内容："+t._s(e.content))]),a("v-uni-view",{staticClass:"list-title",staticStyle:{"margin-top":"10upx",color:"#000000"}},[t._v("被投诉人："+t._s(e.byUserName))]),a("v-uni-view",{staticStyle:{display:"flex","justify-content":"space-between","margin-top":"10rpx"}},[a("v-uni-view",[a("v-uni-text",{staticStyle:{color:"#000000"}},[t._v("投诉时间："+t._s(e.createAt))])],1)],1),0==e.status?a("v-uni-view",{staticClass:"list-title",staticStyle:{"margin-top":"10upx",color:"#000000"}},[t._v("状态：待处理")]):t._e(),1==e.status?a("v-uni-view",{staticClass:"list-title",staticStyle:{"margin-top":"10upx",color:"#000000"}},[t._v("状态：已驳回")]):t._e(),2==e.status?a("v-uni-view",{staticClass:"list-title",staticStyle:{"margin-top":"10upx",color:"#000000"}},[t._v("状态：已封号")]):t._e(),3==e.status?a("v-uni-view",{staticClass:"list-title",staticStyle:{"margin-top":"10upx",color:"#000000"}},[t._v("状态：已删除")]):t._e(),1==e.status?a("v-uni-view",{staticClass:"list-title",staticStyle:{"margin-top":"10upx",color:"#000000"}},[t._v("驳回内容："+t._s(e.auditContent))]):t._e(),4==e.status?a("v-uni-view",{staticClass:"list-title",staticStyle:{"margin-top":"10upx",color:"#000000"}},[t._v("处理内容："+t._s(e.auditContent))]):t._e()],1)],2)],1):t._e()})),t.matchdata.length>0?a("v-uni-view",{staticClass:"s-col is-col-24"},[a("load-more",{attrs:{status:t.loadingType,contentText:t.contentText}})],1):t._e(),0==t.matchdata.length?a("empty",{attrs:{des:"暂无数据"}}):t._e(),a("u-modal",{attrs:{content:t.meContent,title:t.meTitle,"show-cancel-button":t.meShowCancel,"confirm-text":t.meConfirmText,"cancel-text":t.meCancelText},on:{cancel:function(e){arguments[0]=e=t.$handleEvent(e),t.meHandleClose.apply(void 0,arguments)},confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.meHandleBtn.apply(void 0,arguments)}},model:{value:t.meShowModel,callback:function(e){t.meShowModel=e},expression:"meShowModel"}})],2)},o=[]}}]);