(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["my-setting-about"],{"08f3":function(t,n,e){var o=e("9b6b");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var a=e("967d").default;a("f24b2dce",o,!0,{sourceMap:!1,shadowMode:!1})},1140:function(t,n,e){"use strict";e.d(n,"b",(function(){return o})),e.d(n,"c",(function(){return a})),e.d(n,"a",(function(){}));var o=function(){var t=this.$createElement,n=this._self._c||t;return n("v-uni-view",{staticClass:"home1",staticStyle:{"font-size":"14px","line-height":"26px",padding:"32upx",color:"#000000"}},[n("v-uni-view",{staticStyle:{color:"#000000","font-size":"28upx"},domProps:{innerHTML:this._s(this.content)}})],1)},a=[]},1963:function(t,n,e){"use strict";e("6a54"),Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;n.default={data:function(){return{content:""}},onLoad:function(){this.getGuize()},methods:{getGuize:function(){var t=this;this.$Request.getT("/app/common/type/187").then((function(n){0===n.code&&(t.content=n.data.value)}))}}}},"8d12":function(t,n,e){"use strict";var o=e("08f3"),a=e.n(o);a.a},"9b6b":function(t,n,e){var o=e("c86c");n=o(!1),n.push([t.i,"uni-page-body[data-v-4e469b4a]{background:#fff}body.?%PAGE?%[data-v-4e469b4a]{background:#fff}",""]),t.exports=n},c18d:function(t,n,e){"use strict";e.r(n);var o=e("1140"),a=e("d1ca");for(var i in a)["default"].indexOf(i)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(i);e("8d12");var u=e("828b"),c=Object(u["a"])(a["default"],o["b"],o["c"],!1,null,"4e469b4a",null,!1,o["a"],void 0);n["default"]=c.exports},d1ca:function(t,n,e){"use strict";e.r(n);var o=e("1963"),a=e.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){e.d(n,t,(function(){return o[t]}))}(i);n["default"]=a.a}}]);