<template>
	<view style="padding-bottom: 180rpx;">
		<!-- <u-navbar :background="background"></u-navbar> -->

		<view v-if="Number(formData.isShow) == 1">
			<swiper class="screen-swiper" style="height: 759rpx;" :circular="true" :autoplay="true" interval="5000"
				duration="800">
				<swiper-item v-for="(item,index) in formData.userImg" :key="index"
					@click="priveImgs(index,formData.userImg)">
					<image :src="item" mode=""></image>
				</swiper-item>
			</swiper>
			<!-- <image src="../../../static/logo.png" mode="widthFix" style="width: 100%;max-height: 759rpx;"></image> -->
		</view>

		<view class="">
			<view class="padding">
				<view class="flex align-center justify-between">
					<view class="flex align-center ">
						<view class="yhm">{{formData.realName}}</view>
						<image src="../../../static/images/my/rzicon.png" style="width: 40rpx;height: 40rpx;"></image>
					</view>
					<view class="wezhi" v-if="formData.locationCity">
						<text v-if="formData.locationCity!='市辖区'"> {{formData.locationCity}}</text>
						<text v-else> {{formData.locationProvince}}</text>
						{{formData.locationCounty}}
					</view>
				</view>
				<view class="flex align-center margin-top-sm">
					<view class="sexicon" v-if="formData.sex==1">
						<u-icon name="man" color="#FFFFFF"></u-icon>
						{{formData.age}}岁
					</view>
					<view class="sexicons" v-if="formData.sex==2">
						<u-icon name="woman" color="#FFFFFF"></u-icon>
						{{formData.age}}岁
					</view>
					<view class="labe">{{formData.education}}</view>
					<view class="labe" v-if="formData.marriageStatus==1">未婚</view>
					<view class="labe" v-if="formData.marriageStatus==2">离异</view>
					<view class="labe" v-if="formData.userHeight">{{formData.userHeight}}CM</view>
				</view>
				<!-- <view class="remk">
					<u-icon name="/static/images/index/xinxin.png" color="#FF749F" size="35"></u-icon>
					<text>{{formData.idealAspect}}</text>
				</view> -->
			</view>

			<block v-if="formData.myIntro">
				<view style="width: 100%;height: 1rpx;background: #E6E6E6;"></view>
				<view class="padding">
					<view class="title">关于我</view>
					<view class="margin-top-xs">
						{{formData.myIntro}}
					</view>
				</view>
			</block>

			<view style="width: 100%;height: 1rpx;background: #E6E6E6;"></view>
			<view class="padding" v-if="dongta.length!=0">
				<view class="flex align-center justify-between">
					<view class="title">我的动态</view>
					<view class="text-sm" style="color: #999999;" @click="godetail">查看全部</view>
				</view>

				<view class="margin-top-sm flex align-center flex-wrap">
					<view v-for="(item,index) in dongta" :key="index" class="margin-right">
						<view class="flex">
							<view class="margin-right-sm" v-for="(ite,ind) in item.trendsImage" :key="ind"
								@click="priveImgs(ind,item.trendsImage)">
								<image :src="ite?ite:'../../../static/logo.png'"
									style="width: 160rpx;height: 160rpx;border-radius: 24rpx;">
								</image>
							</view>
						</view>
						<!-- <image :src="item.trendsImage?item.trendsImage:'../../../static/logo.png'"
							style="width: 160rpx;height: 160rpx;border-radius: 24rpx;">
						</image> -->
					</view>
				</view>
				<empty v-if="dongta.length==0" style="height: 30vh;" content="暂无动态"></empty>
			</view>

			<view class="padding">
				<view class="title">基本资料</view>
				<view class="flex align-center margin-top-xs flex-wrap">
					<view class="label" v-if="formData.userHeight">
						<image src="../static/shengao.png" style="width: 38rpx;height:38rpx;"></image>
						<text style="margin-left: 6rpx;">{{formData.userHeight}}cm</text>
					</view>

					<view class="label" v-if="formData.starSign">
						<image src="../static/xingzuo.png" style="width: 38rpx;height:38rpx;"></image>
						<text style="margin-left: 6rpx;">{{formData.starSign}}</text>
					</view>
					<view class="label" v-if="formData.userWeight">
						<image src="../static/tizhong.png" style="width: 38rpx;height:38rpx;"></image>
						<text style="margin-left: 6rpx;">{{formData.userWeight}}kg</text>
					</view>
					<view class="label" v-if="formData.education">
						<image src="../static/xueli.png" style="width: 38rpx;height:38rpx;"></image>
						<text style="margin-left: 6rpx;">{{formData.education}}</text>
					</view>
					<view class="label" v-if="formData.career">
						<image src="../static/zhichang.png" style="width: 38rpx;height:38rpx;"></image>
						<text style="margin-left: 6rpx;">职业：{{formData.career}}</text>
					</view>
					<view class="label">
						<image src="../static/yuexin.png" style="width: 38rpx;height:38rpx;"></image>
						<text style="margin-left: 6rpx;" v-if="formData.income">月薪{{formData.income}}.现</text>
						<text v-if="formData.hasHouse==1">有</text>
						<text v-if="formData.hasHouse==2">无</text>
						车
						<text v-if="formData.vehicle==1">有</text>
						<text v-if="formData.vehicle==2">无</text>
						房
					</view>
					<view class="label" v-if="formData.homeProvince">
						<image src="../static/jiaixang.png" style="width: 38rpx;height:38rpx;"></image>
						<text style="margin-left: 6rpx;">家乡：{{formData.homeProvince}}
							<text v-if="formData.homeCity!='市辖区'">{{formData.homeCity}}</text>
							{{formData.homeCounty}}
						</text>
					</view>
					<view class="label" v-if="formData.locationCity">
						<image src="../static/dizhi.png" style="width: 30rpx;height:30rpx;"></image>
						<text style="margin-left: 6rpx;">现居地：
							<text v-if="formData.locationCity!='市辖区'">{{formData.locationCity}}</text>
							<text v-else> {{formData.locationProvince}}</text>
							{{formData.locationCounty}}
						</text>
					</view>
				</view>
			</view>

			<view class="padding-lr" v-if="formData.interest">
				<view class="title">兴趣爱好</view>
				<view class="margin-top-xs">
					{{formData.interest}}
				</view>
			</view>

			<view style="width: 100%;height: 1rpx;background: #E6E6E6;margin: 30rpx 0;"></view>
			<view class="padding-lr" v-if="formData.feelingAngle">
				<view class="title">感情观</view>
				<view class="margin-top-xs">
					{{formData.feelingAngle}}
				</view>
			</view>

			<view style="width: 100%;height: 1rpx;background: #E6E6E6;margin: 30rpx 0;"></view>
			<view class="padding-lr" v-if="formData.idealAspect">
				<view class="title">择偶标准</view>
				<view class="margin-top-xs">
					{{formData.idealAspect}}
				</view>
			</view>


			<view class="taber" v-if="byUserId!=userId">
				<view class="gunbi" @click="back">
					<u-icon name="close" color="#FFFFFF" size="45"></u-icon>
				</view>
				<view class="chat" @click="callPhone">
					<u-icon name="chat-fill" color="#FFFFFF" size="45"></u-icon>
					<text class="margin-left-xs" v-if="formData.isGetPhone!=1">获取联系方式</text>
					<text class="margin-left-xs" v-else>查看联系方式</text>

				</view>
				<!-- 0否 1是 -->
				<view class="xihuan" @click="like()" v-if="formData.isLike==0">
					<u-icon name="/static/images/index/xihuan.png" color="#FFFFFF" size="50"></u-icon>
				</view>
				<view class="xihuans" @click="like()" v-else>
					<u-icon name="/static/images/index/noxihuan.png" color="#FFFFFF" size="50"></u-icon>
				</view>
			</view>
		</view>


		<u-popup v-model="show" mode="center" border-radius="32" :closeable="true" close-icon="close-circle"
			close-icon-size="48">
			<view class="pupobox">
				<view class="bg">
					<image src="../../../static/images/index/beijing.png" style="width: 100%;max-height: 508rpx;">
					</image>
				</view>
				<view class="pupocot">
					<view class="pupotit">获取联系方式</view>
					<!-- 	<view class="tit">
						确认获取系统将立即短信 通知对方，并且您的头像信息将 显示在对方的消息栏中
					</view> -->
					<view class="patit">本次获取需支付{{money}}元</view>
					<view class="vrn" @click="openpay">确认获取</view>
				</view>
			</view>
		</u-popup>

		<u-popup v-model="payshow" mode="bottom" border-radius="32" :closeable="true" close-icon="close-circle"
			close-icon-size="48">
			<view class="pupoboxs">
				<view class="bg">
					<image src="../../../static/images/index/beijing.png" style="width: 100%;max-height: 508rpx;">
					</image>
				</view>
				<view class="pupocot">
					<view class="pupotit">支付方式</view>
					<view class="flex align-center justify-between" style="height: 100upx;padding: 30upx;"
						v-for="(item,index) in openLists" :key='index'>
						<image :src="item.image" style="width: 55upx;height: 55upx;border-radius: 50upx;"></image>
						<view style="font-size: 30upx;margin-left:0upx;width: 70%;">{{item.text}}
						</view>
						<radio-group name="openWay" style="margin-left: 20upx;" @tap='selectWay(item)'>
							<label class="tui-radio">
								<radio color="linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);"
									:checked="openWay === item.id ? true : false" />
							</label>
						</radio-group>
					</view>
					<!-- <view class="flex align-center justify-between" style="height: 100upx;padding: 30upx;"
						v-for="(item,index) in openList" :key='index' v-if="mymoney>0">
						<image :src="item.image" style="width: 55upx;height: 55upx;border-radius: 50upx;"></image>
						<view style="font-size: 30upx;margin-left:0upx;width: 70%;">{{item.text}}
						</view>
						<radio-group name="openWay" style="margin-left: 20upx;" @tap='selectWay(item)'>
							<label class="tui-radio">
								<radio color="linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);" :checked="openWay === item.id ? true : false" />
							</label>
						</radio-group>
					</view> -->

					<view class="btn margin-top" @click="pay">立即支付</view>
				</view>
			</view>
		</u-popup>
		<hninfo ref="hnPopup"></hninfo>
		<!-- modal弹窗 -->
		<u-modal v-model="meShowModel" :content="meContent" :title="meTitle" :show-cancel-button='meShowCancel'
			@cancel="meHandleClose" @confirm="meHandleBtn" :confirm-text='meConfirmText'
			:cancel-text='meCancelText'></u-modal>
	</view>
</template>

<script>
	import empty from '@/components/empty.vue'
	import hninfo from "@/components/hnInfo.vue";
	import {
		showToast
	} from '../../../common/queue';
	export default {
		components: {
			empty,
			hninfo
		},
		data() {
			return {
				show: false,
				byUserId: '',
				userId: '',
				formData: {},
				dongta: [],
				money: '',
				payshow: false,
				dayPhoneCount: 0, //每天获取联系方式的次数

				//弹窗
				meShowModel: false, //是否显示弹框
				meShowCancel: true, //是否显示取消按钮
				meTitle: '提示', //弹框标题
				meContent: '', //弹框内容
				meConfirmText: '确认', //确认按钮的文字
				meCancelText: '取消', //关闭按钮的文字
				meIndex: '', //弹窗的key
				openLists: '',
				openWay: 1,
				openList: [{
					image: '../../../static/images/my/cz.png',
					text: '零钱',
					id: 3
				}],
				mymoney: 0,
				currentIndex: 0,
				statusStauts: '',
				invitationCode: '',
				isVip: ''
			}
		},
		onShareAppMessage(res) {
			return {
				path: '/package/pages/game/detail?invitation=' + this
					.invitationCode, //这是为了传参   onload(data){let id=data.id;} 
				title: this.formData && this.formData.realName ? this.formData.realName : ''
			}
		},
		/*
		 * uniapp微信小程序分享页面到微信朋友圈
		 */
		onShareTimeline(res) {
			return {
				path: '/package/pages/game/detail?invitation=' + this
					.invitationCode, //这是为了传参   onload(data){let id=data.id;} 
				title: this.formData && this.formData.realName ? this.formData.realName : ''
			}
		},
		onLoad(option) {
			let that = this
			// #ifdef MP-WEIXIN
			if (option.scene) {
				const scene = decodeURIComponent(option.scene);
				uni.setStorageSync('inviterCode', scene.split(',')[0])
			}
			// #endif

			// 获取邀请码保存到本地
			if (option.invitation) {
				uni.setStorageSync('inviterCode', option.invitation)
			}
			this.invitationCode = uni.getStorageSync('invitationCode');
			uni.showLoading({
				title: '加载中'
			})
			// #ifdef APP-PLUS
			this.openLists = [{
				image: '../../../static/images/my/zhifubao.png',
				text: '支付宝',
				id: 1
			}, {
				image: '../../../static/images/my/icon_weixin.png',
				text: '微信',
				id: 2
			}, {
				image: '../../../static/images/my/cz.png',
				text: '零钱',
				id: 3
			}];
			this.openWay = 1;
			// #endif

			// #ifdef MP-WEIXIN
			this.openLists = [{
				image: '../../../static/images/my/icon_weixin.png',
				text: '微信',
				id: 2
			}, {
				image: '../../../static/images/my/cz.png',
				text: '零钱',
				id: 3
			}];
			this.openWay = 2;
			// #endif

			// #ifdef H5
			let ua = navigator.userAgent.toLowerCase();
			if (ua.indexOf('micromessenger') !== -1) {
				//公众号是否自动登录  333
				this.$Request.get('/app/common/type/333').then(res => {
					if (res.data && res.data.value && res.data.value == '是') {
						this.openLists = [{
							image: '../../../static/images/my/zhifubao.png',
							text: '支付宝',
							id: 1
						}, {
							image: '../../../static/images/my/icon_weixin.png',
							text: '微信',
							id: 2
						}, {
							image: '../../../static/images/my/cz.png',
							text: '零钱',
							id: 3
						}];
						this.openWay = 2;
					} else {
						this.openLists = [{
							image: '../../../static/images/my/zhifubao.png',
							text: '支付宝',
							id: 1
						}, {
							image: '../../../static/images/my/cz.png',
							text: '零钱',
							id: 3
						}];
						this.openWay = 1;
					}
				})
			} else {
				this.openLists = [{
					image: '../../../static/images/my/zhifubao.png',
					text: '支付宝',
					id: 1
				}, {
					image: '../../../static/images/my/cz.png',
					text: '零钱',
					id: 3
				}];
				this.openWay = 1;
			}
			// #endif

			this.userId = uni.getStorageSync('userId')
			this.currentIndex = option.currentIndex
			if (option.byUserId) {
				this.byUserId = option.byUserId


			}

			this.$Request.getT('/app/common/type/324').then(res => {
				if (res.code == 0) {
					if (res.data && res.data.value) {
						this.money = res.data.value
					}
				}
			});
		},
		onShow() {
			if (this.byUserId) {
				this.getUserInfo()
				this.getdongtai()
				this.getInfo()


			}

			this.getRenZheng()
			this.taskData()
		},
		methods: {
			// 获取余额
			taskData() {
				this.$Request.get("/app/userMoney/selectMyMoney").then(res => {
					if (res.code == 0 && res.data) {
						this.mymoney = res.data.money
					}
				});
			},
			//预览图片
			priveImgs(index, url) {
				uni.previewImage({
					current: index,
					urls: url
				})
			},
			//确认
			meHandleBtn() {
				let that = this
				if (that.meIndex == 'm1') {
					uni.navigateTo({
						url: '/my/vip/index'
					})
				} else if (that.meIndex == 'm6') {
					uni.navigateTo({
						url: '/my/renzheng/index'
					})
				} else if (that.meIndex == 'm4') {
					uni.navigateTo({
						url: '/my/setting/userinfo'
					})
				} else if (that.meIndex == 'm7') {
					uni.navigateTo({
						url: '/my/wallet/Txmoney'
					})
				} else if (that.meIndex == 'm9') {
					uni.navigateTo({
						url: '/pages/my/userinfo'
					})
				} else if (that.meIndex == 'm10') {
					uni.navigateTo({
						url: '/pages/public/login'
					})
				}

			},
			//取消
			meHandleClose() {
				let that = this
				if (that.meIndex == 'm1') {

				}
			},
			getRenZheng() {
				this.$Request.get("/app/userCertification/getMyUserCertification?authType=1").then(res => {
					if (res.code == 0 && res.data) {
						// 0审核中 1通过 2拒绝 
						if (res.data.status == 0) {
							this.statusStauts = 1 //审核中
							uni.setStorageSync('statusStauts', this.statusStauts)
						} else if (res.data.status == 1) {
							this.statusStauts = 2 //已实名
							uni.setStorageSync('statusStauts', this.statusStauts)
						} else if (res.data.status == 2) {
							this.statusStauts = 3 //已拒绝
							uni.setStorageSync('statusStauts', this.statusStauts)
						}
					} else {
						this.statusStauts = -1 //未实名
						uni.setStorageSync('statusStauts', this.statusStauts)
					}
				});
			},
			noLogin() {
				this.meShowModel = true
				this.meTitle = '提示'
				this.meContent = '您还未登录,请先登录'
				this.meIndex = 'm10'
				this.meShowCancel = true
			},
			callPhone() {
				if (uni.getStorageSync('token')) {

					if (this.statusStauts == 1) { //待审核
						this.meShowModel = true
						this.meTitle = '提示'
						this.meContent = '实名认证审核中,请通过后再来查看'
						this.meIndex = 'm6'
						this.meConfirmText = '去查看'
						this.meShowCancel = true
					} else if (this.statusStauts == 2) { //已通过
						if (this.isVip == 1) { //是否是会员(0不是 1是)
							this.$refs.hnPopup.open(this.formData)
						} else {
							this.meShowModel = true
							this.meTitle = '提示'
							this.meContent = '开通会员获取红娘联系方式'
							this.meIndex = 'm1'
							this.meConfirmText = '确认'
							this.meShowCancel = true

						}
					} else if (this.statusStauts == 3) { //已通过
						this.meShowModel = true
						this.meTitle = '提示'
						this.meContent = '实名审核被拒绝'
						this.meIndex = 'm6'
						this.meConfirmText = '去认证'
						this.meShowCancel = true
					} else if (this.statusStauts == -1) { //已通过
						this.meShowModel = true
						this.meTitle = '提示'
						this.meContent = '未实名认证，请先去实名认证'
						this.meIndex = 'm6'
						this.meConfirmText = '去认证'
						this.meShowCancel = true
					}

				} else {
					this.noLogin()
				}
			},
			getdongtai() {
				this.$Request.getT("/app/trends/getTrendsList?byUserId=" + this.userId + '&page=1&limit=1&userId=' + this
					.byUserId).then(res => {
					if (res.code == 0) {
						let list = res.data.records
						for (var i = 0; i < list.length; i++) {
							let trendsImage = list[i].trendsImage.split(',')
							// list[i].trendsImage = trendsImage[3]
							list[i].trendsImage = JSON.parse(JSON.stringify(trendsImage.slice(0, 4)))
							// this.list = JSON.parse(JSON.stringify(this.listImg.slice(0, 3)))
						}

						this.dongta = list
					}
					uni.hideLoading();
				});
			},
			like() {

				if (!uni.getStorageSync('token')) {
					this.noLogin()
					return
				}

				let that = this
				let userId = uni.getStorageSync('userId')

				if (that.statusStauts == 1) { //待审核
					that.meShowModel = true
					that.meTitle = '提示'
					that.meContent = '实名认证审核中，请通过后去完善资料'
					that.meIndex = 'm6'
					that.meConfirmText = '去查看'
					that.meShowCancel = true
				} else if (that.statusStauts == 2) { //已通过
					that.$Request.get("/app/userData/getUserDataInfo?userId=" + userId).then(res => {
						if (res.code == 0) {
							if (res.data) {
								that.$Request.postT("/app/scFollow/saveScFollow?byUserId=" + that
										.byUserId + '&type=2')
									.then(res => {
										if (res.code == 0) {
											uni.showToast({
												title: res.msg,
												icon: 'none',
												duration: 3000
											})
											that.getInfo()
										}
									});
							} else {
								that.firstlogin = false
								that.meShowModel = true
								that.meTitle = '提示'
								that.meContent = '完善个人资料可以被更多人看到哦'
								that.meConfirmText = '去完善'
								that.meIndex = 'm4'
								that.meShowCancel = true
							}

						}

					});

				} else if (that.statusStauts == 3) { //已拒绝
					that.meShowModel = true
					that.meTitle = '提示'
					that.meContent = '实名审核被拒绝，请先去认证'
					that.meIndex = 'm6'
					that.meConfirmText = '去认证'
					that.meShowCancel = true
				} else if (that.statusStauts == -1) { //已通过
					// uni.navigateTo({
					// 	url: '/my/renzheng/index'
					// })
					that.firstlogin = false
					that.meShowModel = true
					that.meTitle = '提示'
					that.meContent = '未实名认证，请先去实名认证'
					that.meConfirmText = '去认证'
					that.meIndex = 'm6'
					that.meShowCancel = true
				}

			},
			back() {
				if (uni.getStorageSync('token')) {
					let data = {
						filterUserId: this.byUserId
					}
					this.$Request.postT('/app/userFilter/saveUserFilter', data).then(res => {
						if (res.code == 0) {
							uni.$emit('closeCard', true)
							uni.navigateBack()
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					})
				} else {
					this.noLogin()
				}


			},
			godetail() {
				uni.navigateTo({
					url: '/package/pages/game/dongtai?byUserId=' + this.byUserId
				})
			},
			getUserInfo() {
				this.$Request.get("/app/user/selectUserById").then(res => {
					if (res.code == 0) {
						this.isVip = res.data.isVip
					}

				});
			},
			getInfo() {
				this.$Request.get("/app/userData/getUserDataInfo?byUserId=" + this.userId + '&userId=' + this.byUserId)
					.then(res => {
						if (res.code == 0) {
							this.formData = res.data
							if (this.formData.userImg) {
								this.formData.userImg = this.formData.userImg.split(',')
							}
							this.$forceUpdate()
							// isLike 是否已喜欢 0否 1是
							// isGetPhone 是否已获取了电话号码 0否 1是
						}
						uni.hideLoading()
					});
			},
			openpay() {
				this.show = false
				this.payshow = true
			},
			selectWay: function(item) {
				this.openWay = item.id;
			},
			pay() {
				uni.showLoading({
					title: '支付中...'
				});
				this.payshow = false
				if (this.openWay == 1) { //支付宝支付
					// #ifdef H5
					let data = {
						classify: 5,
						dataId: this.formData.dataId,
					}
					this.$Request.postT("/app/userGetPhoneRecord/buyPhone", data).then(res => {
						if (res.code == 0) {
							const div = document.createElement('div')
							div.innerHTML = res.data //此处form就是后台返回接收到的数据
							document.body.appendChild(div)
							document.forms[0].submit()
							uni.hideLoading()
						} else {
							uni.showToast({
								icon: 'none',
								title: res.msg
							});
						}
					});
					// #endif

					// #ifdef APP
					let data = {
						classify: 4,
						dataId: this.formData.dataId
					}
					this.$Request.postT("/app/userGetPhoneRecord/buyPhone", data).then(ret => {
						console.log(ret)
						this.isCheckPay(ret.code, 'alipay', ret.data);
					});
					// #endif
				} else if (this.openWay == 2) { //微信支付
					// #ifdef MP-WEIXIN
					let data = {
						classify: 2,
						dataId: this.formData.dataId
					}
					this.$Request.postT("/app/userGetPhoneRecord/buyPhone", data).then(ret => {
						uni.hideLoading()
						uni.requestPayment({
							provider: 'wxpay',
							timeStamp: ret.data.timestamp,
							nonceStr: ret.data.noncestr,
							package: ret.data.package,
							signType: ret.data.signType,
							paySign: ret.data.sign,
							success: function(suc) {
								console.log('success:' + JSON.stringify(suc));
								uni.showToast({
									title: '支付成功',
									icon: 'success'
								})
								this.getUserInfo()
							},
							fail: function(err) {
								console.log('fail:' + JSON.stringify(err));
								uni.showToast({
									title: '支付失败',
									icon: 'none'
								})
							}
						});
					});
					// #endif
					// #ifdef H5
					let data = {
						classify: 3,
						dataId: this.formData.dataId
					}
					this.$Request.postT("/app/userGetPhoneRecord/buyPhone", data).then(res => {
						if (res.code == 0) {
							uni.hideLoading()
							this.callPay(res.data);
						} else {
							uni.showToast({
								icon: 'none',
								title: '支付失败!'
							});
						}
					});
					// #endif
					// #ifdef APP
					let data = {
						classify: 1,
						dataId: this.formData.dataId
					}
					this.$Request.postT("/app/userGetPhoneRecord/buyPhone", data).then(ret => {
						console.log(ret, 'retretretretretret')
						this.isCheckPay(ret.code, 'wxpay', JSON.stringify(ret.data));
					});
					// #endif
				} else if (this.openWay == 3) {
					// console.log(this.mymoney >= this.price,this.mymoney , this.money)
					if (this.mymoney >= this.money) {
						let data = {
							classify: 0,
							dataId: this.formData.dataId
						}
						this.$Request.postT("/app/userGetPhoneRecord/buyPhone", data).then(ret => {
							uni.hideLoading()
							if (ret.code == 0) {
								uni.showToast({
									title: '支付成功',
									icon: 'success'
								})
								this.getUserInfo()
								this.getInfo()
							} else {
								uni.showToast({
									icon: 'none',
									title: ret.msg
								});
							}
						});
					} else {
						uni.hideLoading()
						this.meShowModel = true
						this.meTitle = '提示'
						this.meContent = '零钱余额不足，请选去充值'
						this.meConfirmText = '去充值'
						this.meIndex = 'm7'
						this.meShowCancel = true
					}

				}
			},
			callPay: function(response) {
				console.log(response)
				if (typeof WeixinJSBridge === "undefined") {

					if (document.addEventListener) {
						document.addEventListener('WeixinJSBridgeReady', this.onBridgeReady(response), false);
					} else if (document.attachEvent) {
						document.attachEvent('WeixinJSBridgeReady', this.onBridgeReady(response));
						document.attachEvent('onWeixinJSBridgeReady', this.onBridgeReady(response));
					}
				} else {
					console.log(1)
					this.onBridgeReady(response);
				}
			},
			onBridgeReady: function(response) {
				let that = this;
				if (!response.package) {
					return;
				}
				console.log(response, '++++++++')
				WeixinJSBridge.invoke(
					'getBrandWCPayRequest', {
						"appId": response.appid, //公众号名称，由商户传入
						"timeStamp": response.timestamp, //时间戳，自1970年以来的秒数
						"nonceStr": response.noncestr, //随机串
						"package": response.package,
						"signType": response.signType, //微信签名方式：
						"paySign": response.sign //微信签名
					},
					function(res) {
						console.log(res, '/*-/*-/*-')
						if (res.err_msg === "get_brand_wcpay_request:ok") {
							// 使用以上方式判断前端返回,微信团队郑重提示：
							//res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。
							uni.showLoading({
								title: '支付成功'
							});
							that.getUserInfo()
							that.getInfo()
						} else {
							uni.hideLoading();
						}
						WeixinJSBridge.log(response.err_msg);
					}
				);
			},
			isCheckPay(status, name, order) {
				let that = this
				uni.hideLoading()
				if (status == 0) {
					that.setPayment(name, order);
				} else {
					uni.hideLoading();
					uni.showToast({
						title: '支付信息有误',
						icon: 'none'
					});
				}
			},
			setPayment(name, order) {
				let that = this
				uni.requestPayment({
					provider: name,
					orderInfo: order, //微信、支付宝订单数据
					success: function(res) {
						console.log(res)
						uni.hideLoading();
						uni.showLoading({
							title: '支付成功'
						});
						that.getUserInfo()
						that.getInfo()
					},
					fail: function(err) {
						console.log(err)
						uni.hideLoading();
					},
					complete() {
						uni.hideLoading();
					}
				});
			},
		}
	}
</script>

<style lang="less">
	page {
		background: #FFFFFF;
	}

	.title {
		font-size: 32rpx;
		font-family: PingFang SC;
		font-weight: 600;
		color: #292929;
	}

	.yhm {
		font-size: 42rpx;
		font-family: PingFang SC;
		font-weight: bold;

		margin-right: 20rpx;
	}

	.wezhi {
		font-size: 26rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #999999;
	}

	.sexicon {
		background: #38CAFF;
		border-radius: 10rpx;
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #FFFFFF;
		padding: 4rpx 10rpx;
		margin-right: 10rpx;
	}

	.sexicons {
		background: #edbef3;
		border-radius: 10rpx;
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #FFFFFF;
		padding: 4rpx 10rpx;
		margin-right: 10rpx;
	}

	.labe {
		background: #F2F2F2;
		border-radius: 10rpx;
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #999999;
		padding: 4rpx 10rpx;
		margin-right: 15rpx;
	}

	.remk {
		width: 100%;
		height: 58rpx;
		background: #FFE5EC;
		border-radius: 12rpx;
		color: #666666;
		display: flex;
		align-items: center;
		padding: 0 10rpx 0 30rpx;
		margin-top: 20rpx;

		text {
			width: 100%;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 1;
			overflow: hidden;
			margin-left: 10rpx;
		}
	}

	.label {
		height: 70rpx;
		border: 4rpx solid #F2F2F2;
		border-radius: 12rpx;
		padding: 0 20rpx;
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
		margin-right: 20rpx;
	}

	.taber {
		// background: #FFFFFF;
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 30rpx 70rpx;

		.gunbi {
			width: 100rpx;
			height: 100rpx;
			background: linear-gradient(0deg, #C7C7CF 0%, #D3D2D7 100%);
			box-shadow: 0rpx 10rpx 20rpx 0rpx #E4E4E4;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.xihuan {
			width: 100rpx;
			height: 100rpx;
			background: linear-gradient(0deg, #FF6F9C 0%, #FFA8C7 100%);
			box-shadow: 0rpx 10rpx 20rpx 0rpx #FFDBE7;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.xihuans {
			width: 100rpx;
			height: 100rpx;
			background: linear-gradient(0deg, #cacad1 0%, #dedee2 100%);
			box-shadow: 0rpx 10rpx 20rpx 0rpx #dedee2;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.chat {
			width: 290rpx;
			height: 100rpx;
			background: linear-gradient(111deg, #797CFD 0%, #6165FF 100%);
			box-shadow: 3rpx 6rpx 12rpx 0rpx #D3D4FF;
			border-radius: 50rpx;
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: bold;
			color: #FFFFFF;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}


	.pupobox {
		width: 596rpx;
		// height: 508rpx;
		background: #FFFFFF;
		border-radius: 32rpx;
		position: relative;
		padding: 40rpx 30rpx 30rpx;

		.bg {
			height: 225rpx;
			// background: linear-gradient(90deg, #D8E3FF 0%, #F3DAF7 100%);
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			z-index: 0;
		}

		.pupocot {
			width: 100%;
			position: relative;
			z-index: 99;

			.pupotit {
				text-align: center;
				font-size: 38rpx;
				font-family: PingFang SC;
				font-weight: 800;
				color: #1E1E1E;
			}

			.tit {
				width: 446rpx;
				margin: 25rpx auto;
				text-align: center;
				font-size: 32rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #999999;
			}

			.patit {
				font-size: 32rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);
				;
				text-align: center;
				margin-bottom: 40rpx;
				margin-top: 30rpx;
			}

			.vrn {
				width: 494rpx;
				height: 92rpx;
				background: linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);
				;
				border-radius: 46rpx;
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: #FFFFFF;
				margin: 10rpx auto;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}

	}

	.pupoboxs {
		width: 100%;
		// height: 508rpx;
		background: #FFFFFF;
		border-radius: 32rpx;
		position: relative;
		padding: 40rpx 30rpx 30rpx;

		.bg {
			height: 225rpx;
			// background: linear-gradient(90deg, #D8E3FF 0%, #F3DAF7 100%);
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			z-index: 0;
		}

		.pupocot {
			width: 100%;
			position: relative;
			z-index: 99;

			.pupotit {
				text-align: center;
				font-size: 38rpx;
				font-family: PingFang SC;
				font-weight: 800;
				color: #1E1E1E;
			}

			.btn {
				width: 100%;
				height: 80rpx;
				background: linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);
				;
				border-radius: 46rpx;
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: #FFFFFF;
				margin: 10rpx auto;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}
</style>