<template>
	<view>
		<!-- #ifndef H5 -->
		<!-- 导航栏 -->
		<u-navbar title="红娘牵线" :is-back="false" :background="background" :border-bottom="false"></u-navbar>
		<!-- #endif -->
		<!-- 背景 -->
		<view class="bgs">
			<!-- #ifdef H5 -->
			<image src="../../static/images/bgImg.png" style="width: 100%;height: 189px;"></image>
			<!-- #endif -->
			<!-- #ifndef H5 -->
			<image src="../../static/images/bgImg.png" style="width: 100%;height: 279px;"></image>
			<!-- #endif -->
		</view>

		<view class="headTop">
			<view class="dw" @click="changeCity">
				<image src="../../static/images/hn/add.png"></image>
				<view>{{city}}</view>
			</view>
			<view class="flex">
				<!-- <view class="btn" @click="goHnAdmin()">红娘工作台</view>
				<view class="btn" @click="goHnRanheng()">成为红娘</view> -->
				<!-- {{ hnInfo.status }} 
				{{ userInfo.isMatchmaker }} -->
				<!-- hnInfo.status == 1&& -->
				<view class="btn" @click="goHnAdmin()" v-if="userInfo.isMatchmaker==2">红娘工作台</view>
				<block v-else>
					<view class="btn" @click="goHnRanheng()" v-if="hnInfos.status == 0">审核中</view>
					<view class="btn" @click="goHnRanheng()" v-else-if="hnInfos.status == 2">已被拒</view>
					<view class="btn" @click="goHnRanheng()" v-else>成为红娘</view>

				</block>

			</view>

		</view>

		<view class="libox">
			<list :list="list" @click="clickItem"></list>
		</view>
		<empty v-if="list.length==0" style="height: 50vh;background: transparent;"></empty>

		<u-popup v-model="show" mode="center" border-radius="30" :closeable="true">
			<view class="popup">
				<image src="../../static/images/hn/hnqx.png" mode="aspectFill"></image>
				<view class="qxbox">
					<view class="title">牵线申请</view>
					<view class="tit margin-top">
						<text>已发送</text>
						牵线消息给红娘
					</view>
					<view class="tit margin-top-xs">请等待红娘回复或添加红娘微信</view>
					<view class="btn" @click="hnshow = true">等不及，现在点击联系红娘</view>
				</view>
			</view>
		</u-popup>
		<hninfo ref="hnPopup"></hninfo>
		<u-picker ref="city" mode="region" v-model="show1" :params="params" @confirm="confirmProvince"></u-picker>

		<view class="s-col is-col-24" v-if="list.length > 0">
			<load-more :status="loadingType" :contentText="contentText"></load-more>
		</view>
		<!-- modal弹窗 -->
		<u-modal v-model="meShowModel" :content="meContent" :title="meTitle" :show-cancel-button='meShowCancel'
			@cancel="meHandleClose" @confirm="meHandleBtn" :confirm-text='meConfirmText'
			:cancel-text='meCancelText'></u-modal>
	</view>
</template>

<script>
	import empty from '@/components/empty'
	import list from '@/components/hnlist'
	import hninfo from "@/components/hnInfo.vue";

	export default {
		components: {
			empty,
			list,
			hninfo
		},
		data() {
			return {
				//弹窗
				meShowModel: false, //是否显示弹框
				meShowCancel: true, //是否显示取消按钮
				meTitle: '提示', //弹框标题
				meContent: '', //弹框内容
				meConfirmText: '确认', //确认按钮的文字
				meCancelText: '取消', //关闭按钮的文字
				meIndex: '', //弹窗的key
				background: {
					backgroundImage: 'linear-gradient(90deg, #E4E7F8 0%, #F1E3F4 46%, #FDE1EF 100%)'
				},
				city: '',
				lat: '',
				lng: '',
				list: [


				],
				loadingType: 0,
				contentText: {
					contentdown: '上拉显示更多',
					contentrefresh: '正在加载...',
					contentnomore: '没有更多数据了'
				},
				show: false,
				hnshow: false,
				page: 1,
				userInfo: {},
				userId: "",
				hnInfos: {},
				show1: false,
				params: {
					city: true,
					province: true,
					area: false,
				}
			}
		},
		onLoad() {
			//获取城市
			let that = this
			if (uni.getStorageSync('city') && uni.getStorageSync('lat')) {
				that.lat = uni.getStorageSync('lat')
				that.lng = uni.getStorageSync('lng')
				that.city = uni.getStorageSync('city')
				that.page = 1
				that.list = []
				that.getUserList()
			} else {
				uni.getLocation({
					type: 'gcj02', //wgs84  gcj02
					success: function(res) {
						//根据定位的经纬度获取城市名称
						that.lat = res.latitude
						that.lng = res.longitude
						that.selectCity(res.latitude, res.longitude);
					},
					fail: function() {
						console.log('获取地址失败')
					}
				})
			}

		},
		onShow() {
			this.userId = uni.getStorageSync('userId')
			if (this.userId) {
				this.getUserInfo()
				this.getHnInfo()
			}

			if (this.city) {
				this.page = 1
				this.list = []
				this.getUserList()
			}
		},
		onPullDownRefresh() {
			this.page = 1
			this.list = []
			this.getUserList()
		},
		// 页面处理函数--监听用户上拉触底
		onReachBottom() {
			if (this.loadingType == 0) {
				this.page += 1
				this.getUserList()
			}


		},
		onShareAppMessage(res) {

		},
		methods: {
			changeCity() {
				let type = 1
				if (type == 1) {
					// 省市区下拉选择
					this.show1 = true
				} else {
					// 地图选择
					uni.chooseLocation({
						success: (res) => {
							const {
								name,
								address,
								longitude,
								latitude
							} = res
							console.log(res);
							this.selectCity(latitude, longitude)
						},
						fail: (error) => {
							console.log(error);
						}
					})
				}
			},
			confirmProvince(e) {
				console.log(e);
				this.city = e.city.label == "市辖区" ? e.province.label : e.city.label
				this.page = 1
				this.getUserList()
			},
			previewImage(e) {
				// uni.previewImage({
				//     current: e,
				//     urls: [e]
				// })
			},
			makePhone(e) {
				uni.makePhoneCall({
					phoneNumber: e
				})
			},

			isLogin() {
				if (this.userId) {
					return true
				} else {
					this.meShowModel = true
					this.meTitle = '提示'
					this.meContent = '您还未登录,请先登录'
					this.meIndex = 'm1'
					this.meShowCancel = true
					return false
				}

			},
			//确认
			meHandleBtn() {
				let that = this
				if (that.meIndex == 'm1') {
					uni.navigateTo({
						url: '/pages/public/login'
					})
				} else if (that.meIndex == 'm2') {
					uni.navigateTo({
						url: '/my/renzheng/index'
					})
				} else if (that.meIndex == 'm3') {
					uni.navigateTo({
						url: '/my/renzheng/index'
					})
				} else if (that.meIndex == 'm9') {
					uni.navigateTo({
						url: '/pages/my/userinfo'
					})
				} else if (that.meIndex == 'm6') {
					// #ifdef MP-WEIXIN
					if (uni.getStorageSync('sendMsg')) {
						wx.requestSubscribeMessage({
							tmplIds: that.arr,
							success(re) {
								if (datas.indexOf("accept") != -1) {
									console.log(re)
								}
							},
							fail: (res) => {
								console.log(res)
							}
						})
					}
					// #endif
				}
			},
			//取消
			meHandleClose() {
				let that = this
				if (that.meIndex == 'm1') {

				}
			},
			goHnRanheng() {
				if (!this.isLogin()) {
					return
				}
				uni.navigateTo({
					url: '/my/hongniang/renzheng'
				})
			},
			getUserInfo() {
				this.$Request.get("/app/user/selectUserById").then(res => {
					if (res.code === 0) {
						this.userInfo = res.data
					}
				})
			},
			goHnAdmin() {
				uni.navigateTo({
					url: '/my/hongniang/admin'
				})
			},
			copyOrder(content) {
				let that = this;
				uni.setClipboardData({
					data: content,
					success: function() {
						that.$queue.showToast('复制成功')
					}
				})
			},
			clickItem(e) {
				// this.show = true
				console.log(e, '8888888');
				if (e.index == 0) {
					uni.navigateTo({
						url: '/package/pages/game/detail?byUserId=' + e.id.userId
					})
				} else {
					this.$refs.hnPopup.open(e.id)
				}

			},
			//根据经纬度获取城市地址
			selectCity(latitude, longitude) {
				let data = {
					lat: latitude,
					lng: longitude
				}
				this.$Request.get('/app/Login/selectCity?lat=' + latitude + '&lng=' + longitude).then(
					res => {
						if (res.code == 0) {
							this.city = res.data.city
							uni.setStorageSync('city', res.data.city)
							this.page = 1
							this.list = []
							this.getUserList()
						}
					});
			},
			getUserList() {
				uni.showLoading({
					title: '加载中...'
				});
				this.$Request.get('/app/userRoleMatchmaker/getAllUserDataList', {
					page: this.page,
					limit: 10,
					loginUserId: this.userId,
					city: this.city
				}).then(res => {
					console.log(res);
					uni.hideLoading();
					uni.stopPullDownRefresh();
					if (res.code == 0) {
						res.data.records.map(ite => {
							if (ite.userImg) {
								// console.log(ite.userImg)
								ite.userImg = ite.userImg.split(',')
								// console.log(ite.userImg)
							}

						})
						this.list = this.page == 1 ? res.data.records : [
							...this.list,
							...res.data.records
						]
						if (res.data.pages > res.data.current) {
							this.loadingType = 0
						} else {
							this.loadingType = 2
						}
					}
				})
			},
			getHnInfo() {
				this.$Request.get('//app/userCertification/getMyUserCertification', {
					authType: 2
				}).then(res => {
					if (res.code == 0 && res.data) {
						this.hnInfos = res.data
					}
				})
			}

		}
	}
</script>

<style lang="less">
	.bgs {
		width: 100%;
		height: 346rpx;
		// background: linear-gradient(90deg, #E4E7F8 0%, #F1E3F4 46%, #FDE1EF 100%);
		position: fixed;
		/* #ifdef H5 */
		top: 0;
		/* #endif */
		left: 0;
		right: 0;
		z-index: 9;
	}

	.headTop {
		background: linear-gradient(90deg, #E4E7F8 0%, #F1E3F4 46%, #FDE1EF 100%);
		position: fixed;
		// position: sticky;
		/* #ifdef H5 */
		top: 0;
		/* #endif */
		left: 0;
		right: 0;
		z-index: 999;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 30rpx;

		.dw {
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 26rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #343546;

			image {
				width: 29rpx;
				height: 41rpx;
				margin-right: 15rpx;

			}
		}

		.btn {
			width: 172rpx;
			height: 62rpx;
			background: linear-gradient(0deg, #FF6F9C 0%, #FF90B7 95%);
			border-radius: 31rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 26rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #FFFFFF;
		}
	}

	.libox {
		position: relative;
		z-index: 9;
		padding-top: 90rpx;
	}

	.popup {
		width: 550rpx;
		height: 570rpx;
		position: relative;

		.qxbox {
			width: 550rpx;
			position: absolute;
			left: 0;
			right: 0;
			top: 0;
			z-index: 9;
			padding: 30rpx;
			text-align: center;
			margin-top: 230rpx;

			.title {
				font-size: 36rpx;
				font-family: PingFang SC;
				font-weight: 800;
				color: #333333;
			}

			.tit {

				font-size: 26rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #403E3F;

				text {
					color: #FF6684;
				}
			}

			.btn {
				width: 100%;
				height: 80rpx;
				background: linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);
				border-radius: 40rpx;
				font-size: 30rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: #FFFFFF;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-top: 40rpx;
			}
		}
	}
</style>