(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["my-setting-xieyi2"],{"2f37":function(t,n,e){var i=e("ff98");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=e("967d").default;a("3a7ca0fe",i,!0,{sourceMap:!1,shadowMode:!1})},"30e0":function(t,n,e){"use strict";e.r(n);var i=e("7ae7"),a=e("e2d3");for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(o);e("dcfa");var u=e("828b"),f=Object(u["a"])(a["default"],i["b"],i["c"],!1,null,"5df670f2",null,!1,i["a"],void 0);n["default"]=f.exports},"7ae7":function(t,n,e){"use strict";e.d(n,"b",(function(){return i})),e.d(n,"c",(function(){return a})),e.d(n,"a",(function(){}));var i=function(){var t=this.$createElement,n=this._self._c||t;return n("v-uni-view",{staticClass:"home1",staticStyle:{"line-height":"26px",padding:"32upx"}},[n("v-uni-view",{staticStyle:{color:"#000000","font-size":"28upx"},domProps:{innerHTML:this._s(this.content)}})],1)},a=[]},cc320:function(t,n,e){"use strict";e("6a54"),Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;n.default={data:function(){return{content:""}},onLoad:function(){this.getGuize()},methods:{getGuize:function(){var t=this;this.$Request.getT("/app/common/type/177").then((function(n){0===n.code&&(t.content=n.data.value)}))}}}},dcfa:function(t,n,e){"use strict";var i=e("2f37"),a=e.n(i);a.a},e2d3:function(t,n,e){"use strict";e.r(n);var i=e("cc320"),a=e.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(o);n["default"]=a.a},ff98:function(t,n,e){var i=e("c86c");n=i(!1),n.push([t.i,"uni-page-body[data-v-5df670f2]{background:#fff}body.?%PAGE?%[data-v-5df670f2]{background:#fff}",""]),t.exports=n}}]);