<template>
	<view class="container">
		<!-- 顶部统计区域 -->
		<view class="top-section">
			<image class="bg-image" src="https://photo.zastatic.com/images/common-cms/it/20250527/1748338175386_113338_t.png" mode="aspectFill"></image>
			<view class="stats-container">
				<view class="stat-item">
					<view class="stat-number">{{ inviteCount }}</view>
					<view class="stat-label">邀请红娘（人）</view>
				</view>
				<view class="stat-item">
					<view class="stat-number">{{ myIncome }}</view>
					<view class="stat-label">我的收益</view>
				</view>
				<view class="stat-item">
					<view class="stat-number">{{ redEnvelopeAmount }}</view>
					<view class="stat-label">红娘分润</view>
				</view>
			</view>
			<view class="invite-btn" @click="handleInvite">
				邀请红娘
			</view>
		</view>

		<!-- 下线列表 -->
		<view class="list-section">
			<view class="list-header">
				<view class="header-item">编号</view>
				<view class="header-item">头像</view>
				<view class="header-item">昵称</view>
				<view class="header-item">时间</view>
			</view>

			<view class="list-content">
				<view class="list-item" v-for="(item, index) in downLineList" :key="index">
					<view class="item-number">{{ item.number }}</view>
					<view class="item-avatar">
						<image :src="item.avatar" mode="aspectFill"></image>
					</view>
					<view class="item-name">{{ item.name }}</view>
					<view class="item-time">{{ item.time }}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			inviteCount: 23,
			myIncome: '10000.00',
			redEnvelopeAmount: '10000.00',
			downLineList: [
				{
					number: '007621',
					avatar: 'https://photo.zastatic.com/images/common-cms/it/20250527/1748338545852_26938_t.png',
					name: '王小贝',
					time: '2025-05-26'
				},
				{
					number: '007621',
					avatar: 'https://photo.zastatic.com/images/common-cms/it/20250527/1748338545852_26938_t.png',
					name: '王小贝',
					time: '2025-05-26'
				},
				{
					number: '007621',
					avatar: 'https://photo.zastatic.com/images/common-cms/it/20250527/1748338545852_26938_t.png',
					name: '王小贝',
					time: '2025-05-26'
				},
				{
					number: '007621',
					avatar: 'https://photo.zastatic.com/images/common-cms/it/20250527/1748338545852_26938_t.png',
					name: '王小贝',
					time: '2025-05-26'
				},
				{
					number: '007621',
					avatar: 'https://photo.zastatic.com/images/common-cms/it/20250527/1748338545852_26938_t.png',
					name: '王小贝',
					time: '2025-05-26'
				},
				{
					number: '007621',
					avatar: 'https://photo.zastatic.com/images/common-cms/it/20250527/1748338545852_26938_t.png',
					name: '王小贝',
					time: '2025-05-26'
				}
			]
		}
	},
	onLoad() {
		this.loadDownLineData()
	},
	methods: {
		loadDownLineData() {
			// 这里可以调用API获取下线数据
			// this.$Request.get("/app/downline/list").then(res => {
			//     if (res.code == 0) {
			//         this.downLineList = res.data.list
			//         this.inviteCount = res.data.inviteCount
			//         this.myIncome = res.data.myIncome
			//         this.redEnvelopeAmount = res.data.redEnvelopeAmount
			//     }
			// });
		},
		handleInvite() {
			// 跳转到邀请页面
			uni.navigateTo({
				url: '/pages/my/invitationUser'
			})
		}
	}
}
</script>

<style scoped>
page {
	background: #f5f5f5;
}

.container {
	min-height: 100vh;
}

.top-section {
	position: relative;
	height: 400rpx;
	overflow: hidden;
}

.bg-image {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 1;
}

.stats-container {
	position: relative;
	z-index: 2;
	display: flex;
	justify-content: space-around;
	padding: 60rpx 40rpx 40rpx;
}

.stat-item {
	text-align: center;
	color: #333333;
}

.stat-number {
	font-size: 48rpx;
	font-weight: bold;
	margin-bottom: 10rpx;
}

.stat-label {
	font-size: 28rpx;
	color: #666666;
}

.invite-btn {
	position: relative;
	z-index: 2;
	margin: 0 40rpx;
	height: 80rpx;
	background: linear-gradient(90deg, #FF6B9D, #FF8FA3);
	border-radius: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #FFFFFF;
	font-size: 32rpx;
	font-weight: bold;
}

.list-section {
	background: #FFFFFF;
	margin: 20rpx;
	border-radius: 12rpx;
	overflow: hidden;
}

.list-header {
	display: flex;
	background: #f8f8f8;
	padding: 30rpx 20rpx;
	border-bottom: 1rpx solid #e0e0e0;
}

.header-item {
	flex: 1;
	text-align: center;
	font-size: 28rpx;
	color: #666666;
	font-weight: 500;
}

.list-content {
	max-height: 800rpx;
	overflow-y: auto;
}

.list-item {
	display: flex;
	padding: 30rpx 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
	align-items: center;
}

.list-item:last-child {
	border-bottom: none;
}

.item-number {
	flex: 1;
	text-align: center;
	font-size: 28rpx;
	color: #333333;
}

.item-avatar {
	flex: 1;
	display: flex;
	justify-content: center;
}

.item-avatar image {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
}

.item-name {
	flex: 1;
	text-align: center;
	font-size: 28rpx;
	color: #333333;
}

.item-time {
	flex: 1;
	text-align: center;
	font-size: 28rpx;
	color: #666666;
}
</style>