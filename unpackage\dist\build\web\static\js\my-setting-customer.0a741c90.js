(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["my-setting-customer"],{4847:function(n,e,t){"use strict";t.r(e);var i=t("93b1"),a=t.n(i);for(var o in i)["default"].indexOf(o)<0&&function(n){t.d(e,n,(function(){return i[n]}))}(o);e["default"]=a.a},"4f042":function(n,e,t){"use strict";t.r(e);var i=t("eef2"),a=t("4847");for(var o in a)["default"].indexOf(o)<0&&function(n){t.d(e,n,(function(){return a[n]}))}(o);t("6ea8");var u=t("828b"),s=Object(u["a"])(a["default"],i["b"],i["c"],!1,null,"f911eae0",null,!1,i["a"],void 0);e["default"]=s.exports},"6ea8":function(n,e,t){"use strict";var i=t("b250"),a=t.n(i);a.a},"93b1":function(n,e,t){"use strict";t("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t("5ef2");var i={data:function(){return{SelKeFu:"2",image:"",isWeiXin:!1,weixin:"710070994",webviewStyles:{progress:{color:"#1A1929 "}}}},onLoad:function(){var n=this,e=navigator.userAgent.toLowerCase();-1!==e.indexOf("micromessenger")&&(this.isWeiXin=!0),this.SelKeFu=this.$queue.getData("SelKeFu"),this.$Request.getT("/app/common/type/1").then((function(e){0==e.code&&e.data&&e.data.value&&(console.log(e.data.value),n.image=e.data.value)})),this.$Request.getT("/app/common/type/44").then((function(e){0==e.code&&e.data&&e.data.value&&(n.weixin=e.data.value)}))},onPullDownRefresh:function(){uni.stopPullDownRefresh()},methods:{copyHref:function(){var n=this;uni.setClipboardData({data:this.weixin,success:function(e){n.$queue.showToast("复制成功")}})},saveImg:function(){var n=this;uni.saveImageToPhotosAlbum({filePath:n.image,success:function(e){n.$queue.showToast("保存成功")}})},rests:function(){uni.showToast({title:"已刷新请再次长按识别",mask:!1,duration:1500,icon:"none"}),window.location.reload()},goChat:function(){if("1"===this.SelKeFu){var n=this.$queue.getData("SelKeFuLink");-1!==n.indexOf("/pages/")||-1!==n.indexOf("/my/")?uni.navigateTo({url:n}):window.location.href=n}else{var e=this.$queue.getData("token");e?uni.navigateTo({url:"/my/setting/chat"}):this.goLoginInfo()}},goLoginInfo:function(){uni.navigateTo({url:"/pages/public/loginphone"})}}};e.default=i},b250:function(n,e,t){var i=t("d8ce");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[n.i,i,""]]),i.locals&&(n.exports=i.locals);var a=t("967d").default;a("25cd7f74",i,!0,{sourceMap:!1,shadowMode:!1})},d8ce:function(n,e,t){var i=t("c86c");e=i(!1),e.push([n.i,"\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/* @import '../../static/css/index.css'; */uni-page-body[data-v-f911eae0]{background:#f5f5f5}body.?%PAGE?%[data-v-f911eae0]{background:#f5f5f5}",""]),n.exports=e},eef2:function(n,e,t){"use strict";t.d(e,"b",(function(){return i})),t.d(e,"c",(function(){return a})),t.d(e,"a",(function(){}));var i=function(){var n=this,e=n.$createElement,t=n._self._c||e;return t("v-uni-view",{staticStyle:{height:"100vh",margin:"32upx"}},[t("v-uni-view",{staticStyle:{"text-align":"center",background:"#FFFFFF",padding:"40upx","border-radius":"32upx"}},[t("v-uni-view",{staticStyle:{"font-size":"38upx",color:"#000000"}},[n._v("添加客服微信咨询")]),t("v-uni-view",{staticStyle:{"font-size":"32upx","margin-top":"32upx",color:"#000000"}},[n._v("微信号："+n._s(n.weixin))]),t("v-uni-view",{staticStyle:{background:"#AC75FE",width:"200upx","margin-top":"32upx","font-size":"30upx","margin-left":"36%",color:"#FFFFFF",padding:"4upx 20upx","border-radius":"24upx"},on:{click:function(e){arguments[0]=e=n.$handleEvent(e),n.copyHref.apply(void 0,arguments)}}},[n._v("一键复制")]),t("v-uni-image",{staticStyle:{"margin-top":"32upx"},attrs:{mode:"aspectFit",src:n.image},on:{click:function(e){arguments[0]=e=n.$handleEvent(e),n.saveImg.apply(void 0,arguments)}}}),n.isWeiXin?t("v-uni-view",{staticStyle:{"font-size":"28upx",color:"#FFFFFF","margin-top":"32upx"}},[n._v(n._s(n.isWeiXin?"长按识别上方二维码":""))]):n._e(),t("v-uni-view",{staticStyle:{width:"260upx","margin-top":"32upx","font-size":"30upx","margin-left":"28%",color:"#AC75FE",padding:"4upx 20upx","border-radius":"24upx"},on:{click:function(e){arguments[0]=e=n.$handleEvent(e),n.goChat.apply(void 0,arguments)}}},[n._v("联系在线客服")]),n.isWeiXin?t("v-uni-view",{staticStyle:{"font-size":"24upx",color:"#FFFFFF","margin-top":"80upx"},on:{click:function(e){arguments[0]=e=n.$handleEvent(e),n.rests.apply(void 0,arguments)}}},[n._v("无法识别？")]):n._e()],1)],1)},a=[]}}]);