(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["my-renzheng-index"],{"501a":function(t,e,a){"use strict";a.r(e);var i=a("e1ed"),o=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=o.a},"7c80":function(t,e,a){var i=a("fa91");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("6b3dfed5",i,!0,{sourceMap:!1,shadowMode:!1})},"8c83":function(t,e,a){"use strict";var i=a("7c80"),o=a.n(i);o.a},"98af":function(t,e,a){"use strict";a.r(e);var i=a("a2ff"),o=a("501a");for(var r in o)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(r);a("8c83");var n=a("828b"),d=Object(n["a"])(o["default"],i["b"],i["c"],!1,null,"17c7702e",null,!1,i["a"],void 0);e["default"]=d.exports},a2ff:function(t,e,a){"use strict";a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){return i}));var i={uForm:a("c3a4").default,uFormItem:a("3b2d").default,uInput:a("cd60").default},o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return"否"!=t.shangxianSelect?a("v-uni-view",[a("v-uni-view",{staticClass:"text-center text-red bg-white"},[t._v(t._s(t.form.remark))]),a("v-uni-view",{staticClass:"padding"},[a("v-uni-view",{staticClass:"text-black padding bg radius"},[a("u-form",{ref:"uForm",attrs:{model:t.form,"label-position":"top","label-style":t.lableStyle}},[a("u-form-item",{attrs:{label:"真实姓名","border-bottom":!0}},[a("u-input",{attrs:{placeholderStyle:"color:#999999",customStyle:t.customStyle3,placeholder:"请输入真实姓名"},model:{value:t.form.realName,callback:function(e){t.$set(t.form,"realName",e)},expression:"form.realName"}})],1),a("v-uni-view",{staticClass:"margin-tb-xl"}),a("u-form-item",{attrs:{label:"身份证号码","border-bottom":!1}},[a("u-input",{attrs:{placeholder:"请输入身份证号码",placeholderStyle:"color:#999999",customStyle:t.customStyle3,height:"60",maxlength:"18"},model:{value:t.form.idNumber,callback:function(e){t.$set(t.form,"idNumber",e)},expression:"form.idNumber"}})],1)],1)],1),a("v-uni-view",{staticStyle:{"margin-top":"60rpx"}},[t.disabled?t._e():a("v-uni-view",{staticClass:"buttoms",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.submit.apply(void 0,arguments)}}},[t._v("提交审核")])],1)],1)],1):t._e()},r=[]},e1ed:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("dd2b"),a("c9b5"),a("bf0f"),a("ab80"),a("5c47"),a("0506");var o=i(a("b741")),r={data:function(){return{shangxianSelect:"否",form:{id:"",authType:1,userType:1,realName:"",idNumber:"",idCardFront:"",idCardVerso:"",remark:""},skillImg:[],lifeImg:[],disabled:!0,lableStyle:{color:"#000000",fontSize:"28upx"},customStyle:{backgroundColor:"linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);",color:"#FFFFFF",border:0},customStyle3:{color:"#000000",border:0},count:""}},onLoad:function(){this.shangxianSelect="是",this.getUserInfo()},methods:{removeImg:function(t,e){1==e?this.skillImg.splice(t,1):2==e&&this.lifeImg.splice(t,1)},submit:function(){if(console.log(this.form),this.form.skillImg=this.skillImg,this.form.skillImg=this.form.skillImg.toString(),this.form.lifePhoto=this.lifeImg,this.form.lifePhoto=this.form.lifePhoto.toString(),this.form.realName)if(this.form.idNumber){/^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(this.form.idNumber)?(uni.showLoading({title:"提交中..."}),this.$Request.postJson("/app/userCertification/saveUserCertification",this.form).then((function(t){uni.hideLoading(),0==t.code?(uni.showToast({title:"认证提交成功！",icon:"none"}),setTimeout((function(){uni.navigateBack()}),1e3)):uni.showToast({title:t.msg,icon:"none"})}))):uni.showToast({title:"请输入正确的身份证号",icon:"none",duration:1e3})}else uni.showToast({title:"请输入身份证号",icon:"none",duration:1e3});else uni.showToast({title:"请输入真实姓名",icon:"none",duration:1e3})},getUserInfo:function(){var t=this;this.$Request.get("/app/userCertification/getMyUserCertification",{authType:1}).then((function(e){0==e.code&&e.data?(t.form.realName=e.data.realName?e.data.realName:"",t.form.idNumber=e.data.idNumber?e.data.idNumber:"",t.form.idCardFront=e.data.idCardFront?e.data.idCardFront:"",t.form.idCardVerso=e.data.idCardVerso?e.data.idCardVerso:"",t.form.id=e.data.id?e.data.id:"",0==e.data.isSubmit?0==e.data.status||1==e.data.status?t.disabled=!0:t.disabled=!1:t.disabled=!0,2==e.data.status&&(t.form.remark=e.data.remark?e.data.remark:"")):t.disabled=!1}))},addImages:function(t){var e=this,a=this;uni.chooseImage({count:1,sourceType:["album","camera"],success:function(i){for(var o=0;o<1;o++)e.$queue.showLoading("上传中..."),uni.uploadFile({url:a.config("APIHOST1")+"/alioss/upload",filePath:i.tempFilePaths[o],name:"file",success:function(a){1==t?e.form.idCardFront=JSON.parse(a.data).data:e.form.idCardVerso=JSON.parse(a.data).data,uni.hideLoading()}})}})},config:function(t){var e=null;if(t){var a=t.split(".");if(e=a.length>1?o.default[a[0]][a[1]]||null:o.default[t]||null,null==e){var i=cache.get("web_config");i&&(e=a.length>1?i[a[0]][a[1]]||null:i[t]||null)}}return e}}};e.default=r},fa91:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,".bg[data-v-17c7702e]{background-color:#fff}[data-v-17c7702e] .u-form-item{padding:0!important;line-height:10px!important}.buttoms[data-v-17c7702e]{background:linear-gradient(114deg,#ff6f9c,#ff98bd);padding:%?20?% 0;border-radius:%?12?%;color:#fff;text-align:center}",""]),t.exports=e}}]);