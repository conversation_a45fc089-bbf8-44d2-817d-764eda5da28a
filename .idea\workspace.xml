<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="c346f0bb-7cc8-4906-96a1-dd278e14143b" name="Default Changelist" comment="">
      <change beforePath="$PROJECT_DIR$/my/store/info.vue" beforeDir="false" afterPath="$PROJECT_DIR$/my/store/info.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/my/store/staffList.vue" beforeDir="false" afterPath="$PROJECT_DIR$/my/store/staffList.vue" afterDir="false" />
    </list>
    <ignored path="$PROJECT_DIR$/.tmp/" />
    <ignored path="$PROJECT_DIR$/temp/" />
    <ignored path="$PROJECT_DIR$/tmp/" />
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileEditorManager">
    <leaf SIDE_TABS_SIZE_LIMIT_KEY="300">
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/pages/index/index.vue">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="347">
              <caret line="50" column="34" lean-forward="true" selection-start-line="50" selection-start-column="34" selection-end-line="50" selection-end-column="34" />
              <folding>
                <element signature="n#style#0;n#view#1;n#view#0;n#template#0;n#!!top" expanded="true" />
                <element signature="n#style#0;n#swiper#0;n#view#0;n#view#1;n#view#0;n#template#0;n#!!top" expanded="true" />
                <element signature="n#style#0;n#image#0;n#swiper-item#0;n#block#0;n#swiper#0;n#view#0;n#view#1;n#view#0;n#template#0;n#!!top" expanded="true" />
                <element signature="n#style#0;n#view#1;n#view#1;n#view#0;n#template#0;n#!!top" expanded="true" />
                <element signature="n#style#0;n#view#0;n#view#1;n#view#1;n#view#0;n#template#0;n#!!top" expanded="true" />
                <element signature="n#style#0;n#image#0;n#view#0;n#view#0;n#view#1;n#view#1;n#view#0;n#template#0;n#!!top" expanded="true" />
                <element signature="n#style#0;n#image#0;n#view#1;n#view#1;n#view#1;n#view#1;n#view#0;n#template#0;n#!!top" expanded="true" />
                <element signature="n#style#0;n#image#0;n#view#0;n#view#0;n#view#0;n#view#0;n#view#2;n#view#1;n#view#1;n#view#0;n#template#0;n#!!top" expanded="true" />
                <element signature="n#style#0;n#view#0;n#u-popup#0;n#view#1;n#view#0;n#template#0;n#!!top" expanded="true" />
                <element signature="n#style#0;n#view#1;n#view#0;n#u-popup#0;n#view#1;n#view#0;n#template#0;n#!!top" expanded="true" />
                <element signature="e#9083#9121#0" expanded="true" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="true">
        <entry file="file://$PROJECT_DIR$/my/store/staffList.vue">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="315">
              <caret line="109" column="14" selection-start-line="109" selection-start-column="14" selection-end-line="109" selection-end-column="14" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/pages.json">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="11084">
              <caret line="652" column="48" selection-start-line="652" selection-start-column="48" selection-end-line="652" selection-end-column="48" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/common/config.js">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="374">
              <caret line="22" selection-start-line="22" selection-end-line="22" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/manifest.json">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="34">
              <caret line="2" column="31" selection-start-line="2" selection-start-column="31" selection-end-line="2" selection-end-column="31" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/uview-ui/README.md">
          <provider selected="true" editor-type-id="split-provider[text-editor;markdown-preview-editor]">
            <state split_layout="SPLIT">
              <first_editor relative-caret-position="289">
                <caret line="17" column="18" selection-start-line="17" selection-start-column="18" selection-end-line="17" selection-end-column="18" />
              </first_editor>
              <second_editor />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/my/hongniang/renzheng.vue">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="3995">
              <caret line="235" column="36" selection-start-line="235" selection-start-column="36" selection-end-line="235" selection-end-column="36" />
            </state>
          </provider>
        </entry>
      </file>
    </leaf>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Vue Single File Component" />
      </list>
    </option>
  </component>
  <component name="FindInProjectRecents">
    <findStrings>
      <find>/hn.png</find>
      <find>10.151.1.182:8083</find>
      <find>push</find>
      <find>login</find>
      <find>icon</find>
      <find>selectedIcon</find>
      <find>my/hongniang/admin</find>
      <find>pages/my/hongniang/admin</find>
      <find>&quot;game/detail&quot;,</find>
      <find>shopSet</find>
      <find>门店收益</find>
      <find>hongniang/admin</find>
      <find>icons</find>
      <find>staff-list</find>
    </findStrings>
    <replaceStrings>
      <replace />
    </replaceStrings>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="master" />
      </map>
    </option>
  </component>
  <component name="IdeDocumentHistory">
    <option name="CHANGED_PATHS">
      <list>
        <option value="$PROJECT_DIR$/my/hongniang/renzheng.vue" />
        <option value="$PROJECT_DIR$/common/config.js" />
        <option value="$PROJECT_DIR$/manifest.json" />
        <option value="$PROJECT_DIR$/components/custom-tabbar/custom-tabbar.vue" />
        <option value="$PROJECT_DIR$/pages/my/index.vue" />
        <option value="$PROJECT_DIR$/pages/index/index.vue" />
        <option value="$PROJECT_DIR$/my/store/info.vue" />
        <option value="$PROJECT_DIR$/pages.json" />
        <option value="$PROJECT_DIR$/my/store/staffList.vue" />
      </list>
    </option>
  </component>
  <component name="ProjectFrameBounds" extendedState="6">
    <option name="x" value="-7" />
    <option name="width" value="1920" />
    <option name="height" value="1039" />
  </component>
  <component name="ProjectView">
    <navigator proportions="" version="1">
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="Scope" />
      <pane id="ProjectPane">
        <subPane>
          <expand>
            <path>
              <item name="hl-user" type="b2602c69:ProjectViewProjectNode" />
              <item name="hl-user" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="hl-user" type="b2602c69:ProjectViewProjectNode" />
              <item name="hl-user" type="462c0819:PsiDirectoryNode" />
              <item name="my" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="hl-user" type="b2602c69:ProjectViewProjectNode" />
              <item name="hl-user" type="462c0819:PsiDirectoryNode" />
              <item name="my" type="462c0819:PsiDirectoryNode" />
              <item name="store" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="hl-user" type="b2602c69:ProjectViewProjectNode" />
              <item name="hl-user" type="462c0819:PsiDirectoryNode" />
              <item name="pages" type="462c0819:PsiDirectoryNode" />
            </path>
          </expand>
          <select />
        </subPane>
      </pane>
    </panes>
  </component>
  <component name="PropertiesComponent">
    <property name="SHARE_PROJECT_CONFIGURATION_FILES" value="true" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="ignore_missing_gitignore" value="true" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$/unpackage" />
    <property name="nodejs_interpreter_path.stuck_in_default_project" value="undefined stuck path" />
    <property name="nodejs_npm_path_reset_for_default_project" value="true" />
    <property name="nodejs_package_manager_path" value="npm" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\workplace\yuehuitong\hl-user\unpackage" />
      <recent name="E:\workplace\yuehuitong\hl-user\uview-ui\components" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="ruleStates">
      <list>
        <RuleState>
          <option name="name" value="ConfigurationTypeDashboardGroupingRule" />
        </RuleState>
        <RuleState>
          <option name="name" value="StatusDashboardGroupingRule" />
        </RuleState>
      </list>
    </option>
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="c346f0bb-7cc8-4906-96a1-dd278e14143b" name="Default Changelist" comment="" />
      <created>1748315483272</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748315483272</updated>
      <workItem from="1748315484854" duration="19894000" />
    </task>
    <task id="LOCAL-00001" summary="feat: 配置">
      <created>1748332078180</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1748332078180</updated>
    </task>
    <task id="LOCAL-00002" summary="feat: 门店">
      <created>1748347523138</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1748347523138</updated>
    </task>
    <option name="localTasksCounter" value="3" />
    <servers />
  </component>
  <component name="TimeTrackingManager">
    <option name="totallyTimeSpent" value="19894000" />
  </component>
  <component name="ToolWindowManager">
    <frame x="-8" y="-8" width="1936" height="1056" extended-state="6" />
    <editor active="true" />
    <layout>
      <window_info id="Favorites" side_tool="true" />
      <window_info active="true" content_ui="combo" id="Project" order="0" visible="true" weight="0.24973656" />
      <window_info id="Structure" order="1" side_tool="true" weight="0.25" />
      <window_info anchor="bottom" id="Docker" show_stripe_button="false" />
      <window_info anchor="bottom" id="Version Control" weight="0.30802602" />
      <window_info anchor="bottom" id="Terminal" visible="true" weight="0.329718" />
      <window_info anchor="bottom" id="Event Log" side_tool="true" />
      <window_info anchor="bottom" id="Messages" />
      <window_info anchor="bottom" id="Message" order="0" />
      <window_info anchor="bottom" id="Find" order="1" />
      <window_info anchor="bottom" id="Run" order="2" />
      <window_info anchor="bottom" id="Debug" order="3" weight="0.4" />
      <window_info anchor="bottom" id="Cvs" order="4" weight="0.25" />
      <window_info anchor="bottom" id="Inspection" order="5" weight="0.4" />
      <window_info anchor="bottom" id="TODO" order="6" />
      <window_info anchor="right" id="Commander" internal_type="SLIDING" order="0" type="SLIDING" weight="0.4" />
      <window_info anchor="right" id="Ant Build" order="1" weight="0.25" />
      <window_info anchor="right" content_ui="combo" id="Hierarchy" order="2" weight="0.25" />
    </layout>
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="1" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ORDER">
      <list>
        <option value="0" />
        <option value="2" />
        <option value="3" />
        <option value="1" />
      </list>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="feat: 配置" />
    <MESSAGE value="feat: 门店" />
    <option name="LAST_COMMIT_MESSAGE" value="feat: 门店" />
  </component>
  <component name="editorHistoryManager">
    <entry file="file://$PROJECT_DIR$/pages/my/index.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="232">
          <caret line="824" column="17" lean-forward="true" selection-start-line="824" selection-start-column="17" selection-end-line="824" selection-end-column="17" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/pages/hongniang/index.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="408">
          <caret line="24" column="33" lean-forward="true" selection-start-line="24" selection-start-column="33" selection-end-line="24" selection-end-column="33" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/pages/index/details.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="459">
          <caret line="27" column="21" lean-forward="true" selection-start-line="27" selection-start-column="21" selection-end-line="27" selection-end-column="21" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/pages/public/login.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="164">
          <caret line="642" column="29" lean-forward="true" selection-start-line="642" selection-start-column="29" selection-end-line="642" selection-end-column="29" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/uview-ui/components/u-tabbar-item/u-tabbar-item.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="340">
          <caret line="50" column="54" selection-start-line="50" selection-start-column="54" selection-end-line="50" selection-end-column="54" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/uview-ui/components/u-tabbar-item/props.js">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/uview-ui/LICENSE">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/components/custom-tabbar/custom-tabbar.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="229">
          <caret line="37" column="46" lean-forward="true" selection-start-line="37" selection-start-column="46" selection-end-line="37" selection-end-column="46" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/my/store/info.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="262">
          <caret line="25" column="34" selection-start-line="25" selection-start-column="34" selection-end-line="25" selection-end-column="34" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/package.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="170">
          <caret line="10" column="1" selection-start-line="10" selection-start-column="1" selection-end-line="10" selection-end-column="1" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/pages/index/index.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="347">
          <caret line="50" column="34" lean-forward="true" selection-start-line="50" selection-start-column="34" selection-end-line="50" selection-end-column="34" />
          <folding>
            <element signature="n#style#0;n#view#1;n#view#0;n#template#0;n#!!top" expanded="true" />
            <element signature="n#style#0;n#swiper#0;n#view#0;n#view#1;n#view#0;n#template#0;n#!!top" expanded="true" />
            <element signature="n#style#0;n#image#0;n#swiper-item#0;n#block#0;n#swiper#0;n#view#0;n#view#1;n#view#0;n#template#0;n#!!top" expanded="true" />
            <element signature="n#style#0;n#view#1;n#view#1;n#view#0;n#template#0;n#!!top" expanded="true" />
            <element signature="n#style#0;n#view#0;n#view#1;n#view#1;n#view#0;n#template#0;n#!!top" expanded="true" />
            <element signature="n#style#0;n#image#0;n#view#0;n#view#0;n#view#1;n#view#1;n#view#0;n#template#0;n#!!top" expanded="true" />
            <element signature="n#style#0;n#image#0;n#view#1;n#view#1;n#view#1;n#view#1;n#view#0;n#template#0;n#!!top" expanded="true" />
            <element signature="n#style#0;n#image#0;n#view#0;n#view#0;n#view#0;n#view#0;n#view#2;n#view#1;n#view#1;n#view#0;n#template#0;n#!!top" expanded="true" />
            <element signature="n#style#0;n#view#0;n#u-popup#0;n#view#1;n#view#0;n#template#0;n#!!top" expanded="true" />
            <element signature="n#style#0;n#view#1;n#view#0;n#u-popup#0;n#view#1;n#view#0;n#template#0;n#!!top" expanded="true" />
            <element signature="e#9083#9121#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/pages.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="11084">
          <caret line="652" column="48" selection-start-line="652" selection-start-column="48" selection-end-line="652" selection-end-column="48" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/common/config.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="374">
          <caret line="22" selection-start-line="22" selection-end-line="22" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/manifest.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="34">
          <caret line="2" column="31" selection-start-line="2" selection-start-column="31" selection-end-line="2" selection-end-column="31" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/uview-ui/README.md">
      <provider selected="true" editor-type-id="split-provider[text-editor;markdown-preview-editor]">
        <state split_layout="SPLIT">
          <first_editor relative-caret-position="289">
            <caret line="17" column="18" selection-start-line="17" selection-start-column="18" selection-end-line="17" selection-end-column="18" />
          </first_editor>
          <second_editor />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/my/hongniang/renzheng.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="3995">
          <caret line="235" column="36" selection-start-line="235" selection-start-column="36" selection-end-line="235" selection-end-column="36" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/my/store/staffList.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="315">
          <caret line="109" column="14" selection-start-line="109" selection-start-column="14" selection-end-line="109" selection-end-column="14" />
        </state>
      </provider>
    </entry>
  </component>
</project>