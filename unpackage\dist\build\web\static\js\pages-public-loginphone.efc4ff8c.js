(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-public-loginphone"],{"0840":function(e,t,n){"use strict";var i=n("16ed"),a=n.n(i);a.a},"16ed":function(e,t,n){var i=n("6d98");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var a=n("967d").default;a("21c1c868",i,!0,{sourceMap:!1,shadowMode:!1})},"6c60":function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={data:function(){return{phone:"",password:"",banners:[],invitation:"",loginName:"",sending:!1,sendTime:"获取验证码",count:60}},onLoad:function(){},methods:{forget:function(){uni.navigateTo({url:"/pages/public/forgetPwd"})},register:function(){uni.navigateTo({url:"/pages/public/register"})},inputChange:function(e){var t=e.currentTarget.dataset.key;this[t]=e.detail.value},navBack:function(){uni.navigateBack()},toLogin:function(){var e=this;this.$queue.loginClear();this.$queue.getData("openid");var t=this.phone,n=this.password;t?11!=t.length?this.$queue.showToast("请输入正确的手机号"):n?(this.$queue.showLoading("正在登录中..."),this.$Request.post("/app/Login/loginApp",{password:n,phone:t,openId:this.$queue.getData("openid")}).then((function(t){console.log(t),0==t.code?(e.$queue.setData("userId",t.user.userId),e.$queue.setData("token",t.token),e.$queue.setData("phone",t.user.phone),e.$queue.setData("userName",t.user.userName),e.$queue.setData("avatar",t.user.avatar),e.$queue.setData("invitationCode",t.user.invitationCode),e.$queue.setData("inviterCode",t.user.inviterCode),uni.hideLoading(),uni.switchTab({url:"/pages/my/index"})):(uni.hideLoading(),e.$queue.showToast(t.msg))}))):this.$queue.showToast("请输入密码"):this.$queue.showToast("请输入手机号")},getIsVip:function(){var e=this;this.$Request.get("/app/UserVip/isUserVip").then((function(t){0==t.code&&(console.log(t.data),e.$queue.setData("isVip",t.data))}))}}};t.default=i},"6d98":function(e,t,n){var i=n("c86c");t=i(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */uni-page-body[data-v-2e14974e]{height:100%;background:#fff!important}body.?%PAGE?%[data-v-2e14974e]{background:#fff!important}.send-msg[data-v-2e14974e]{border-radius:30px;color:#000;background:#fff;height:30px;font-size:14px;line-height:30px}.container[data-v-2e14974e]{top:0;padding-top:%?32?%;position:relative;width:100%;height:100%;overflow:hidden;background:#fff!important}.wrapper[data-v-2e14974e]{position:relative;z-index:90;background:#fff;padding-bottom:%?32?%}.input-content[data-v-2e14974e]{\r\n  /* margin-top: 300upx; */\r\n  /* padding-top: 300upx; */padding:%?32?% %?80?%}.confirm-btn[data-v-2e14974e]{width:%?600?%;height:%?80?%;line-height:%?80?%;border-radius:%?60?%;margin-top:%?32?%;background:linear-gradient(114deg,#ff6f9c,#ff98bd);color:#fff;font-size:%?32?%}.confirm-btn[data-v-2e14974e]:after{border-radius:60px}',""]),e.exports=t},"860b":function(e,t,n){"use strict";n.r(t);var i=n("6c60"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);t["default"]=a.a},"98db":function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){}));var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"container"},[n("v-uni-view",{staticClass:"wrapper"},[n("v-uni-view",{staticClass:"input-content"},[n("v-uni-view",{staticClass:"cu-form-group",staticStyle:{border:"2upx solid whitesmoke","margin-bottom":"20px","border-radius":"30px"}},[n("v-uni-view",{staticClass:"title text-black"},[e._v("账号")]),n("v-uni-input",{attrs:{type:"number",value:e.phone,placeholder:"请输入手机号",maxlength:"11","data-key":"phone"},on:{input:function(t){arguments[0]=t=e.$handleEvent(t),e.inputChange.apply(void 0,arguments)}}})],1),n("v-uni-view",{staticClass:"cu-form-group",staticStyle:{border:"2upx solid whitesmoke","border-radius":"30px"}},[n("v-uni-view",{staticClass:"title text-black"},[e._v("密码")]),n("v-uni-input",{attrs:{type:"password",placeholder:"请输入密码",maxlength:"20",value:e.password,"data-key":"password"},on:{input:function(t){arguments[0]=t=e.$handleEvent(t),e.inputChange.apply(void 0,arguments)},confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.toLogin.apply(void 0,arguments)}}}),n("v-uni-text",{staticClass:"send-msg",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.forget.apply(void 0,arguments)}}},[e._v("忘记密码")])],1)],1),n("v-uni-button",{staticClass:"confirm-btn",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toLogin.apply(void 0,arguments)}}},[e._v("登录")]),n("v-uni-view",{staticStyle:{"margin-top":"32px","text-align":"center"}},[n("v-uni-view",[n("v-uni-text",{staticStyle:{color:"linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%)","font-size":"28upx"}},[e._v("没有账号？")]),n("v-uni-text",{staticStyle:{color:"linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%)"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.register()}}},[e._v("立即注册")])],1)],1)],1)],1)},a=[]},d075:function(e,t,n){"use strict";n.r(t);var i=n("98db"),a=n("860b");for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);n("0840");var r=n("828b"),u=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"2e14974e",null,!1,i["a"],void 0);t["default"]=u.exports}}]);