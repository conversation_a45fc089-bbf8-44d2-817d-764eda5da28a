(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["package-pages-game-dongtai"],{"0a97":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={uNavbar:n("ddec").default,uIcon:n("3688").default,uPopup:n("0347").default,uModal:n("7e01").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticStyle:{"padding-bottom":"350rpx"}},[i("u-navbar",{attrs:{title:"TA的动态","is-back":!0,"border-bottom":!1,background:t.background,"title-color":"#111224"}}),i("v-uni-view",{staticClass:"bgjb"}),"{}"!=JSON.stringify(t.formData)?i("v-uni-view",{staticClass:"userbox",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goDetail()}}},[i("v-uni-image",{staticStyle:{width:"160rpx",height:"160rpx","border-radius":"30rpx"},attrs:{src:t.formData.userImg?t.formData.userImg:"../../../static/logo.png",mode:"aspectFill"}}),i("v-uni-view",{staticClass:"margin-left-sm"},[i("v-uni-view",{staticClass:"flex align-center"},[i("v-uni-view",{staticClass:"yhm"},[t._v(t._s(t.formData.realName))]),i("v-uni-image",{staticClass:"rencion",staticStyle:{width:"40rpx",height:"40rpx"},attrs:{src:n("d7d2")}})],1),i("v-uni-view",{staticClass:"flex align-center margin-tb-xs"},[1==t.formData.sex?i("v-uni-view",{staticClass:"sexicon"},[i("u-icon",{attrs:{name:"man",color:"#FFFFFF"}}),t._v(t._s(t.formData.age)+"岁")],1):t._e(),2==t.formData.sex?i("v-uni-view",{staticClass:"sexicons"},[i("u-icon",{attrs:{name:"woman",color:"#FFFFFF"}}),t._v(t._s(t.formData.age)+"岁")],1):t._e(),i("v-uni-view",{staticClass:"labe"},[t._v(t._s(t.formData.education))]),1==t.formData.marriageStatus?i("v-uni-view",{staticClass:"labe"},[t._v("未婚")]):t._e(),2==t.formData.marriageStatus?i("v-uni-view",{staticClass:"labe"},[t._v("离异")]):t._e(),i("v-uni-view",{staticClass:"labe"},[t._v(t._s(t.formData.userHeight)+"CM")])],1),i("v-uni-view",{staticStyle:{"font-size":"26rpx",color:"#999999"}},[t._v(t._s(t.formData.locationCity)+t._s(t.formData.locationCounty))])],1)],1):t._e(),i("v-uni-view",{staticClass:"list-box-item",style:{marginTop:"{}"==JSON.stringify(t.formData)?"20rpx":""}},[t.list.length>0?i("Head",{attrs:{list:t.list,userId:t.userId},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clickItem.apply(void 0,arguments)}}}):t._e(),0==t.list.length?i("v-uni-view",{staticClass:"emptybox"},[i("v-uni-view",{staticClass:"flex justify-center",staticStyle:{"margin-top":"200rpx"}},[i("v-uni-image",{staticStyle:{width:"300rpx",height:"341rpx"},attrs:{src:n("e003"),mode:"widthFix"}})],1),i("v-uni-view",{staticClass:"flex justify-center",staticStyle:{"font-size":"32rpx",color:"#999999","margin-top":"20rpx","font-weight":"700"}},[t._v("暂无数据")])],1):t._e()],1),"{}"!=JSON.stringify(t.formData)?i("v-uni-view",{staticClass:" taber"},[i("v-uni-view",{staticClass:"gunbi",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.callPhone.apply(void 0,arguments)}}},[i("v-uni-image",{staticStyle:{width:"40rpx",height:"40rpx"},attrs:{src:n("ba17")}}),1!=t.formData.isGetPhone?i("v-uni-text",{staticClass:"margin-left-xs"},[t._v("获取联系方式")]):i("v-uni-text",{staticClass:"margin-left-xs"},[t._v("查看联系方式")])],1),0==t.formData.isLike?i("v-uni-view",{staticClass:"xihuan",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.like()}}},[i("u-icon",{attrs:{name:"/static/images/index/xihuan.png",color:"#FFFFFF",size:"50"}}),i("v-uni-text",{staticClass:"margin-left-xs"},[t._v("喜欢")])],1):i("v-uni-view",{staticClass:"xihuans",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.like()}}},[i("u-icon",{attrs:{name:"/static/images/index/noxihuan.png",color:"#FFFFFF",size:"50"}}),i("v-uni-text",{staticClass:"margin-left-xs"},[t._v("不喜欢")])],1)],1):t._e(),i("u-popup",{attrs:{mode:"center","border-radius":"32",closeable:!0,"close-icon":"close-circle","close-icon-size":"48"},model:{value:t.show,callback:function(e){t.show=e},expression:"show"}},[i("v-uni-view",{staticClass:"pupobox"},[i("v-uni-view",{staticClass:"bg"},[i("v-uni-image",{staticStyle:{width:"100%","max-height":"508rpx"},attrs:{src:n("9ac0")}})],1),i("v-uni-view",{staticClass:"pupocot"},[i("v-uni-view",{staticClass:"pupotit"},[t._v("获取联系方式")]),i("v-uni-view",{staticClass:"patit"},[t._v("本次获取需支付"+t._s(t.money)+"元")]),i("v-uni-view",{staticClass:"vrn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openpay.apply(void 0,arguments)}}},[t._v("确认获取")])],1)],1)],1),i("u-popup",{attrs:{mode:"bottom","border-radius":"32",closeable:!0,"close-icon":"close-circle","close-icon-size":"48"},model:{value:t.payshow,callback:function(e){t.payshow=e},expression:"payshow"}},[i("v-uni-view",{staticClass:"pupoboxs"},[i("v-uni-view",{staticClass:"bg"},[i("v-uni-image",{staticStyle:{width:"100%","max-height":"508rpx"},attrs:{src:n("9ac0")}})],1),i("v-uni-view",{staticClass:"pupocot"},[i("v-uni-view",{staticClass:"pupotit"},[t._v("支付方式")]),t._l(t.openLists,(function(e,n){return i("v-uni-view",{key:n,staticClass:"flex align-center justify-between",staticStyle:{height:"100upx",padding:"30upx"}},[i("v-uni-image",{staticStyle:{width:"55upx",height:"55upx","border-radius":"50upx"},attrs:{src:e.image}}),i("v-uni-view",{staticStyle:{"font-size":"30upx","margin-left":"0upx",width:"70%"}},[t._v(t._s(e.text))]),i("v-uni-radio-group",{staticStyle:{"margin-left":"20upx"},attrs:{name:"openWay"},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.selectWay(e)}}},[i("v-uni-label",{staticClass:"tui-radio"},[i("v-uni-radio",{attrs:{color:"linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);",checked:t.openWay===e.id}})],1)],1)],1)})),i("v-uni-view",{staticClass:"btn margin-top",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.pay.apply(void 0,arguments)}}},[t._v("立即支付")])],2)],1)],1),i("hninfo",{ref:"hnPopup"}),i("u-modal",{attrs:{content:t.meContent,title:t.meTitle,"show-cancel-button":t.meShowCancel,"confirm-text":t.meConfirmText,"cancel-text":t.meCancelText},on:{cancel:function(e){arguments[0]=e=t.$handleEvent(e),t.meHandleClose.apply(void 0,arguments)},confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.meHandleBtn.apply(void 0,arguments)}},model:{value:t.meShowModel,callback:function(e){t.meShowModel=e},expression:"meShowModel"}})],1)},o=[]},"0eeb":function(t,e,n){t.exports=n.p+"static/logo.png"},"1d63":function(t,e,n){var i=n("6334");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("967d").default;a("4d44e25f",i,!0,{sourceMap:!1,shadowMode:!1})},2189:function(t,e,n){t.exports=n.p+"static/images/bgImg.png"},2838:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={uIcon:n("3688").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{},[n("v-uni-view",{staticClass:"u-navbar",class:{"u-navbar-fixed":t.isFixed,"u-border-bottom":t.borderBottom},style:[t.navbarStyle]},[n("v-uni-view",{staticClass:"u-status-bar",style:{height:t.statusBarHeight+"px"}}),n("v-uni-view",{staticClass:"u-navbar-inner",style:[t.navbarInnerStyle]},[t.isBack?n("v-uni-view",{staticClass:"u-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"u-icon-wrap"},[n("u-icon",{attrs:{name:t.backIconName,color:t.backIconColor,size:t.backIconSize}})],1),t.backText?n("v-uni-view",{staticClass:"u-icon-wrap u-back-text u-line-1",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()],1):t._e(),t.title?n("v-uni-view",{staticClass:"u-navbar-content-title",style:[t.titleStyle]},[n("v-uni-view",{staticClass:"u-title u-line-1",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),n("v-uni-view",{staticClass:"u-slot-content"},[t._t("default")],2),n("v-uni-view",{staticClass:"u-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?n("v-uni-view",{staticClass:"u-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+t.statusBarHeight+"px"}}):t._e()],1)},o=[]},"30f7":function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},n("7a76"),n("c9b5")},"320c":function(t,e,n){"use strict";n.r(e);var i=n("c4d4"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},4087:function(t,e,n){var i=n("c86c"),a=n("2ec5"),o=n("2189");e=i(!1);var s=a(o);e.push([t.i,"uni-page-body[data-v-0220621e]{background:#f2f6fc}body.?%PAGE?%[data-v-0220621e]{background:#f2f6fc}.rencion[data-v-0220621e]{width:%?40?%!important;height:%?40?%!important}.bgjb[data-v-0220621e]{width:100%;height:%?531?%;background-image:url("+s+");background-size:100% 100%;position:fixed;top:%?80?%;left:0;right:0;z-index:0}.userbox[data-v-0220621e]{background:linear-gradient(90deg,#e4e7f8,#f1e3f4 46%,#fde1ef);padding:%?30?%;display:flex;align-items:center;position:fixed;top:%?78?%;left:0;right:0;z-index:999}.userbox .yhm[data-v-0220621e]{font-size:%?42?%;font-family:PingFang SC;font-weight:700;margin-right:%?20?%}.userbox .sexicon[data-v-0220621e]{background:#38caff;border-radius:%?10?%;font-size:%?24?%;font-family:PingFang SC;font-weight:500;color:#fff;padding:%?4?% %?10?%;margin-right:%?10?%}.userbox .labe[data-v-0220621e]{background:#fff;border-radius:%?10?%;font-size:%?24?%;font-family:PingFang SC;font-weight:500;color:#999;padding:%?4?% %?10?%;margin-right:%?15?%}.list-box-item[data-v-0220621e]{margin-top:%?250?%;position:relative;z-index:99}.list-box-item .emptybox[data-v-0220621e]{background:#fff;border-radius:%?24?%;margin:0 %?30?%;padding:%?30?%;height:50vh}.gunbi[data-v-0220621e]{width:%?333?%;height:%?100?%;background:#686bfe;box-shadow:%?0?% %?10?% %?20?% %?0?% #e4e4e4;border-radius:%?55?%;display:flex;align-items:center;justify-content:center;color:#fff}.xihuan[data-v-0220621e]{width:%?333?%;height:%?100?%;background:linear-gradient(0deg,#ff6f9c,#ffa8c7);box-shadow:%?0?% %?10?% %?20?% %?0?% #ffdbe7;border-radius:%?55?%;display:flex;align-items:center;justify-content:center;color:#fff}.xihuans[data-v-0220621e]{width:%?333?%;height:%?100?%;background:linear-gradient(0deg,#cacad1,#dedee2);box-shadow:%?0?% %?10?% %?20?% %?0?% #dedee2;border-radius:%?55?%;display:flex;align-items:center;justify-content:center;color:#fff}.taber[data-v-0220621e]{display:flex;align-items:center;justify-content:space-between;padding:%?60?% %?30?%;position:fixed;bottom:0;left:0;right:0;z-index:999}.pupobox[data-v-0220621e]{width:%?596?%;background:#fff;border-radius:%?32?%;position:relative;padding:%?40?% %?30?% %?30?%}.pupobox .bg[data-v-0220621e]{height:%?225?%;position:absolute;top:0;left:0;right:0;z-index:0}.pupobox .pupocot[data-v-0220621e]{width:100%;position:relative;z-index:99}.pupobox .pupocot .pupotit[data-v-0220621e]{text-align:center;font-size:%?38?%;font-family:PingFang SC;font-weight:800;color:#1e1e1e}.pupobox .pupocot .tit[data-v-0220621e]{width:%?446?%;margin:%?25?% auto;text-align:center;font-size:%?32?%;font-family:PingFang SC;font-weight:500;color:#999}.pupobox .pupocot .patit[data-v-0220621e]{font-size:%?32?%;font-family:PingFang SC;font-weight:500;color:linear-gradient(114deg,#ff6f9c,#ff98bd);text-align:center;margin-bottom:%?40?%;margin-top:%?30?%}.pupobox .pupocot .vrn[data-v-0220621e]{width:%?494?%;height:%?92?%;background:linear-gradient(114deg,#ff6f9c,#ff98bd);border-radius:%?46?%;font-size:%?28?%;font-family:PingFang SC;font-weight:700;color:#fff;margin:%?10?% auto;display:flex;align-items:center;justify-content:center}.pupoboxs[data-v-0220621e]{width:100%;background:#fff;border-radius:%?32?%;position:relative;padding:%?40?% %?30?% %?30?%}.pupoboxs .bg[data-v-0220621e]{height:%?225?%;position:absolute;top:0;left:0;right:0;z-index:0}.pupoboxs .pupocot[data-v-0220621e]{width:100%;position:relative;z-index:99}.pupoboxs .pupocot .pupotit[data-v-0220621e]{text-align:center;font-size:%?38?%;font-family:PingFang SC;font-weight:800;color:#1e1e1e}.pupoboxs .pupocot .btn[data-v-0220621e]{width:100%;height:%?80?%;background:linear-gradient(114deg,#ff6f9c,#ff98bd);border-radius:%?46?%;font-size:%?28?%;font-family:PingFang SC;font-weight:700;color:#fff;margin:%?10?% auto;display:flex;align-items:center;justify-content:center}.sexicons[data-v-0220621e]{background:#edbef3;border-radius:%?10?%;font-size:%?24?%;font-family:PingFang SC;font-weight:500;color:#fff;padding:%?4?% %?10?%;margin-right:%?10?%}",""]),t.exports=e},4733:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if(Array.isArray(t))return(0,i.default)(t)};var i=function(t){return t&&t.__esModule?t:{default:t}}(n("8d0b"))},"4ae5":function(t,e,n){"use strict";n.r(e);var i=n("0a97"),a=n("f9c8");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("f549");var s=n("828b"),r=Object(s["a"])(a["default"],i["b"],i["c"],!1,null,"0220621e",null,!1,i["a"],void 0);e["default"]=r.exports},5907:function(t,e,n){"use strict";n.r(e);var i=n("ebc3"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},6334:function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.hnpopup[data-v-11897acf]{background:linear-gradient(0deg,#fff 40%,#ffe4e9);padding-bottom:%?40?%}.hnpopup .title[data-v-11897acf]{font-size:%?30?%;font-family:PingFang SC;font-weight:800;color:#000;text-align:center;padding:%?30?% 0}.hnpopup .box[data-v-11897acf]{background:#fff;border:1px solid #ffe9ed;border-radius:%?24?%;margin:%?40?% %?72?% 0;padding-bottom:%?40?%;text-align:center;position:relative}.hnpopup .box .hnavatr[data-v-11897acf]{position:absolute;top:%?-40?%;left:0;right:0;z-index:9}.hnpopup .box .hnavatr uni-image[data-v-11897acf]{width:%?120?%;height:%?120?%;border-radius:50%}.hnpopup .box .hnname[data-v-11897acf]{font-size:%?30?%;font-family:PingFang SC;font-weight:700;color:#ff6684;margin-top:%?100?%}.hnpopup .box .hnwx[data-v-11897acf]{font-size:%?26?%;font-family:PingFang SC;font-weight:500;color:#999;margin-top:%?10?%;display:flex;align-items:center;justify-content:center}.hnpopup .box .hnwx uni-image[data-v-11897acf]{width:%?23?%;height:%?25?%;margin-left:%?10?%}.hnpopup .box .hxwxm[data-v-11897acf]{margin-top:%?40?%}.hnpopup .box .hxwxm uni-image[data-v-11897acf]{width:%?299?%;height:%?282?%;border-radius:%?24?%}.hnpopup .box .hntit[data-v-11897acf]{font-size:%?26?%;font-family:PingFang SC;font-weight:500;color:#999}.hnpopup .box .btn[data-v-11897acf]{width:%?505?%;height:%?86?%;margin:0 auto;background:linear-gradient(114deg,#ff6f9c,#ff98bd);border-radius:%?43?%;font-size:%?28?%;font-family:PingFang SC;font-weight:700;color:#fff;display:flex;align-items:center;justify-content:center;margin-top:%?40?%}',""]),t.exports=e},8911:function(t,e,n){"use strict";var i=n("1d63"),a=n.n(i);a.a},"9ac0":function(t,e,n){t.exports=n.p+"static/images/index/beijing.png"},"9ea6":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={uPopup:n("0347").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"hnInfo"},[i("u-popup",{attrs:{mode:"bottom","border-radius":"30",closeable:!0,"close-icon-color":"#000000"},model:{value:t.hnshow,callback:function(e){t.hnshow=e},expression:"hnshow"}},[i("v-uni-view",{staticClass:"hnpopup"},[i("v-uni-view",{staticClass:"title"},[t._v("专属资深红娘助你脱单")]),i("v-uni-view",{staticClass:"box"},[i("v-uni-view",{staticClass:"hnavatr"},[i("v-uni-image",{attrs:{src:n("0eeb")}})],1),i("v-uni-view",{staticClass:"hnname"},[t._v(t._s(t.hnInfo.realName))]),t.hnInfo.wxCode?i("v-uni-view",{staticClass:"hnwx"},[t._v("微信："+t._s(t.hnInfo.wxCode)),i("v-uni-image",{attrs:{src:n("e31d")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.copyOrder(t.hnInfo.wxCode)}}})],1):t._e(),t.hnInfo.wxImg?i("v-uni-view",{staticClass:"hxwxm"},[i("v-uni-image",{attrs:{"show-menu-by-longpress":!0,src:t.hnInfo.wxImg},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.previewImage(t.hnInfo.wxImg)}}})],1):t._e(),t.hnInfo.wxImg?i("v-uni-view",{staticClass:"hntit"},[t._v("长按识别二维码添加红娘微信")]):t._e(),i("v-uni-view",{staticClass:"btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.makePhone(t.hnInfo.matchPhone)}}},[t._v("打电话")])],1)],1)],1)],1)},o=[]},a34e:function(t,e,n){var i=n("edc5");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("967d").default;a("b709bde6",i,!0,{sourceMap:!1,shadowMode:!1})},a4d7:function(t,e,n){"use strict";n.r(e);var i=n("9ea6"),a=n("5907");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("8911");var s=n("828b"),r=Object(s["a"])(a["default"],i["b"],i["c"],!1,null,"11897acf",null,!1,i["a"],void 0);e["default"]=r.exports},b706:function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(n("b7c7"));n("5ef2"),n("bf0f"),n("2797"),n("c223");var o=i(n("1a8a")),s=i(n("2bdc")),r=i(n("a4d7")),u={components:{empty:s.default,Head:o.default,hninfo:r.default},data:function(){return{background:{backgroundImage:"linear-gradient(90deg, #E4E7F8 0%, #F1E3F4 46%, #FDE1EF 100%)"},list:[],byUserId:"",userId:"",formData:{},page:1,limit:10,count:0,money:"",payshow:!1,show:!1,openLists:"",openWay:1,meShowModel:!1,meShowCancel:!0,meTitle:"提示",meContent:"",meConfirmText:"确认",meCancelText:"取消",meIndex:"",isVip:"",statusStauts:""}},onLoad:function(t){var e=this;uni.showLoading({title:"加载中..."});var n=navigator.userAgent.toLowerCase();-1!==n.indexOf("micromessenger")?this.$Request.get("/app/common/type/333").then((function(t){t.data&&t.data.value&&"是"==t.data.value?(e.openLists=[{image:"../../../static/images/my/zhifubao.png",text:"支付宝",id:1},{image:"../../../static/images/my/icon_weixin.png",text:"微信",id:2},{image:"../../../static/images/my/cz.png",text:"零钱",id:3}],e.openWay=2):(e.openLists=[{image:"../../../static/images/my/zhifubao.png",text:"支付宝",id:1},{image:"../../../static/images/my/cz.png",text:"零钱",id:3}],e.openWay=1)})):(this.openLists=[{image:"../../../static/images/my/zhifubao.png",text:"支付宝",id:1},{image:"../../../static/images/my/cz.png",text:"零钱",id:3}],this.openWay=1),this.userId=uni.getStorageSync("userId"),t.byUserId&&(this.byUserId=t.byUserId)},onShow:function(){var t=this;this.byUserId&&(this.getUserInfo(),this.getdongtai(),this.getInfo(),this.getRenZheng(),this.taskData()),this.$Request.getT("/app/common/type/324").then((function(e){0==e.code&&e.data&&e.data.value&&(t.money=e.data.value)}))},methods:{goDetail:function(){uni.navigateTo({url:"/package/pages/game/detail?byUserId="+this.formData.userId})},taskData:function(){var t=this;this.$Request.get("/app/userMoney/selectMyMoney").then((function(e){0==e.code&&e.data&&(t.mymoney=e.data.money)}))},meHandleBtn:function(){"m1"==this.meIndex?uni.navigateTo({url:"/my/vip/index"}):"m4"==this.meIndex?uni.navigateTo({url:"/my/setting/userinfo"}):"m6"==this.meIndex?uni.navigateTo({url:"/my/renzheng/index"}):"m7"==this.meIndex&&uni.navigateTo({url:"/my/wallet/Txmoney"})},meHandleClose:function(){this.meIndex},getRenZheng:function(){var t=this;this.$Request.get("/app/userCertification/getMyUserCertification",{authType:1}).then((function(e){0==e.code&&e.data?0==e.data.status?(t.statusStauts=1,uni.setStorageSync("statusStauts",t.statusStauts)):1==e.data.status?(t.statusStauts=2,uni.setStorageSync("statusStauts",t.statusStauts)):2==e.data.status&&(t.statusStauts=3,uni.setStorageSync("statusStauts",t.statusStauts)):(t.statusStauts=0,uni.setStorageSync("statusStauts",t.statusStauts))}))},callPhone:function(){var t=this;uni.getStorageSync("token")?this.$Request.get("/app/user/selectUserById").then((function(e){0==e.code&&e.data&&(e.data.phone?1==t.statusStauts?(t.meShowModel=!0,t.meTitle="提示",t.meContent="实名认证审核中,请通过后再来查看",t.meIndex="m6",t.meConfirmText="去查看",t.meShowCancel=!0):2==t.statusStauts?1==t.isVip?t.$refs.hnPopup.open(t.formData):(t.meShowModel=!0,t.meTitle="提示",t.meContent="开通会员获取红娘联系方式",t.meIndex="m1",t.meConfirmText="确认",t.meShowCancel=!0):3==t.statusStauts?(t.meShowModel=!0,t.meTitle="提示",t.meContent="实名审核被拒绝",t.meIndex="m6",t.meConfirmText="去认证",t.meShowCancel=!0):-1==t.statusStauts&&(t.meShowModel=!0,t.meTitle="提示",t.meContent="未实名认证，请先去实名认证",t.meIndex="m6",t.meConfirmText="去认证",t.meShowCancel=!0):(t.meShowModel=!0,t.meTitle="提示",t.meContent="请先去完善个人信息",t.meIndex="m9",t.meConfirmText="去完善",t.meShowCancel=!0))})):this.noLogin()},getdongtai:function(){var t=this;this.$Request.getT("/app/trends/getTrendsList?byUserId="+this.userId+"&page="+this.page+"&limit="+this.limit+"&userId="+this.byUserId).then((function(e){if(0==e.code){var n=e.data.records;n.forEach((function(t){t.trendsImage&&(t.trendsImage=t.trendsImage.split(","))})),1==t.page?t.list=n:t.list=[].concat((0,a.default)(t.list),(0,a.default)(n)),t.count=e.data.total}uni.hideLoading(),uni.stopPullDownRefresh()}))},getUserInfo:function(){var t=this;this.$Request.get("/app/user/selectUserById").then((function(e){0==e.code&&(t.isVip=e.data.isVip)}))},like:function(){var t=this,e=uni.getStorageSync("userId");1==t.statusStauts?(t.meShowModel=!0,t.meTitle="提示",t.meContent="实名认证审核中，请通过后去完善资料",t.meIndex="m6",t.meConfirmText="去查看",t.meShowCancel=!0):2==t.statusStauts?t.$Request.get("/app/userData/getUserDataInfo?userId="+e).then((function(e){0==e.code&&(e.data?t.$Request.postT("/app/scFollow/saveScFollow?byUserId="+t.byUserId+"&type=2").then((function(e){0==e.code&&(uni.showToast({title:e.msg,icon:"none",duration:3e3}),t.getInfo())})):(t.firstlogin=!1,t.meShowModel=!0,t.meTitle="提示",t.meContent="完善个人资料可以被更多人看到哦",t.meConfirmText="去完善",t.meIndex="m4",t.meShowCancel=!0))})):3==t.statusStauts?(t.meShowModel=!0,t.meTitle="提示",t.meContent="实名审核被拒绝，请先去认证",t.meIndex="m6",t.meConfirmText="去认证",t.meShowCancel=!0):-1==t.statusStauts&&(t.firstlogin=!1,t.meShowModel=!0,t.meTitle="提示",t.meContent="未实名认证，请先去实名认证",t.meConfirmText="去认证",t.meIndex="m6",t.meShowCancel=!0)},getInfo:function(){var t=this;this.$Request.get("/app/userData/getUserDataInfo?byUserId="+this.userId+"&userId="+this.byUserId).then((function(e){if(0==e.code&&e.data){if(t.formData=e.data,t.formData&&t.formData.userImg){var n=t.formData.userImg.split(",");t.formData.userImg=n[0]}t.$forceUpdate()}}))},clickItem:function(t){var e=this.$queue.getData("token");if(e){if(console.log(t),0==t.index)uni.navigateTo({url:"/package/pages/detail/listDetails?trendsId="+this.list[t.id].trendsId});else if(1==t.index){var n=this.$queue.getData("token");n?uni.navigateTo({url:"/my/gird/guanzhuDetail?userId="+this.list[t.id].userId}):this.goLoginInfo()}else if(2==t.index);else if(3==t.index)this.saveLove(this.list[t.id].trendsId);else if(9==t.index)this.guanzhu1(this.list[t.id].userId);else if(10==t.index){console.log(t);var i=t.id;this.$refs.hnPopup.open(i)}}else this.goLoginInfo()},guanzhu1:function(t){var e=this;this.$Request.postT("/app/scFollow/saveScFollow?byUserId="+t+"&type=1").then((function(t){0==t.code&&(uni.showToast({title:t.msg,icon:"none"}),e.page=1,e.getdongtai())}))},saveLove:function(t){var e=this,n={trendsId:t};this.$Request.postT("/app/trendsLike/saveTrendsLike",n).then((function(t){0==t.code?(e.page=1,e.getdongtai()):e.$queue.showToast(t.msg)}))},openpay:function(){this.show=!1,this.payshow=!0},selectWay:function(t){this.openWay=t.id},pay:function(){var t=this;if(uni.showLoading({title:"支付中..."}),this.payshow=!1,1==this.openWay){var e={classify:5,dataId:this.formData.dataId};this.$Request.postT("/app/userGetPhoneRecord/buyPhone",e).then((function(t){if(0==t.code){var e=document.createElement("div");e.innerHTML=t.data,document.body.appendChild(e),document.forms[0].submit(),uni.hideLoading()}else uni.showToast({icon:"none",title:"支付失败!"})}))}else if(2==this.openWay){var n={classify:3,dataId:this.formData.dataId};this.$Request.postT("/app/userGetPhoneRecord/buyPhone",n).then((function(e){0==e.code?(uni.hideLoading(),t.callPay(e.data)):uni.showToast({icon:"none",title:"支付失败!"})}))}else if(3==this.openWay)if(this.mymoney>=this.money){var i={classify:0,dataId:this.formData.dataId};this.$Request.postT("/app/userGetPhoneRecord/buyPhone",i).then((function(e){uni.hideLoading(),0==e.code?(uni.showToast({title:"支付成功",icon:"success"}),t.getUserInfo(),t.getInfo()):uni.showToast({icon:"none",title:e.msg})}))}else uni.hideLoading(),this.meShowModel=!0,this.meTitle="提示",this.meContent="零钱余额不足，请选去充值",this.meConfirmText="去充值",this.meIndex="m7",this.meShowCancel=!0},callPay:function(t){console.log(t),"undefined"===typeof WeixinJSBridge?document.addEventListener?document.addEventListener("WeixinJSBridgeReady",this.onBridgeReady(t),!1):document.attachEvent&&(document.attachEvent("WeixinJSBridgeReady",this.onBridgeReady(t)),document.attachEvent("onWeixinJSBridgeReady",this.onBridgeReady(t))):(console.log(1),this.onBridgeReady(t))},onBridgeReady:function(t){t.package&&(console.log(t,"++++++++"),WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:t.appid,timeStamp:t.timestamp,nonceStr:t.noncestr,package:t.package,signType:t.signType,paySign:t.sign},(function(e){console.log(e,"/*-/*-/*-"),"get_brand_wcpay_request:ok"===e.err_msg?(uni.showLoading({title:"支付成功"}),setTimeout((function(){uni.hideLoading()}),1e3)):uni.hideLoading(),WeixinJSBridge.log(t.err_msg)})))},isCheckPay:function(t,e,n){0==t?this.setPayment(e,n):(uni.hideLoading(),uni.showToast({title:"支付信息有误",icon:"none"}))},setPayment:function(t,e){console.log("*-*-*"),uni.requestPayment({provider:t,orderInfo:e,success:function(t){console.log(t),uni.hideLoading(),uni.showLoading({title:"支付成功"})},fail:function(t){console.log(t),uni.hideLoading()},complete:function(){uni.hideLoading()}})}},onReachBottom:function(){this.list.length==this.count?uni.showToast({title:"已经到底了",icon:"none"}):(this.page=this.page+1,this.getdongtai())},onPullDownRefresh:function(){this.page=1,this.getdongtai()}};e.default=u},b7c7:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,i.default)(t)||(0,a.default)(t)||(0,o.default)(t)||(0,s.default)()};var i=r(n("4733")),a=r(n("d14d")),o=r(n("5d6b")),s=r(n("30f7"));function r(t){return t&&t.__esModule?t:{default:t}}},ba17:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADEAAAAqCAYAAAAajbNEAAAC60lEQVRogdWZy0sXURTHP/4SbBEFLlKQilZFL6jAaFHZkygKDXpR0qZFC/0P2rZp0aLnJiIiiKBF2IOIMohMWhRFmGVBJtJDyLAwK8ITp+7AOOf+xpmBdOYDhx+/M/fMnO+cuY+5UyEiZKQGWAEsABYC04FpQCUw3kl/ASNAP/ASeAo8Br6blgmoTNleE90DNAFrgSrTIjsDwC3gkvtNjlYigVWJyGERGZCJoUNEdibMLZGIHSLSO0HJR7kmInWenFKJOGZOO/F8FZFGT26JRLTlQECYVk+Of61cx74NbDTeyeW4G/VORrPwDbGXgV05ExBGR8arYUdURAtwwoTlC014LvDOJ2IW0JdzAQFPgOXBn1LowFnTNL8sAxqD7IJKLAGeFUiE8gmoJVSJI6ZJ/tG12xZcJWY6VUXkJrC15IasorIJqFYRDQUWoZN1g4qoN4eKRb0qmZ0h5XbgBjAMLAb2AzNMq3iGgHNANzAV2A5siI3wMx+zzIpnVESaPIuwWhG5Hxs5ljsiUuM5T6NpOT6daUXs81w4sJKI9JsIS58nNmx7TUQ8z9OI6PJcMGqtJsrS4omL2isTVZ63Je9T5uee1zuWTuOxdBiPpd14yjOaRkSStr+Nx2LW/hmvFTCcpvF647GsNh7LSuOxrDGe8nxJ27GbPc9v2D6YCIt2/ime2MAOmIh4LqaphHIB2G28/95FOoJV5TjUAQ/cbxTd0zpvvPF06wIwyxagdry7wKBb2ze7CSsNI+6m6AuOTpSbgXUZclmdVURe0JtYk/ZxyhtXdEQseiV0I/tFkSvxUAXg3uyKWol5QA8pZ8Y8cTQQQEEr0QUsCjuKVolB34tTkUTop7BVwMfogaKIeA8sDUajKCrim/Hmi+tuh7KnXFYq4pTx5gN9bA4B24DPcRkFe7GPcrR1o5+FTzsbMkc9BCK0ImeAg5PUT167G9nm7Gea4OhHljnu7azaifkfc0iFS/KH+x7SC7zJfDbgD1RoI+byKuK+AAAAAElFTkSuQmCC"},c4d4:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("64aa");var i=uni.getSystemInfoSync(),a={},o={name:"u-navbar",props:{height:{type:[String,Number],default:""},backIconColor:{type:String,default:"#606266"},backIconName:{type:String,default:"nav-back"},backIconSize:{type:[String,Number],default:"44"},backText:{type:String,default:""},backTextStyle:{type:Object,default:function(){return{color:"#606266"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleColor:{type:String,default:"#606266"},titleBold:{type:Boolean,default:!1},titleSize:{type:[String,Number],default:32},isBack:{type:[Boolean,String],default:!0},background:{type:Object,default:function(){return{background:"#ffffff"}}},isFixed:{type:Boolean,default:!0},immersive:{type:Boolean,default:!1},borderBottom:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},customBack:{type:Function,default:null}},data:function(){return{menuButtonInfo:a,statusBarHeight:i.statusBarHeight}},computed:{navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),t},titleStyle:function(){var t={};return t.left=(i.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(i.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44}},created:function(){},methods:{goBack:function(){"function"===typeof this.customBack?this.customBack.bind(this.$u.$parent.call(this))():uni.navigateBack()}}};e.default=o},cbe8:function(t,e,n){"use strict";var i=n("a34e"),a=n.n(i);a.a},d14d:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)},n("01a2"),n("e39c"),n("bf0f"),n("844d"),n("18f7"),n("de6c"),n("08eb")},ddec:function(t,e,n){"use strict";n.r(e);var i=n("2838"),a=n("320c");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("cbe8");var s=n("828b"),r=Object(s["a"])(a["default"],i["b"],i["c"],!1,null,"1d7f90d0",null,!1,i["a"],void 0);e["default"]=r.exports},e31d:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABcAAAAZCAYAAADaILXQAAAAAXNSR0IArs4c6QAAA15JREFUSEvNVc9rXFUU/s69971JhhQp/ZEmabEVXFWstrhw50ZERNwoRWlaiRkSUzPUdiFUKA+6KagLDeNk5i0UV5qgIiKoC0HwHyiIuii6SiAxEkyGedPpe+czd8hIOjGZSVZeeIt333nfPfc75/uOoGPFcfyoql4GcJRk2vm9y7sVkRqAbwF8I53B1Wr1CZLvADgCgHsA98COpAFwl+R7LfBKpRIAOAwgMMbYLMsOiYjdA7APrfvHWntMVT9UVUqpVBqw1j4vIq8COOhPBVATESW57Wb/deBmtr+JyIyqrhtjYgCnZXZ29oKIvE3yLwAL/mcR2QsdGcm8iJwh+XOWZW855260wKvV6meq+iSA881m844HHxgY6JmRWq2G/v5+o6ovARg3xryWZdkbIvKYVCqVLwE8kmXZM1NTU7/3jLolMIoiMzg4+LIx5irJCQCX2+BzAB631j47Pj5+J4oiNzIycihJkiAIgl3pIVlfXl7+e2hoyBf/EoDX7wP3tJA82wYvl8snNzrlpoicJNnc4SZGRLwGvnfOzaZp2ugJPI7j46p6XUQeBHCP5LbsSbrNbz+o6sfOOd9h3TOfm5uzq6urB9M0dWEY7kpLkiTJ9PT0erVa9Yd1B99PQTdF2B3cB24o7GiapqFzbsfMfbEbjUZtZWVlteeCzszMnArD8F0AD4tIcwfOrTHmnqp+3Ww2P8jlcklPtJTL5RERmRaRE94Vd1CrIZkB+Cmfz3+aJInvqu60+IIuLCwcCIJgV+Pq6+tjGIZ3R0dH6/+fgrbtt7NzjDGaJMlasVhc3+r1PXdLHMenSN4i+RCAZptzVQ1EZF1VP1paWvo8iiKvytbqBN+Iu0byXMsVt8q/VCqdsNZeE5HjHQW1quqHwReNRuO7YrHoVbkNPE3TSefcVQBnvSveZ1y+oGtraw/U6/VtBc3lcr7v6xMTE771/tWAN7vh4eGLJK+kaXrRWttP8pgH/4rkaWvt04VC4Y/9KNRPrDiOz5O8ISKvFAqF2/Pz88aDf0LyKRG5pKr7Avdz1w8bABc2ptGLk5OTv7QmWqVSeQ7ALQAHAPjrqt/fww08Pd648iR/DMPwzbGxsT/b4H7zBa8wETksIl55PS9PyaZF3DbGvL+4uPhrFEU+QfwD3Kw9SBuidI8AAAAASUVORK5CYII="},ebc3:function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(n("9b8e")),o=a.default.extend({name:"hnInfo",props:{},data:function(){return{hnshow:!1,hnInfo:{}}},computed:{},methods:{open:function(t){var e=this;this.$Request.get("/app/userRoleMatchmaker/getMatchInfoByUserId",{userId:t.userId}).then((function(t){console.log(t),0===t.code?(e.hnInfo=t.data,e.hnshow=!0):e.$queue.showToast(t.msg)}))},makePhone:function(t){uni.makePhoneCall({phoneNumber:t})},copyOrder:function(t){var e=this;uni.setClipboardData({data:t,success:function(){e.$queue.showToast("复制成功")}})}},watch:{},mounted:function(){},beforeUpdate:function(){},updated:function(){},activated:function(){},deactivated:function(){},beforeDestroy:function(){}});e.default=o},edc5:function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.u-navbar[data-v-1d7f90d0]{width:100%}.u-navbar-fixed[data-v-1d7f90d0]{position:fixed;left:0;right:0;top:0;z-index:991}.u-status-bar[data-v-1d7f90d0]{width:100%}.u-navbar-inner[data-v-1d7f90d0]{display:flex;flex-direction:row;justify-content:space-between;position:relative;align-items:center}.u-back-wrap[data-v-1d7f90d0]{display:flex;flex-direction:row;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.u-back-text[data-v-1d7f90d0]{padding-left:%?4?%;font-size:%?30?%}.u-navbar-content-title[data-v-1d7f90d0]{display:flex;flex-direction:row;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0}.u-navbar-centent-slot[data-v-1d7f90d0]{flex:1}.u-title[data-v-1d7f90d0]{line-height:%?60?%;font-size:%?32?%;flex:1}.u-navbar-right[data-v-1d7f90d0]{flex:1;display:flex;flex-direction:row;align-items:center;justify-content:flex-end}.u-slot-content[data-v-1d7f90d0]{flex:1;display:flex;flex-direction:row;align-items:center}',""]),t.exports=e},f2cb:function(t,e,n){var i=n("4087");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("967d").default;a("7aaa07ba",i,!0,{sourceMap:!1,shadowMode:!1})},f549:function(t,e,n){"use strict";var i=n("f2cb"),a=n.n(i);a.a},f9c8:function(t,e,n){"use strict";n.r(e);var i=n("b706"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a}}]);