<template>
	<view class="container">
		<!-- 顶部统计区域 -->
		<view class="top-section">
			<view class="stats-container">
				<view class="stats-row">
					<view class="stat-item">
						<view class="stat-number">{{ orderCount }}</view>
						<view class="stat-label">订单数</view>
					</view>
					<view class="stat-item">
						<view class="stat-number">{{ totalRevenue }}</view>
						<view class="stat-label">收益</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 订单列表 -->
		<view class="order-list">
			<view class="order-item" v-for="(order, index) in orderList" :key="index">
				<view class="order-header">
					<view class="order-title">{{ order.title }}</view>
					<view class="order-amount">+{{ order.amount }} 元</view>
				</view>
				<view class="order-details">
					<view class="detail-item">
						<text class="detail-label">订单编号：</text>
						<text class="detail-value">{{ order.orderNo }}</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">订单时间：</text>
						<text class="detail-value">{{ order.orderTime }}</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">会员名称：</text>
						<text class="detail-value">{{ order.memberName }}</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">红娘：</text>
						<text class="detail-value">{{ order.matchmaker }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			storeId: '',
			storeName: '',
			orderCount: 42,
			totalRevenue: '64500',
			orderList: [
				{
					title: '购买月度会员',
					amount: '68',
					orderNo: 'xxe55554rerereree',
					orderTime: '2025-02-24 15:32:00',
					memberName: '陆毅',
					matchmaker: '陈晨晨'
				},
				{
					title: '购买月度会员',
					amount: '68',
					orderNo: 'xxe55554rerereree',
					orderTime: '2025-02-24 15:32:00',
					memberName: '陆毅',
					matchmaker: '陈晨晨'
				},
				{
					title: '购买月度会员',
					amount: '68',
					orderNo: 'xxe55554rerereree',
					orderTime: '2025-02-24 15:32:00',
					memberName: '陆毅',
					matchmaker: '陈晨晨'
				},
				{
					title: '购买月度会员',
					amount: '68',
					orderNo: 'xxe55554rerereree',
					orderTime: '2025-02-24 15:32:00',
					memberName: '陆毅',
					matchmaker: '陈晨晨'
				}
			]
		}
	},
	onLoad(options) {
		// 获取传递的门店信息
		if (options.storeId) {
			this.storeId = options.storeId
			this.storeName = options.storeName
		}
		this.loadOrderData()
	},
	methods: {
		loadOrderData() {
			// 这里可以调用API获取订单数据
			// this.$Request.get("/app/store/orders", {
			//     storeId: this.storeId
			// }).then(res => {
			//     if (res.code == 0) {
			//         this.orderList = res.data.list
			//         this.orderCount = res.data.orderCount
			//         this.totalRevenue = res.data.totalRevenue
			//     }
			// });
		}
	}
}
</script>

<style scoped>
page {
	background: #f5f5f5;
}

.container {
	min-height: 100vh;
}

.top-section {
	padding: 20rpx;
}

.stats-container {
	display: flex;
	flex-direction: column;
	padding: 40rpx;
	background: #FFFFFF;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.stats-row {
	display: flex;
	justify-content: space-around;
}

.stat-item {
	text-align: center;
	color: #333333;
}

.stat-number {
	font-size: 48rpx;
	font-weight: bold;
	margin-bottom: 10rpx;
}

.stat-label {
	font-size: 28rpx;
	color: #666666;
}

.order-list {
	padding: 0 20rpx;
}

.order-item {
	background: #FFFFFF;
	border-radius: 12rpx;
	margin-bottom: 20rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.order-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.order-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
}

.order-amount {
	font-size: 32rpx;
	font-weight: bold;
	color: #FF6B9D;
}

.order-details {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.detail-item {
	display: flex;
	font-size: 28rpx;
	line-height: 1.5;
}

.detail-label {
	color: #666666;
	min-width: 160rpx;
}

.detail-value {
	color: #333333;
	flex: 1;
}
</style>