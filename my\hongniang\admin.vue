<!--
 * @name: 
 * @Author: 刘大可
 * @Date: 2024-03-01 14:29:01
 * @LastEditors: 刘大可
 * @LastEditTime: 2024-03-11 10:38:15
-->
<template>
	<view class="admin-page">
		<u-navbar :is-back="true" title="红娘工作台" :background="background" :border-bottom="false"
			back-icon-color="#FFFFFF" title-color="#FFFFFF"></u-navbar>
		<view class="bgTop">
			<view class="static">
				<view class="flex userInfo">
					<image class="avatar" :src="userInfo.avatar || '/static/logo.png'" mode="scaleToFill" />
					<view class="flex align-center">
						<view class="nickname">
							{{ userInfo.realName }}
						</view>

						<view class="auth">
							<image src="../static/admin/auth.png" mode="widthFix" />
							已认证
						</view>
					</view>
					<!-- <view class="loginDay">
					登录累计<text class="nums">12</text>天
				</view> -->
				</view>
				<view class="flex margin-top justify-between">
					<view class="item" @click="goMyUserList">
						<view class="margin-top-sm">
							<view class="key">我服务的会员</view>
							<view class="val" style="font-size: 44rpx;">{{ hnData.userDataCount}}</view>
						</view>
						<view class="flex align-end justify-end">
							<image src="../static/admin/huiyuan.png" style="width: 138rpx;height:100rpx;"></image>
						</view>
					</view>
					<!-- 	<view class="item2" @click="goMyDongtai">
						<view>
							<view class="key">我发布的动态</view>
							<view class="val">{{ hnData.trendsCount }}</view>
						</view>
						<image src="../static/admin/dongtai.png" style="width: 83rpx;height: 104rpx;"></image>
					</view> -->
					<view class="item3">
						<view class="item2" @click="goMyDongtai">
							<view>
								<view class="key">我发布的动态</view>
								<view class="val">{{ hnData.trendsCount }}</view>
							</view>
							<image src="../static/admin/dongtai.png" style="width: 83rpx;height: 104rpx;"></image>
						</view>
						<view class="item6" @click="goMyGuodong">
							<view>
								<view class="key">活动管理</view>
								<view class="vals">查看活动信息</view>
							</view>
							<image src="../static/admin/huodong.png" style="width:110rpx;height: 104rpx;"></image>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="libox">
			<view class="tit row">
				全部会员
				<view class="send row" @click="goChangeType">
					<image class="icon" src="../static/admin/add.png" mode="scaleToFill" />发布
				</view>
			</view>
			<list :list="list" @click="clickItem"></list>

			<block v-if="list.length == 0">
				<view class="flex justify-center" style="margin-top: 200rpx;">
					<image src="../../static/images/empty.png" style="width: 414rpx;height: 269rpx;" mode="">
					</image>
				</view>
				<view class="flex justify-center" style="font-size: 32rpx;color: #999999;margin-top: 20rpx;">
					暂无数据
				</view>
			</block>

		</view>
		<view class="s-col is-col-24" v-if="list.length > 0">
			<load-more :status="loadingType" :contentText="contentText"></load-more>
		</view>
	</view>
</template>

<script>
	import Vue from "vue";
	import list from "@/components/viplist.vue";
	import empty from '@/components/empty'

	export default Vue.extend({
		components: {
			list,
			empty
		},
		data() {
			return {
				background: {
					backgroundImage: 'linear-gradient(101deg, rgba(250,95,151,0.71) 0%, rgba(252,189,215,0.71) 100%)'
				},
				list: [],
				page: 1,
				userInfo: {},
				loadingType: 0,
				contentText: {
					contentdown: '上拉显示更多',
					contentrefresh: '正在加载...',
					contentnomore: '没有更多数据了'
				},
				hnData: {},

			};
		},
		computed: {},
		methods: {
			goMyUserList() {
				uni.navigateTo({
					url: "/my/hongniang/myUsers"
				})
			},
			QixnxianList(type = 0) {
				uni.navigateTo({
					url: '/my/hongniang/qxList?type=' + type
				})
			},
			goChangeType() {
				uni.navigateTo({
					url: "/my/hongniang/changeType"
				})
			},
			goMyDongtai() { //红娘发布的动态
				uni.navigateTo({
					url: "/my/hongniang/myDongtai"
				})
			},
			goMyDongtai2() {
				uni.navigateTo({
					url: "/my/hongniang/myDongtai2"
				})
			},
			goMyGuodong() { //红娘活动管理
				uni.navigateTo({
					url: "/my/Events/hnList"
				})
			},
			getMyUser() {
				uni.showLoading({
					title: '加载中',
					mask: true
				})
				if (this.page == 1) {
					this.$Request.get("/app/dataCenter/getMatcherData").then(res => {
						if (res.code === 0) {
							this.hnData = res.data
						}
					});
				}
				this.$Request.getT('/app/userData/getMatherDataList', {
					page: this.page,
					limit: 10,
					// searchKey:"",
					// sex:"",
					// status:""
				}).then(res => {
					uni.hideLoading();
					uni.stopPullDownRefresh();
					if (res.code == 0) {
						this.list = this.page == 1 ? res.data.records : [
							...this.list,
							...res.data.records
						]
						if (res.data.pages > res.data.current) {
							this.loadingType = 0
						} else {
							this.loadingType = 2
						}
					}
				})
			},

			getUserInfo() {
				this.$Request.get("/app/user/selectUserById").then(res => {
					if (res.code === 0) {
						this.userInfo = res.data
					}
				})
			},
			clickItem(e) {
				this.$Request.post('/app/userRoleMatchmaker/saveUserRoleMatchmaker', {
					userId: e.item.userId
				}).then(res => {
					if (res.code == 0) {
						uni.showToast({
							title: "接管成功"
						})
						this.getMyUser()
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none"
						})
					}
				})
			}
		},
		watch: {},

		// 页面周期函数--监听页面加载
		onLoad() {

			this.getUserInfo()
		},
		// 页面周期函数--监听页面初次渲染完成
		onReady() {},
		// 页面周期函数--监听页面显示(not-nvue)
		onShow() {
			this.page = 1
			this.getMyUser()

		},
		// 页面周期函数--监听页面隐藏
		onHide() {},
		// 页面周期函数--监听页面卸载
		onUnload() {},
		// 页面处理函数--监听用户下拉动作
		onPullDownRefresh() {
			this.page = 1
			this.getMyUser()
		},
		// 页面处理函数--监听用户上拉触底
		onReachBottom() {
			if (this.loadingType == 0) {
				this.page++
				this.getMyUser()
			}

		},
		// 页面处理函数--监听页面滚动(not-nvue)
		// onPageScroll(event) {},
		// 页面处理函数--用户点击右上角分享
		// onShareAppMessage(options) {},
	});
</script>

<style scoped lang="scss">
	@import "./main.scss";

	page {
		height: 100%;
		background: #F9F9F9;
		// background: linear-gradient(0deg, #F5F5F5 21%, #FFFFFF 100%);

	}

	.bgTop {
		height: 350rpx;
		position: relative;
		// background: linear-gradient(101deg, rgba(250, 95, 151, 0.71) 0%, rgba(252, 189, 215, 0.71) 100%);
	}

	.bgTop::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(101deg, rgba(250, 95, 151, 0.71) 0%, rgba(252, 189, 215, 0.71) 100%);
		// background: linear-gradient(to right, #FFE5EA 0%, #E4F5FB 49%, #FFF0EE 100%);
		z-index: 1;
		/* 确保在垂直渐变之上 */
	}

	.bgTop::after {
		content: "";
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(to bottom, transparent 50%, #F9F9F9 100%);
		/* 垂直渐变，结束颜色为透明 */
		z-index: 2;
		/* 确保在水平渐变之上 */
	}
</style>