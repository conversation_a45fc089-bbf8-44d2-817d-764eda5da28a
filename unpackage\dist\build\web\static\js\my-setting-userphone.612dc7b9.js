(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["my-setting-userphone"],{3461:function(n,e,t){"use strict";var i=t("7735"),o=t.n(i);o.a},6787:function(n,e,t){"use strict";t.r(e);var i=t("f51c"),o=t.n(i);for(var a in i)["default"].indexOf(a)<0&&function(n){t.d(e,n,(function(){return i[n]}))}(a);e["default"]=o.a},"6d3c":function(n,e,t){var i=t("c86c");e=i(!1),e.push([n.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */uni-page-body[data-v-215221dd]{background:#1c1b20}body.?%PAGE?%[data-v-215221dd]{background:#1c1b20}.send-msg[data-v-215221dd]{border-radius:30px;color:#fff;height:30px;font-size:14px;line-height:30px;background:#5e81f9}.container[data-v-215221dd]{padding-top:%?32?%;position:relative;width:100%;height:100%;overflow:hidden;background:#1c1b20}.wrapper[data-v-215221dd]{position:relative;z-index:90;background:#1c1b20;padding-bottom:20px}.input-content[data-v-215221dd]{padding:%?32?% %?80?%}.confirm-btn[data-v-215221dd]{width:%?600?%;height:%?80?%;line-height:%?80?%;border-radius:%?60?%;margin-top:%?32?%;background:#5e81f9;color:#fff;font-size:%?32?%}.confirm-btn[data-v-215221dd]:after{border-radius:60px}',""]),n.exports=e},7735:function(n,e,t){var i=t("6d3c");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[n.i,i,""]]),i.locals&&(n.exports=i.locals);var o=t("967d").default;o("5647e189",i,!0,{sourceMap:!1,shadowMode:!1})},a5da:function(n,e,t){"use strict";t.r(e);var i=t("e85a"),o=t("6787");for(var a in o)["default"].indexOf(a)<0&&function(n){t.d(e,n,(function(){return o[n]}))}(a);t("3461");var s=t("828b"),d=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,"215221dd",null,!1,i["a"],void 0);e["default"]=d.exports},e85a:function(n,e,t){"use strict";t.d(e,"b",(function(){return o})),t.d(e,"c",(function(){return a})),t.d(e,"a",(function(){return i}));var i={uModal:t("7e01").default},o=function(){var n=this,e=n.$createElement,t=n._self._c||e;return t("v-uni-view",{staticClass:"container"},[t("v-uni-view",{staticClass:"wrapper"},[t("v-uni-view",{staticClass:"input-content"},[t("v-uni-view",{staticClass:"cu-form-group",staticStyle:{border:"2upx solid whitesmoke","margin-bottom":"20px","border-radius":"30px"}},[t("v-uni-view",{staticClass:"title"},[n._v("手机号")]),t("v-uni-input",{attrs:{type:"number",value:n.phone,placeholder:"请输入新手机号",maxlength:"11","data-key":"phone"},on:{input:function(e){arguments[0]=e=n.$handleEvent(e),n.inputChange.apply(void 0,arguments)}}})],1),t("v-uni-view",{staticClass:"cu-form-group",staticStyle:{border:"2upx solid whitesmoke","margin-bottom":"20px","border-radius":"30px"}},[t("v-uni-text",{staticClass:"title"},[n._v("验证码")]),t("v-uni-input",{attrs:{type:"number",value:n.code,placeholder:"请输入验证码",maxlength:"6","data-key":"code"},on:{input:function(e){arguments[0]=e=n.$handleEvent(e),n.inputChange.apply(void 0,arguments)},confirm:function(e){arguments[0]=e=n.$handleEvent(e),n.toLogin.apply(void 0,arguments)}}}),t("v-uni-button",{staticClass:"send-msg",attrs:{disabled:n.sending},on:{click:function(e){arguments[0]=e=n.$handleEvent(e),n.sendMsg.apply(void 0,arguments)}}},[n._v(n._s(n.sendTime))])],1)],1),t("v-uni-button",{staticClass:"confirm-btn",on:{click:function(e){arguments[0]=e=n.$handleEvent(e),n.toLogin.apply(void 0,arguments)}}},[n._v("保存")])],1),t("u-modal",{attrs:{content:n.meContent,title:n.meTitle,"show-cancel-button":n.meShowCancel,"confirm-text":n.meConfirmText,"cancel-text":n.meCancelText,"mask-close-able":!1},on:{cancel:function(e){arguments[0]=e=n.$handleEvent(e),n.meHandleClose.apply(void 0,arguments)},confirm:function(e){arguments[0]=e=n.$handleEvent(e),n.meHandleBtn.apply(void 0,arguments)}},model:{value:n.meShowModel,callback:function(e){n.meShowModel=e},expression:"meShowModel"}})],1)},a=[]},f51c:function(n,e,t){"use strict";t("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={data:function(){return{meShowModel:!1,meShowCancel:!0,meTitle:"提示",meContent:"",meConfirmText:"确认",meCancelText:"取消",meIndex:"",code:"",phone:"",password:"",sending:!1,sendTime:"获取验证码",count:60,logining:!1}},methods:{meHandleBtn:function(){this.meIndex},meHandleClose:function(){this.meIndex},sendMsg:function(){var n=this,e=this.phone;e?11!==e.length?this.$queue.showToast("请输入正确的手机号"):(this.$queue.showLoading("正在发送验证码..."),this.$Request.getT("/msg/sendMsg/"+e+"/bind").then((function(e){0===e.status?(n.sending=!0,n.$queue.showToast("验证码发送成功请注意查收"),n.countDown(),uni.hideLoading()):(uni.hideLoading(),n.meShowModel=!0,n.meTitle="短信发送失败",n.meContent=e.msg?e.msg:"请一分钟后再获取验证码",n.meIndex="m1")}))):this.$queue.showToast("请输入手机号")},countDown:function(){var n=this.count;1===n?(this.count=60,this.sending=!1,this.sendTime="获取验证码"):(this.count=n-1,this.sending=!0,this.sendTime=n-1+"秒后重新获取",setTimeout(this.countDown.bind(this),1e3))},inputChange:function(n){var e=n.currentTarget.dataset.key;this[e]=n.detail.value},navBack:function(){uni.navigateBack()},navTo:function(n){uni.navigateTo({url:n})},toLogin:function(){var n=this;if(""==this.code)this.$queue.showToast("请输入验证码");else{var e=this.$queue.getData("userId"),t=this.phone,i=(this.password,this.code);t?(this.logining=!0,this.$queue.showLoading("加载中..."),this.$Request.getT("/user/changePhone",{userId:e,phone:t,msg:i}).then((function(e){uni.hideLoading(),0===e.status?uni.navigateTo({url:"/pages/my/userstatus"}):(n.meShowModel=!0,n.meTitle="绑定手机号失败",n.meContent=e.msg,n.meIndex="m1")}))):this.$queue.showToast("请输入手机号")}}}};e.default=i}}]);