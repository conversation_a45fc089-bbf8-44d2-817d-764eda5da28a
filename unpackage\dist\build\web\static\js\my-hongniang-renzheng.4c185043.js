(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["my-hongniang-renzheng"],{"0cfc":function(t,e,i){var n=i("71a8");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("0dd2f148",n,!0,{sourceMap:!1,shadowMode:!1})},1649:function(t,e,i){"use strict";i("6a54");var n=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(i("9b1b"));i("5c47"),i("0506");var o=n(i("b741")),r={data:function(){return{background:{backgroundImage:"linear-gradient(90deg,  #FE6387 0%, #F95FA6 100%)"},value:"",fileList:[],action:"https://www.example.com/upload",checked:!1,formData:{realName:"",idNumber:"",idCardFront:"",idCardVerso:"",authType:"",takeProvince:"",matchPhone:"",wxImg:"",wxCode:"",isSubmit:0},show1:!1,params:{city:!1,province:!0,area:!1}}},onLoad:function(t){console.log(this.$refs),console.log(this.$refs.city),this.getHnInfo()},methods:{goXieyi:function(){uni.navigateTo({url:"/my/setting/xieyi2"})},showCitySelect:function(){console.log("111"),this.show1=!0},confirmProvince:function(t){this.formData.takeProvince=t.province.label},uploadImg:function(t){var e=this;uni.chooseImage({count:1,sourceType:["album","camera"],success:function(i){for(var n=0;n<i.tempFilePaths.length;n++)e.$queue.showLoading("上传中..."),uni.uploadFile({url:e.config("APIHOST1")+"/alioss/upload",filePath:i.tempFilePaths[n],name:"file",success:function(i){e.formData[t]=JSON.parse(i.data).data,uni.hideLoading()}})}})},checkIdCard:function(t){return!1!==/^[1-9]\d{5}(19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[Xx\d]$/.test(t)},submit:function(){if(0!=this.checked)if(this.formData.realName)if(this.formData.idNumber)if(this.checkIdCard(this.formData.idNumber))if(this.formData.takeProvince)if(this.formData.matchPhone)if(this.formData.wxCode){/^[a-zA-Z][a-zA-Z0-9_-]{5,19}$/.test(this.formData.wxCode)?this.formData.wxImg?this.formData.idCardFront?this.formData.idCardVerso?this.$Request.postJson("/app/userCertification/saveUserCertification",(0,a.default)((0,a.default)({},this.formData),{},{authType:"2"})).then((function(t){console.log(t),0==t.code?(uni.showToast({title:"提交成功",icon:"success",duration:2e3}),setTimeout((function(){uni.navigateBack({delta:1})}),2e3)):uni.showToast({title:t.msg,icon:"none",mask:!0})})):uni.showToast({title:"请上传身份证反面",icon:"none"}):uni.showToast({title:"请上传身份证正面",icon:"none"}):uni.showToast({title:"请上传微信二维码",icon:"none"}):uni.showToast({icon:"none",title:"微信号不正确"})}else uni.showToast({title:"请输入微信号",icon:"none"});else uni.showToast({title:"请输入手机号",icon:"none"});else uni.showToast({title:"请选择接管区域",icon:"none"});else uni.showToast({title:"请输入正确身份证号码",icon:"none"});else uni.showToast({title:"请输入身份证号码",icon:"none"});else uni.showToast({title:"请输入真实姓名",icon:"none"});else uni.showToast({title:"请先同意《协议说明》",icon:"none"})},getHnInfo:function(){var t=this;this.$Request.get("//app/userCertification/getMyUserCertification",{authType:2}).then((function(e){console.log(e),0===e.code&&e.data&&(t.formData=e.data)}))},config:function(t){var e=null;if(t){var i=t.split(".");if(e=i.length>1?o.default[i[0]][i[1]]||null:o.default[t]||null,null==e){var n=cache.get("web_config");n&&(e=i.length>1?n[i[0]][i[1]]||null:n[t]||null)}}return e}}};e.default=r},"1c2a":function(t,e,i){"use strict";i.r(e);var n=i("f145"),a=i("b5a5");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("f512");var r=i("828b"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"21019ee6",null,!1,n["a"],void 0);e["default"]=s.exports},2633:function(t,e,i){"use strict";i.r(e);var n=i("83f7"),a=i("f1a1");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("ce40");var r=i("828b"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"326f4b11",null,!1,n["a"],void 0);e["default"]=s.exports},2838:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return n}));var n={uIcon:i("3688").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{},[i("v-uni-view",{staticClass:"u-navbar",class:{"u-navbar-fixed":t.isFixed,"u-border-bottom":t.borderBottom},style:[t.navbarStyle]},[i("v-uni-view",{staticClass:"u-status-bar",style:{height:t.statusBarHeight+"px"}}),i("v-uni-view",{staticClass:"u-navbar-inner",style:[t.navbarInnerStyle]},[t.isBack?i("v-uni-view",{staticClass:"u-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"u-icon-wrap"},[i("u-icon",{attrs:{name:t.backIconName,color:t.backIconColor,size:t.backIconSize}})],1),t.backText?i("v-uni-view",{staticClass:"u-icon-wrap u-back-text u-line-1",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()],1):t._e(),t.title?i("v-uni-view",{staticClass:"u-navbar-content-title",style:[t.titleStyle]},[i("v-uni-view",{staticClass:"u-title u-line-1",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),i("v-uni-view",{staticClass:"u-slot-content"},[t._t("default")],2),i("v-uni-view",{staticClass:"u-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?i("v-uni-view",{staticClass:"u-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+t.statusBarHeight+"px"}}):t._e()],1)},o=[]},"320c":function(t,e,i){"use strict";i.r(e);var n=i("c4d4"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},"35fb":function(t,e,i){"use strict";i.r(e);var n=i("8934"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},"3bd6":function(t,e,i){var n=i("5608");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("4a4ab346",n,!0,{sourceMap:!1,shadowMode:!1})},"4cfb":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa"),i("aa9c"),i("fd3c");var n={name:"u-checkbox",props:{name:{type:[String,Number],default:""},shape:{type:String,default:""},value:{type:Boolean,default:!1},disabled:{type:[String,Boolean],default:""},labelDisabled:{type:[String,Boolean],default:""},activeColor:{type:String,default:""},iconSize:{type:[String,Number],default:""},labelSize:{type:[String,Number],default:""},size:{type:[String,Number],default:""}},data:function(){return{parentDisabled:!1,newParams:{}}},created:function(){this.parent=this.$u.$parent.call(this,"u-checkbox-group"),this.parent&&this.parent.children.push(this)},computed:{isDisabled:function(){return""!==this.disabled?this.disabled:!!this.parent&&this.parent.disabled},isLabelDisabled:function(){return""!==this.labelDisabled?this.labelDisabled:!!this.parent&&this.parent.labelDisabled},checkboxSize:function(){return this.size?this.size:this.parent?this.parent.size:34},checkboxIconSize:function(){return this.iconSize?this.iconSize:this.parent?this.parent.iconSize:20},elActiveColor:function(){return this.activeColor?this.activeColor:this.parent?this.parent.activeColor:"primary"},elShape:function(){return this.shape?this.shape:this.parent?this.parent.shape:"square"},iconStyle:function(){var t={};return this.elActiveColor&&this.value&&!this.isDisabled&&(t.borderColor=this.elActiveColor,t.backgroundColor=this.elActiveColor),t.width=this.$u.addUnit(this.checkboxSize),t.height=this.$u.addUnit(this.checkboxSize),t},iconColor:function(){return this.value?"#ffffff":"transparent"},iconClass:function(){var t=[];return t.push("u-checkbox__icon-wrap--"+this.elShape),1==this.value&&t.push("u-checkbox__icon-wrap--checked"),this.isDisabled&&t.push("u-checkbox__icon-wrap--disabled"),this.value&&this.isDisabled&&t.push("u-checkbox__icon-wrap--disabled--checked"),t.join(" ")},checkboxStyle:function(){var t={};return this.parent&&this.parent.width&&(t.width=this.parent.width,t.flex="0 0 ".concat(this.parent.width)),this.parent&&this.parent.wrap&&(t.width="100%",t.flex="0 0 100%"),t}},methods:{onClickLabel:function(){this.isLabelDisabled||this.isDisabled||this.setValue()},toggle:function(){this.isDisabled||this.setValue()},emitEvent:function(){var t=this;this.$emit("change",{value:!this.value,name:this.name}),setTimeout((function(){t.parent&&t.parent.emitEvent&&t.parent.emitEvent()}),80)},setValue:function(){var t=0;if(this.parent&&this.parent.children&&this.parent.children.map((function(e){e.value&&t++})),1==this.value)this.emitEvent(),this.$emit("input",!this.value);else{if(this.parent&&t>=this.parent.max)return this.$u.toast("最多可选".concat(this.parent.max,"项"));this.emitEvent(),this.$emit("input",!this.value)}}}};e.default=n},5608:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,"uni-page-body[data-v-21019ee6]{background:#f2f2f2}body.?%PAGE?%[data-v-21019ee6]{background:#f2f2f2}.bgs[data-v-21019ee6]{width:100%;height:%?346?%;position:fixed;left:0;right:0;z-index:9}.cont[data-v-21019ee6]{position:relative;z-index:99;margin-top:%?200?%}.cont .box[data-v-21019ee6]{margin:%?20?% %?32?%;padding:%?30?%;background:#fff;border-radius:%?24?%}.cont .box .name[data-v-21019ee6]{display:flex;align-items:center;font-size:%?32?%;font-family:PingFang SC;font-weight:700;color:transparent 0}.cont .box .name uni-text[data-v-21019ee6]{color:#ff252b}.box2[data-v-21019ee6]{display:flex;align-items:center;width:100%;flex-direction:column;padding-top:%?10?%}.slot-btn[data-v-21019ee6]{margin-top:%?30?%}.slot-btn uni-image[data-v-21019ee6]{background-color:#fff;position:relative;width:%?530?%;height:%?300?%}.xy[data-v-21019ee6]{margin:%?65?% 0 %?28?% %?84?%;font-family:PingFang SC;font-weight:500;font-size:%?24?%;color:#666}.xy .tit[data-v-21019ee6]{color:#ff6684}.submit-btn[data-v-21019ee6]{width:%?613?%;height:%?86?%;background:linear-gradient(107deg,#ff6f9c 36%,#ff8eb6);border-radius:%?43?%;font-family:PingFang SC;font-weight:700;font-size:%?28?%;color:#fff;line-height:%?32?%;display:flex;align-items:center;justify-content:center;margin:0 auto %?35?%}.huibox[data-v-21019ee6]{width:%?202?%;height:%?202?%;background:#f0f1f5;border-radius:%?16?%;display:flex;align-items:center;justify-content:center;position:relative;z-index:9999}",""]),t.exports=e},"71a8":function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.u-checkbox[data-v-326f4b11]{display:inline-flex;align-items:center;overflow:hidden;-webkit-user-select:none;user-select:none;line-height:1.8}.u-checkbox__icon-wrap[data-v-326f4b11]{color:#606266;flex:none;display:-webkit-flex;display:flex;flex-direction:row;align-items:center;justify-content:center;box-sizing:border-box;width:%?42?%;height:%?42?%;color:transparent;text-align:center;transition-property:color,border-color,background-color;font-size:20px;border:1px solid #c8c9cc;transition-duration:.2s}.u-checkbox__icon-wrap--circle[data-v-326f4b11]{border-radius:100%}.u-checkbox__icon-wrap--square[data-v-326f4b11]{border-radius:%?6?%}.u-checkbox__icon-wrap--checked[data-v-326f4b11]{color:#fff;background-color:#2979ff;border-color:#2979ff}.u-checkbox__icon-wrap--disabled[data-v-326f4b11]{background-color:#ebedf0;border-color:#c8c9cc}.u-checkbox__icon-wrap--disabled--checked[data-v-326f4b11]{color:#c8c9cc!important}.u-checkbox__label[data-v-326f4b11]{word-wrap:break-word;margin-left:%?10?%;margin-right:%?24?%;color:#606266;font-size:%?30?%}.u-checkbox__label--disabled[data-v-326f4b11]{color:#c8c9cc}',""]),t.exports=e},8097:function(t,e,i){t.exports=i.p+"my/static/hnbg.png"},"83f7":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return n}));var n={uIcon:i("3688").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"u-checkbox",style:[t.checkboxStyle]},[i("v-uni-view",{staticClass:"u-checkbox__icon-wrap",class:[t.iconClass],style:[t.iconStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toggle.apply(void 0,arguments)}}},[i("u-icon",{staticClass:"u-checkbox__icon-wrap__icon",attrs:{name:"checkbox-mark",size:t.checkboxIconSize,color:t.iconColor}})],1),i("v-uni-view",{staticClass:"u-checkbox__label",style:{fontSize:t.$u.addUnit(t.labelSize)},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClickLabel.apply(void 0,arguments)}}},[t._t("default")],2)],1)},o=[]},8934:function(t,e,i){"use strict";i("6a54");var n=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa"),i("0c26");var a=n(i("b7d0")),o={name:"u-input",mixins:[a.default],props:{value:{type:[String,Number],default:""},type:{type:String,default:"text"},inputAlign:{type:String,default:"left"},placeholder:{type:String,default:"请输入内容"},disabled:{type:Boolean,default:!1},maxlength:{type:[Number,String],default:140},placeholderStyle:{type:String,default:"color: #c0c4cc;"},confirmType:{type:String,default:"done"},customStyle:{type:Object,default:function(){return{}}},fixed:{type:Boolean,default:!1},focus:{type:Boolean,default:!1},passwordIcon:{type:Boolean,default:!0},border:{type:Boolean,default:!1},borderColor:{type:String,default:"#dcdfe6"},autoHeight:{type:Boolean,default:!0},selectOpen:{type:Boolean,default:!1},height:{type:[Number,String],default:""},clearable:{type:Boolean,default:!0},cursorSpacing:{type:[Number,String],default:0},selectionStart:{type:[Number,String],default:-1},selectionEnd:{type:[Number,String],default:-1},trim:{type:Boolean,default:!0},showConfirmbar:{type:Boolean,default:!0}},data:function(){return{defaultValue:this.value,inputHeight:70,textareaHeight:100,validateState:!1,focused:!1,showPassword:!1,lastValue:""}},watch:{value:function(t,e){this.defaultValue=t,t!=e&&"select"==this.type&&this.handleInput({detail:{value:t}})}},computed:{inputMaxlength:function(){return Number(this.maxlength)},getStyle:function(){var t={};return t.minHeight=this.height?this.height+"rpx":"textarea"==this.type?this.textareaHeight+"rpx":this.inputHeight+"rpx",t=Object.assign(t,this.customStyle),this.disabled&&(t.pointerEvents="none"),t},getCursorSpacing:function(){return Number(this.cursorSpacing)},uSelectionStart:function(){return String(this.selectionStart)},uSelectionEnd:function(){return String(this.selectionEnd)}},created:function(){this.$on("on-form-item-error",this.onFormItemError)},methods:{handleInput:function(t){var e=this,i=t.detail.value;this.trim&&(i=this.$u.trim(i)),this.$emit("input",i),this.defaultValue=i,setTimeout((function(){e.dispatch("u-form-item","on-form-change",i)}),40)},handleBlur:function(t){var e=this;setTimeout((function(){e.focused=!1}),100),this.$emit("blur",t.detail.value),setTimeout((function(){e.dispatch("u-form-item","on-form-blur",t.detail.value)}),40)},onFormItemError:function(t){this.validateState=t},onFocus:function(t){this.focused=!0,this.$emit("focus")},onConfirm:function(t){this.$emit("confirm",t.detail.value)},onClear:function(t){this.$emit("input","")},inputClick:function(){this.$emit("click")}}};e.default=o},a34e:function(t,e,i){var n=i("edc5");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("b709bde6",n,!0,{sourceMap:!1,shadowMode:!1})},a667:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return n}));var n={uIcon:i("3688").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"u-input",class:{"u-input--border":t.border,"u-input--error":t.validateState},style:{padding:"0 "+(t.border?20:0)+"rpx",borderColor:t.borderColor,textAlign:t.inputAlign},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.inputClick.apply(void 0,arguments)}}},["textarea"==t.type?i("v-uni-textarea",{staticClass:"u-input__input u-input__textarea",style:[t.getStyle],attrs:{value:t.defaultValue,placeholder:t.placeholder,placeholderStyle:t.placeholderStyle,disabled:t.disabled,maxlength:t.inputMaxlength,fixed:t.fixed,focus:t.focus,autoHeight:t.autoHeight,"selection-end":t.uSelectionEnd,"selection-start":t.uSelectionStart,"cursor-spacing":t.getCursorSpacing,"show-confirm-bar":t.showConfirmbar},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.handleInput.apply(void 0,arguments)},blur:function(e){arguments[0]=e=t.$handleEvent(e),t.handleBlur.apply(void 0,arguments)},focus:function(e){arguments[0]=e=t.$handleEvent(e),t.onFocus.apply(void 0,arguments)},confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.onConfirm.apply(void 0,arguments)}}}):i("v-uni-input",{staticClass:"u-input__input",style:[t.getStyle],attrs:{type:"password"==t.type?"text":t.type,value:t.defaultValue,password:"password"==t.type&&!t.showPassword,placeholder:t.placeholder,placeholderStyle:t.placeholderStyle,disabled:t.disabled||"select"===t.type,maxlength:t.inputMaxlength,focus:t.focus,confirmType:t.confirmType,"cursor-spacing":t.getCursorSpacing,"selection-end":t.uSelectionEnd,"selection-start":t.uSelectionStart,"show-confirm-bar":t.showConfirmbar},on:{focus:function(e){arguments[0]=e=t.$handleEvent(e),t.onFocus.apply(void 0,arguments)},blur:function(e){arguments[0]=e=t.$handleEvent(e),t.handleBlur.apply(void 0,arguments)},input:function(e){arguments[0]=e=t.$handleEvent(e),t.handleInput.apply(void 0,arguments)},confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.onConfirm.apply(void 0,arguments)}}}),i("v-uni-view",{staticClass:"u-input__right-icon u-flex"},[t.clearable&&""!=t.value&&t.focused?i("v-uni-view",{staticClass:"u-input__right-icon__clear u-input__right-icon__item",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClear.apply(void 0,arguments)}}},[i("u-icon",{attrs:{size:"32",name:"close-circle-fill",color:"#c0c4cc"}})],1):t._e(),t.passwordIcon&&"password"==t.type?i("v-uni-view",{staticClass:"u-input__right-icon__clear u-input__right-icon__item"},[i("u-icon",{attrs:{size:"32",name:t.showPassword?"eye-fill":"eye",color:"#c0c4cc"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showPassword=!t.showPassword}}})],1):t._e(),"select"==t.type?i("v-uni-view",{staticClass:"u-input__right-icon--select u-input__right-icon__item",class:{"u-input__right-icon--select--reverse":t.selectOpen}},[i("u-icon",{attrs:{name:"arrow-down-fill",size:"26",color:"#c0c4cc"}})],1):t._e()],1)],1)},o=[]},b5a5:function(t,e,i){"use strict";i.r(e);var n=i("1649"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},b7d0:function(t,e,i){"use strict";function n(t,e,i){this.$children.map((function(a){t===a.$options.name?a.$emit.apply(a,[e].concat(i)):n.apply(a,[t,e].concat(i))}))}i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("fd3c"),i("c223");var a={methods:{dispatch:function(t,e,i){var n=this.$parent||this.$root,a=n.$options.name;while(n&&(!a||a!==t))n=n.$parent,n&&(a=n.$options.name);n&&n.$emit.apply(n,[e].concat(i))},broadcast:function(t,e,i){n.call(this,t,e,i)}}};e.default=a},c4d4:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa");var n=uni.getSystemInfoSync(),a={},o={name:"u-navbar",props:{height:{type:[String,Number],default:""},backIconColor:{type:String,default:"#606266"},backIconName:{type:String,default:"nav-back"},backIconSize:{type:[String,Number],default:"44"},backText:{type:String,default:""},backTextStyle:{type:Object,default:function(){return{color:"#606266"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleColor:{type:String,default:"#606266"},titleBold:{type:Boolean,default:!1},titleSize:{type:[String,Number],default:32},isBack:{type:[Boolean,String],default:!0},background:{type:Object,default:function(){return{background:"#ffffff"}}},isFixed:{type:Boolean,default:!0},immersive:{type:Boolean,default:!1},borderBottom:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},customBack:{type:Function,default:null}},data:function(){return{menuButtonInfo:a,statusBarHeight:n.statusBarHeight}},computed:{navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),t},titleStyle:function(){var t={};return t.left=(n.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(n.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44}},created:function(){},methods:{goBack:function(){"function"===typeof this.customBack?this.customBack.bind(this.$u.$parent.call(this))():uni.navigateBack()}}};e.default=o},caf2:function(t,e,i){"use strict";var n=i("f683"),a=i.n(n);a.a},cbe8:function(t,e,i){"use strict";var n=i("a34e"),a=i.n(n);a.a},cd60:function(t,e,i){"use strict";i.r(e);var n=i("a667"),a=i("35fb");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("caf2");var r=i("828b"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"24c9efac",null,!1,n["a"],void 0);e["default"]=s.exports},ce40:function(t,e,i){"use strict";var n=i("0cfc"),a=i.n(n);a.a},ddec:function(t,e,i){"use strict";i.r(e);var n=i("2838"),a=i("320c");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("cbe8");var r=i("828b"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"1d7f90d0",null,!1,n["a"],void 0);e["default"]=s.exports},edc5:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.u-navbar[data-v-1d7f90d0]{width:100%}.u-navbar-fixed[data-v-1d7f90d0]{position:fixed;left:0;right:0;top:0;z-index:991}.u-status-bar[data-v-1d7f90d0]{width:100%}.u-navbar-inner[data-v-1d7f90d0]{display:flex;flex-direction:row;justify-content:space-between;position:relative;align-items:center}.u-back-wrap[data-v-1d7f90d0]{display:flex;flex-direction:row;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.u-back-text[data-v-1d7f90d0]{padding-left:%?4?%;font-size:%?30?%}.u-navbar-content-title[data-v-1d7f90d0]{display:flex;flex-direction:row;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0}.u-navbar-centent-slot[data-v-1d7f90d0]{flex:1}.u-title[data-v-1d7f90d0]{line-height:%?60?%;font-size:%?32?%;flex:1}.u-navbar-right[data-v-1d7f90d0]{flex:1;display:flex;flex-direction:row;align-items:center;justify-content:flex-end}.u-slot-content[data-v-1d7f90d0]{flex:1;display:flex;flex-direction:row;align-items:center}',""]),t.exports=e},f145:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return n}));var n={uNavbar:i("ddec").default,uInput:i("cd60").default,uIcon:i("3688").default,uPicker:i("df41").default,uCheckbox:i("2633").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",[n("u-navbar",{attrs:{title:"申请成为红娘","is-back":!0,background:t.background,"title-color":"#FFFFFF","border-bottom":!1,"back-icon-color":"#FFFFFF"}}),n("v-uni-view",{staticClass:"bgs"},[n("v-uni-image",{staticStyle:{width:"100%",height:"249rpx"},attrs:{src:i("8097")}})],1),n("v-uni-view",{staticClass:"cont"},[n("v-uni-view",{staticClass:"box"},[2==t.formData.status?n("v-uni-view",{staticClass:"name margin-bottom",staticStyle:{"justify-content":"space-between"}},[n("v-uni-view",[t._v("拒绝理由:")]),n("v-uni-view",{staticClass:"status"},[t._v(t._s(t.formData.remark))])],1):t._e(),n("v-uni-view",{staticClass:"name"},[n("v-uni-text",[t._v("*")]),n("v-uni-view",[t._v("真实姓名")])],1),n("v-uni-view",{staticClass:"margin-top-sm"},[n("u-input",{attrs:{type:"text",border:!0,placeholder:"请输入联系姓名"},model:{value:t.formData.realName,callback:function(e){t.$set(t.formData,"realName",e)},expression:"formData.realName"}})],1),n("v-uni-view",{staticClass:"name margin-top"},[n("v-uni-text",[t._v("*")]),n("v-uni-view",[t._v("身份证号")])],1),n("v-uni-view",{staticClass:"margin-top-sm"},[n("u-input",{attrs:{type:"idcard",border:!0,placeholder:"请输入身份证号",maxlength:"18"},model:{value:t.formData.idNumber,callback:function(e){t.$set(t.formData,"idNumber",e)},expression:"formData.idNumber"}})],1),n("v-uni-view",{staticClass:"name margin-top"},[n("v-uni-text",[t._v("*")]),n("v-uni-view",[t._v("手机号码")])],1),n("v-uni-view",{staticClass:"margin-top-sm"},[n("u-input",{attrs:{type:"tel",border:!0,placeholder:"请输入手机号码",maxlength:"11"},model:{value:t.formData.matchPhone,callback:function(e){t.$set(t.formData,"matchPhone",e)},expression:"formData.matchPhone"}})],1),n("v-uni-view",{staticClass:"name margin-top",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showCitySelect.apply(void 0,arguments)}}},[n("v-uni-text",[t._v("*")]),n("v-uni-view",[t._v("接管区域")])],1),n("v-uni-view",{staticClass:"margin-top-sm",staticStyle:{position:"relative"}},[n("u-input",{attrs:{disabled:!0,border:!0,placeholder:"请选择接管区域"},model:{value:t.formData.takeProvince,callback:function(e){t.$set(t.formData,"takeProvince",e)},expression:"formData.takeProvince"}}),n("v-uni-view",{staticStyle:{position:"absolute",top:"0px",left:"0px",width:"100%",height:"100%","z-index":"9"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showCitySelect.apply(void 0,arguments)}}})],1),n("v-uni-view",{staticClass:"name margin-top",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showCitySelect.apply(void 0,arguments)}}},[n("v-uni-text",[t._v("*")]),n("v-uni-view",[t._v("微信号")])],1),n("v-uni-view",{staticClass:"margin-top-sm"},[n("u-input",{attrs:{border:!0,placeholder:"请输入微信号"},model:{value:t.formData.wxCode,callback:function(e){t.$set(t.formData,"wxCode",e)},expression:"formData.wxCode"}})],1),n("v-uni-view",{staticClass:"name margin-top",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showCitySelect.apply(void 0,arguments)}}},[n("v-uni-text",[t._v("*")]),n("v-uni-view",[t._v("微信二维码")])],1),n("v-uni-view",{staticClass:"margin-top-sm"},[t.formData.wxImg?n("v-uni-image",{staticClass:"huibox",attrs:{src:t.formData.wxImg,mode:"scaleToFill"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.uploadImg("wxImg")}}}):n("v-uni-view",{staticClass:"huibox",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.uploadImg("wxImg")}}},[n("u-icon",{attrs:{name:"plus",color:"#CCCCCC",size:"60"}})],1)],1),n("u-picker",{ref:"city",attrs:{mode:"region",params:t.params},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.confirmProvince.apply(void 0,arguments)}},model:{value:t.show1,callback:function(e){t.show1=e},expression:"show1"}})],1),n("v-uni-view",{staticClass:"box "},[n("v-uni-view",{staticClass:"name"},[n("v-uni-text",[t._v("*")]),n("v-uni-view",[t._v("上传身份证")])],1),n("v-uni-view",{staticClass:"box2"},[n("v-uni-view",{staticClass:"slot-btn",attrs:{"hover-class":"slot-btn__hover","hover-stay-time":"150"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.uploadImg("idCardFront")}}},[n("v-uni-image",{attrs:{src:t.formData.idCardFront||"../static/upimg1.png",mode:"scaleToFill"}})],1),n("v-uni-view",{staticClass:"slot-btn",attrs:{"hover-class":"slot-btn__hover","hover-stay-time":"150"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.uploadImg("idCardVerso")}}},[n("v-uni-image",{attrs:{src:t.formData.idCardVerso||"../static/upimg2.png",mode:"scaleToFill"}})],1)],1)],1),n("v-uni-view",{staticClass:"xy row"},[n("u-checkbox",{attrs:{"label-size":"26rpx",shape:"circle","active-color":"#FF6684"},model:{value:t.checked,callback:function(e){t.checked=e},expression:"checked"}},[t._v("我已阅读并同意"),n("v-uni-text",{staticClass:"tit",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goXieyi.apply(void 0,arguments)}}},[t._v("《协议说明》")])],1)],1),0==t.formData.isSubmit?n("v-uni-view",{staticClass:"submit-btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.submit.apply(void 0,arguments)}}},[t._v("提交审核")]):t._e()],1)],1)},o=[]},f1a1:function(t,e,i){"use strict";i.r(e);var n=i("4cfb"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},f512:function(t,e,i){"use strict";var n=i("3bd6"),a=i.n(n);a.a},f683:function(t,e,i){var n=i("fdde");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("2690e81b",n,!0,{sourceMap:!1,shadowMode:!1})},fdde:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.u-input[data-v-24c9efac]{position:relative;flex:1;display:flex;flex-direction:row}.u-input__input[data-v-24c9efac]{font-size:%?28?%;color:#000;flex:1}.u-input__textarea[data-v-24c9efac]{width:auto;font-size:%?28?%;color:#303133;padding:%?15?%;line-height:normal;flex:1;border-radius:%?6?%}.u-input--border[data-v-24c9efac]{border-radius:%?6?%;border-radius:4px;border:1px solid #dcdfe6}.u-input--error[data-v-24c9efac]{border-color:#fa3534!important}.u-input__right-icon__item[data-v-24c9efac]{margin-left:%?10?%}.u-input__right-icon--select[data-v-24c9efac]{transition:-webkit-transform .4s;transition:transform .4s;transition:transform .4s,-webkit-transform .4s}.u-input__right-icon--select--reverse[data-v-24c9efac]{-webkit-transform:rotate(-180deg);transform:rotate(-180deg)}',""]),t.exports=e}}]);