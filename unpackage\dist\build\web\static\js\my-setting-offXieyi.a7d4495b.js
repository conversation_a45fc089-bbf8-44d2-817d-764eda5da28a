(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["my-setting-offXieyi"],{"0e4d":function(t,n,e){var i=e("e716");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=e("967d").default;a("50b4686a",i,!0,{sourceMap:!1,shadowMode:!1})},2238:function(t,n,e){"use strict";var i=e("0e4d"),a=e.n(i);a.a},5618:function(t,n,e){"use strict";e.r(n);var i=e("86b7"),a=e.n(i);for(var u in i)["default"].indexOf(u)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(u);n["default"]=a.a},"86b7":function(t,n,e){"use strict";e("6a54"),Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;n.default={data:function(){return{content:""}},onLoad:function(){this.getGuize()},methods:{getGuize:function(){var t=this;this.$Request.getT("/app/common/type/293").then((function(n){0==n.code&&(t.content=n.data.value)}))}}}},9854:function(t,n,e){"use strict";e.r(n);var i=e("d34f"),a=e("5618");for(var u in a)["default"].indexOf(u)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(u);e("2238");var o=e("828b"),c=Object(o["a"])(a["default"],i["b"],i["c"],!1,null,"2537788a",null,!1,i["a"],void 0);n["default"]=c.exports},d34f:function(t,n,e){"use strict";e.d(n,"b",(function(){return i})),e.d(n,"c",(function(){return a})),e.d(n,"a",(function(){}));var i=function(){var t=this.$createElement,n=this._self._c||t;return n("v-uni-view",{staticClass:"home1",staticStyle:{"line-height":"26px",padding:"32upx"}},[n("v-uni-view",{staticStyle:{"font-size":"28upx"},domProps:{innerHTML:this._s(this.content)}})],1)},a=[]},e716:function(t,n,e){var i=e("c86c");n=i(!1),n.push([t.i,"uni-page-body[data-v-2537788a]{background:#fff}body.?%PAGE?%[data-v-2537788a]{background:#fff}",""]),t.exports=n}}]);