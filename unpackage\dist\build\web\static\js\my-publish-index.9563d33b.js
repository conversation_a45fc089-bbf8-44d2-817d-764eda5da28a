(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["my-publish-index"],{"045b":function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */\r\n/*\r\nsticky生效条件：\r\n1、父元素不能overflow:hidden或者overflow:auto属性。(mescroll-body设置:sticky="true"即可, mescroll-uni本身没有设置overflow)\r\n2、必须指定top、bottom、left、right4个值之一，否则只会处于相对定位\r\n3、父元素的高度不能低于sticky元素的高度\r\n4、sticky元素仅在其父元素内生效,所以父元素必须是 mescroll\r\n*/.sticky-tabs[data-v-ac17e15a]{z-index:990;position:-webkit-sticky;position:sticky;top:var(--window-top)}.mescroll-uni .sticky-tabs[data-v-ac17e15a],[data-v-ac17e15a] .mescroll-uni .sticky-tabs{top:0}.demo-tip[data-v-ac17e15a]{padding:%?18?%;font-size:%?24?%;text-align:center}',""]),t.exports=e},2183:function(t,e,n){var i=n("045b");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var s=n("967d").default;s("13bb03e6",i,!0,{sourceMap:!1,shadowMode:!1})},"2d4a":function(t,e,n){"use strict";n.d(e,"b",(function(){return s})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){return i}));var i={uButton:n("ddc5").default,uIcon:n("3688").default,uModal:n("7e01").default},s=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticStyle:{"padding-bottom":"80rpx"}},[n("v-uni-view",{staticClass:"sticky-tabs"},[n("me-tabs",{attrs:{nameKey:"title",tabs:t.tabs},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.tabChange.apply(void 0,arguments)}},model:{value:t.tabIndex,callback:function(e){t.tabIndex=e},expression:"tabIndex"}})],1),n("mescroll-body",{ref:"mescrollRef",attrs:{sticky:!0},on:{init:function(e){arguments[0]=e=t.$handleEvent(e),t.mescrollInit.apply(void 0,arguments)},down:function(e){arguments[0]=e=t.$handleEvent(e),t.downCallback.apply(void 0,arguments)},up:function(e){arguments[0]=e=t.$handleEvent(e),t.upCallback.apply(void 0,arguments)}}},[t._l(t.goods,(function(e,i){return n("v-uni-view",{key:i,staticClass:"margin-sm padding bg  ",staticStyle:{"border-radius":"24rpx"},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.goNav("/pages/index/game/myOrder?id="+e.id+"&userId="+t.userId)}}},[n("v-uni-view",{staticClass:"flex justify-between u-border-bottom padding-bottom"},[n("v-uni-view",{staticStyle:{color:"#AC75FE"}},[0==e.status?n("v-uni-view",[t._v("进行中")]):t._e(),1==e.status?n("v-uni-view",[t._v("待审核")]):t._e(),2==e.status?n("v-uni-view",[t._v("已下架")]):t._e(),3==e.status?n("v-uni-view",[t._v("已拒绝")]):t._e()],1),n("v-uni-view",{staticStyle:{color:"#999999"}},[t._v(t._s(e.updateTime))])],1),n("v-uni-view",{staticClass:" u-flex u-p-t-30"},[n("v-uni-view",{staticClass:"u-flex-1 text-white margin-left-xs"},[n("v-uni-view",{staticClass:"text-lg  text-bold flex justify-between"},[n("v-uni-view",[t._v(t._s(e.gameName))]),n("v-uni-view",{staticStyle:{color:"#FF6F1B"}},[t._v(t._s(e.oldMoney)+"币/"+t._s(e.unit))])],1),n("v-uni-view",{staticClass:"u-font-14 u-tips-color margin-top-xs"},[n("v-uni-view",{staticStyle:{color:"#999999"}},[t._v("接单时间："+t._s(e.orderTakingTime))])],1),e.content&&3==e.status?n("v-uni-view",{staticClass:"u-font-14 u-tips-color margin-top "},[n("v-uni-view",{staticClass:"text-red"},[t._v("拒绝理由："+t._s(e.content))])],1):t._e()],1)],1),n("v-uni-view",{staticClass:"flex justify-end u-p-t-20"},[1==e.status?n("u-button",{staticClass:"margin-right-sm",attrs:{"hover-class":"none","custom-style":t.customStyle,shape:"circle",plain:!0},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.updateStatus(e,2)}}},[t._v("取消审核")]):t._e(),0==e.status?n("u-button",{staticClass:"margin-right-sm",attrs:{"hover-class":"none","custom-style":t.customStyle,shape:"circle",plain:!0},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.updateStatus(e,2)}}},[t._v("立即下架")]):t._e(),2==e.status||3==e.status?n("u-button",{staticClass:"margin-right-sm",attrs:{"hover-class":"none","custom-style":t.customStyle,shape:"circle",plain:!0},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.delStatus(e)}}},[t._v("删除")]):t._e(),2==e.status?n("u-button",{staticClass:"margin-right-sm",attrs:{"hover-class":"none","custom-style":t.customStyle,shape:"circle",plain:!0},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.updateStatus(e,1)}}},[t._v("重新上架")]):t._e(),0!=e.status?n("u-button",{attrs:{"hover-class":"none","custom-style":t.customStyle,shape:"circle",plain:!0},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.update(e.id)}}},[t._v("重新编辑")]):t._e()],1)],1)})),0==t.goods.length?n("empty"):t._e()],2),n("v-uni-view",{staticClass:"padding-lr-sm",staticStyle:{position:"fixed",bottom:"40rpx",width:"100%","z-index":"999"}},[n("u-button",{attrs:{"custom-style":t.customStyle1,"hair-line":!1},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goNav("/my/publish/editor")}}},[n("u-icon",{staticStyle:{"margin-right":"10upx"},attrs:{name:"plus",color:"#fff",size:"30"}}),t._v("发布")],1)],1),n("u-modal",{attrs:{content:t.meContent,title:t.meTitle,"show-cancel-button":t.meShowCancel,"confirm-text":t.meConfirmText,"cancel-text":t.meCancelText},on:{cancel:function(e){arguments[0]=e=t.$handleEvent(e),t.meHandleClose.apply(void 0,arguments)},confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.meHandleBtn.apply(void 0,arguments)}},model:{value:t.meShowModel,callback:function(e){t.meShowModel=e},expression:"meShowModel"}})],1)},a=[]},"7f33":function(t,e,n){"use strict";n.r(e);var i=n("f0c9"),s=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);e["default"]=s.a},ade2:function(t,e,n){"use strict";n.r(e);var i=n("2d4a"),s=n("7f33");for(var a in s)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return s[t]}))}(a);n("dd31");var o=n("828b"),l=Object(o["a"])(s["default"],i["b"],i["c"],!1,null,"ac17e15a",null,!1,i["a"],void 0);e["default"]=l.exports},dd31:function(t,e,n){"use strict";var i=n("2183"),s=n.n(i);s.a},f0c9:function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("c223");var s=i(n("b7c7")),a=i(n("3145")),o=i(n("7a77")),l=i(n("72e2")),u=i(n("2bdc")),r={mixins:[a.default],components:{mescrollBody:o.default,meTabs:l.default,empty:u.default},data:function(){return{meShowModel:!1,meShowCancel:!0,meTitle:"提示",meContent:"",meConfirmText:"确认",meCancelText:"取消",meIndex:"",infoId:"",goods:[],tabs:[{title:"全部",status:""},{title:"进行中",status:"0"},{title:"待审核",status:"1"},{title:"已下架",status:"2"},{title:"已拒绝",status:"3"}],tabIndex:0,page:1,limit:10,userId:0,status:1,nickName:"",avatar:"",customStyle:{color:"#AC75FE",backgroundColor:"#FFFFFF",border:"8rpx",width:"180rpx",height:"65rpx",margin:"0 0 0 20rpx"},customStyle1:{backgroundColor:"#AC75FE",border:0,color:"#FFF",height:"98rpx"},renzheng:0}},onLoad:function(){this.$queue.showLoading("加载中..."),this.userId=uni.getStorageSync("userId"),this.nickName=uni.getStorageSync("nickName"),this.getRenZheng()},onShow:function(){this.mescroll.resetUpScroll()},methods:{getRenZheng:function(){var t=this;this.$Request.get("/app/userCertification/queryInsert").then((function(e){0==e.code&&(null==e.data?t.renzheng=0:0==e.data.status?t.renzheng=1:1==e.data.status?t.renzheng=2:2==e.data.status&&(t.renzheng=3))}))},downCallback:function(){this.mescroll.resetUpScroll()},upCallback:function(t){var e=this,n=this.tabs[this.tabIndex].status,i={status:n,page:t.num,limit:t.size};this.$Request.get("/app/orderTaking/selectMyRelease",i).then((function(n){uni.hideLoading(),e.mescroll.endBySize(n.data.list.length,n.data.totalCount),1==t.num&&(e.goods=[]),e.goods=[].concat((0,s.default)(e.goods),(0,s.default)(n.data.list)),e.mescroll.endSuccess(n.data.list.length)})).catch((function(){e.mescroll.endErr()}))},tabChange:function(){this.goods=[],this.mescroll.resetUpScroll()},updateStatus:function(t,e){var n=this,i={id:t.id,status:e};this.$Request.get("/app/orderTaking/updateTakingStatus",i).then((function(t){0==t.code&&n.mescroll.resetUpScroll()}))},update:function(t){uni.navigateTo({url:"/my/publish/editor?id="+t})},meHandleBtn:function(){var t=this;if("m1"==this.meIndex){var e={id:this.infoId};t.$Request.get("/app/orderTaking/deleteTaking",e).then((function(e){uni.showToast({title:"删除成功"}),t.mescroll.resetUpScroll()}))}"m2"==t.meIndex&&uni.navigateTo({url:"/my/renzheng/index"})},meHandleClose:function(){this.meIndex},delStatus:function(t){this.meShowModel=!0,this.meTitle="提示",this.meContent="确定删除吗?",this.meIndex="m1",this.infoId=t.id},edit:function(t){uni.navigateTo({url:"/pages/order/release?type=hasEdit&id="+t.id+"&content="+t.content+"&site="+t.site+"&phone="+t.phone+"&deliveryTime="+t.deliveryTime+"&classifyId="+t.classifyId+"&classifyName="+t.classifyName+"&userType="+t.userType+"&commission="+t.commission+"&image="+t.image+"&address="+t.address+"&latitude="+t.latitude+"&longitude="+t.longitude})},clickItem:function(t){console.log("点击",t),uni.navigateTo({url:"/pages/index/game/order?id="+t.orderTakingId+"&userId="+t.userId})},goNav:function(t){if(2!=this.renzheng)return this.meShowModel=!0,this.meTitle="提示",this.meContent="确定删除吗？",void(this.meIndex="m2");uni.navigateTo({url:t})}}};e.default=r}}]);