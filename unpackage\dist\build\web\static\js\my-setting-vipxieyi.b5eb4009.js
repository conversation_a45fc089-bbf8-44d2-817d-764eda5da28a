(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["my-setting-vipxieyi"],{"31bb":function(t,n,e){"use strict";e("6a54"),Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;n.default={data:function(){return{content:""}},onLoad:function(){this.getGuize()},methods:{getGuize:function(){var t=this;this.$Request.getT("/app/common/type/329").then((function(n){0===n.code&&(t.content=n.data.value)}))}}}},"97c9":function(t,n,e){var i=e("c86c");n=i(!1),n.push([t.i,"uni-page-body[data-v-060fb849]{background:#fff}body.?%PAGE?%[data-v-060fb849]{background:#fff}",""]),t.exports=n},a2b7:function(t,n,e){"use strict";e.r(n);var i=e("d703"),a=e("ec02");for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(o);e("add0");var u=e("828b"),c=Object(u["a"])(a["default"],i["b"],i["c"],!1,null,"060fb849",null,!1,i["a"],void 0);n["default"]=c.exports},add0:function(t,n,e){"use strict";var i=e("f391"),a=e.n(i);a.a},d703:function(t,n,e){"use strict";e.d(n,"b",(function(){return i})),e.d(n,"c",(function(){return a})),e.d(n,"a",(function(){}));var i=function(){var t=this.$createElement,n=this._self._c||t;return n("v-uni-view",{staticClass:"home1",staticStyle:{"line-height":"26px",padding:"32upx"}},[n("v-uni-view",{staticStyle:{color:"#000000","font-size":"28upx"},domProps:{innerHTML:this._s(this.content)}})],1)},a=[]},ec02:function(t,n,e){"use strict";e.r(n);var i=e("31bb"),a=e.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(o);n["default"]=a.a},f391:function(t,n,e){var i=e("97c9");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=e("967d").default;a("761a3b31",i,!0,{sourceMap:!1,shadowMode:!1})}}]);