(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-public-login"],{"02eb":function(e,t,n){"use strict";var i=n("5e7b"),a=n.n(i);a.a},"0cfc":function(e,t,n){var i=n("71a8");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var a=n("967d").default;a("0dd2f148",i,!0,{sourceMap:!1,shadowMode:!1})},"0eeb":function(e,t,n){e.exports=n.p+"static/logo.png"},"25a8":function(e,t,n){"use strict";n.r(t);var i=n("7940"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);t["default"]=a.a},2633:function(e,t,n){"use strict";n.r(t);var i=n("83f7"),a=n("f1a1");for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);n("ce40");var r=n("828b"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"326f4b11",null,!1,i["a"],void 0);t["default"]=s.exports},"327c":function(e,t){e.exports="data:image/png;base64,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"},"4cfb":function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("64aa"),n("aa9c"),n("fd3c");var i={name:"u-checkbox",props:{name:{type:[String,Number],default:""},shape:{type:String,default:""},value:{type:Boolean,default:!1},disabled:{type:[String,Boolean],default:""},labelDisabled:{type:[String,Boolean],default:""},activeColor:{type:String,default:""},iconSize:{type:[String,Number],default:""},labelSize:{type:[String,Number],default:""},size:{type:[String,Number],default:""}},data:function(){return{parentDisabled:!1,newParams:{}}},created:function(){this.parent=this.$u.$parent.call(this,"u-checkbox-group"),this.parent&&this.parent.children.push(this)},computed:{isDisabled:function(){return""!==this.disabled?this.disabled:!!this.parent&&this.parent.disabled},isLabelDisabled:function(){return""!==this.labelDisabled?this.labelDisabled:!!this.parent&&this.parent.labelDisabled},checkboxSize:function(){return this.size?this.size:this.parent?this.parent.size:34},checkboxIconSize:function(){return this.iconSize?this.iconSize:this.parent?this.parent.iconSize:20},elActiveColor:function(){return this.activeColor?this.activeColor:this.parent?this.parent.activeColor:"primary"},elShape:function(){return this.shape?this.shape:this.parent?this.parent.shape:"square"},iconStyle:function(){var e={};return this.elActiveColor&&this.value&&!this.isDisabled&&(e.borderColor=this.elActiveColor,e.backgroundColor=this.elActiveColor),e.width=this.$u.addUnit(this.checkboxSize),e.height=this.$u.addUnit(this.checkboxSize),e},iconColor:function(){return this.value?"#ffffff":"transparent"},iconClass:function(){var e=[];return e.push("u-checkbox__icon-wrap--"+this.elShape),1==this.value&&e.push("u-checkbox__icon-wrap--checked"),this.isDisabled&&e.push("u-checkbox__icon-wrap--disabled"),this.value&&this.isDisabled&&e.push("u-checkbox__icon-wrap--disabled--checked"),e.join(" ")},checkboxStyle:function(){var e={};return this.parent&&this.parent.width&&(e.width=this.parent.width,e.flex="0 0 ".concat(this.parent.width)),this.parent&&this.parent.wrap&&(e.width="100%",e.flex="0 0 100%"),e}},methods:{onClickLabel:function(){this.isLabelDisabled||this.isDisabled||this.setValue()},toggle:function(){this.isDisabled||this.setValue()},emitEvent:function(){var e=this;this.$emit("change",{value:!this.value,name:this.name}),setTimeout((function(){e.parent&&e.parent.emitEvent&&e.parent.emitEvent()}),80)},setValue:function(){var e=0;if(this.parent&&this.parent.children&&this.parent.children.map((function(t){t.value&&e++})),1==this.value)this.emitEvent(),this.$emit("input",!this.value);else{if(this.parent&&e>=this.parent.max)return this.$u.toast("最多可选".concat(this.parent.max,"项"));this.emitEvent(),this.$emit("input",!this.value)}}}};t.default=i},"5e7b":function(e,t,n){var i=n("6c45");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var a=n("967d").default;a("1731df2b",i,!0,{sourceMap:!1,shadowMode:!1})},"6c45":function(e,t,n){var i=n("c86c");t=i(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */uni-page-body[data-v-601f627b]{height:100%;background:#fff!important}body.?%PAGE?%[data-v-601f627b]{background:#fff!important}.container[data-v-601f627b]{top:0;padding-top:50px;position:relative;width:100%;height:100%;overflow:hidden;background:#fff!important;color:#fff}.confirm-btn-weixin[data-v-601f627b]{width:200px;height:42px;line-height:42px;border-radius:30px;margin-top:%?40?%;background:-moz-linear-gradient(left,#f15b6c,#e10a07 100%);background:-webkit-gradient(linear,left top,left right,color-stop(0,#f15b6c),color-stop(100%,#e10a07));background:-webkit-linear-gradient(left,#f15b6c,#e10a07);background:-o-linear-gradient(left,#f15b6c 0,#e10a07 100%);background:-ms-linear-gradient(left,#f15b6c 0,#e10a07 100%);background:linear-gradient(270deg,#f15b6c 0,#e10a07);color:#fff;font-size:%?32?%}.confirm-btn-weixin[data-v-601f627b]:after{border-radius:60px}.confirm-btn[data-v-601f627b]{width:200px;height:42px;line-height:42px;border-radius:30px;margin-top:%?300?%;background:-moz-linear-gradient(left,#f15b6c,#e10a07 100%);background:-webkit-gradient(linear,left top,left right,color-stop(0,#f15b6c),color-stop(100%,#e10a07));background:-webkit-linear-gradient(left,#f15b6c,#e10a07);background:-o-linear-gradient(left,#f15b6c 0,#e10a07 100%);background:-ms-linear-gradient(left,#f15b6c 0,#e10a07 100%);background:linear-gradient(270deg,#f15b6c 0,#e10a07);color:#fff;font-size:%?32?%}.confirm-btn[data-v-601f627b]:after{border-radius:60px}.headers[data-v-601f627b]{text-align:center}.headers > uni-image[data-v-601f627b]{width:%?400?%;height:%?400?%}.footer[data-v-601f627b]{margin-top:%?32?%;font-size:%?24?%;color:#666;text-align:center;display:flex;justify-content:center;padding:0 %?30?%}uni-page-body[data-v-601f627b]{background:#fff}body.?%PAGE?%[data-v-601f627b]{background:#fff}.send-msg[data-v-601f627b]{border-radius:30px;color:#000;background:#fff;height:30px;font-size:14px;line-height:30px}.container[data-v-601f627b]{top:0;padding-top:%?32?%;position:relative;width:100%;height:100%;overflow:hidden;background:#fff;color:#fff}.container .mp_wxBox .headers[data-v-601f627b]{margin:35% auto %?50?%;text-align:center;border-radius:%?60?%;width:%?650?%;height:%?300?%;line-height:%?450?%}.container .mp_wxBox .headers uni-image[data-v-601f627b]{width:%?300?%;height:%?300?%}.container .mp_wxBox .content[data-v-601f627b]{text-align:center}.container .mp_wxBox uni-text[data-v-601f627b]{display:block;color:#9d9d9d;margin-top:%?40?%}.container .mp_wxBox .bottom[data-v-601f627b]{line-height:%?80?%;border-radius:%?80?%;margin:%?70?% %?50?%;height:%?80?%;font-size:%?35?%}.wrapper[data-v-601f627b]{position:relative;z-index:90;background:#fff;padding-bottom:20px}.input-content[data-v-601f627b]{padding:0 20px}.confirm-btn[data-v-601f627b]{width:300px;height:42px;line-height:42px;border-radius:30px;margin-top:80px;background:linear-gradient(114deg,#ff6f9c,#ff98bd);color:#fff}.confirm-btn[data-v-601f627b]:after{border-radius:60px}',""]),e.exports=t},"71a8":function(e,t,n){var i=n("c86c");t=i(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.u-checkbox[data-v-326f4b11]{display:inline-flex;align-items:center;overflow:hidden;-webkit-user-select:none;user-select:none;line-height:1.8}.u-checkbox__icon-wrap[data-v-326f4b11]{color:#606266;flex:none;display:-webkit-flex;display:flex;flex-direction:row;align-items:center;justify-content:center;box-sizing:border-box;width:%?42?%;height:%?42?%;color:transparent;text-align:center;transition-property:color,border-color,background-color;font-size:20px;border:1px solid #c8c9cc;transition-duration:.2s}.u-checkbox__icon-wrap--circle[data-v-326f4b11]{border-radius:100%}.u-checkbox__icon-wrap--square[data-v-326f4b11]{border-radius:%?6?%}.u-checkbox__icon-wrap--checked[data-v-326f4b11]{color:#fff;background-color:#2979ff;border-color:#2979ff}.u-checkbox__icon-wrap--disabled[data-v-326f4b11]{background-color:#ebedf0;border-color:#c8c9cc}.u-checkbox__icon-wrap--disabled--checked[data-v-326f4b11]{color:#c8c9cc!important}.u-checkbox__label[data-v-326f4b11]{word-wrap:break-word;margin-left:%?10?%;margin-right:%?24?%;color:#606266;font-size:%?30?%}.u-checkbox__label--disabled[data-v-326f4b11]{color:#c8c9cc}',""]),e.exports=t},7940:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("5ef2"),n("d4b5"),n("aa9c"),n("e966");var i={data:function(){return{checked:!1,mobile:"",code:"",weixinLogin:!1,sending:!1,sendTime:"获取验证码",count:60,weixinPhone:!1,sendDataList:{},sessionkey:"",phoneNum:!1,isopen:!1}},onLoad:function(e){var t=this;e.inviterCode&&this.$queue.setData("inviterCode",e.inviterCode),this.$Request.getT("/app/common/type/53").then((function(e){0==e.code&&e.data&&e.data.value&&e.data.value})),this.$Request.getT("/app/common/type/188").then((function(e){0==e.code&&e.data&&e.data.value&&"是"==e.data.value&&(t.phoneNum=!0)})),this.selbindwx()},onShow:function(){var e=this;this.$Request.get("/app/common/type/333").then((function(t){if(0==t.code&&t.data&&t.data.value&&"是"==t.data.value){var n=navigator.userAgent.toLowerCase();-1!==n.indexOf("micromessenger")?e.isopen=!0:e.isopen=!1}}))},methods:{selbindwx:function(){var e=this,t=navigator.userAgent.toLowerCase();if(-1!==t.indexOf("micromessenger")){var n;uni.getStorageSync("openid"),uni.getStorageSync("userId");if(-1!==window.location.href.indexOf("?code=")||-1!==window.location.href.indexOf("&code="))n=-1!==window.location.href.indexOf("?code=")?window.location.href.split("?code=")[1].split("&")[0]:window.location.href.split("&code=")[1].split("&")[0],this.$Request.get("/app/Login/getOpenId?code="+n).then((function(t){uni.setStorageSync("openid",t.data),e.$Request.get("/app/Login/openid/login?openId="+t.data.openid).then((function(t){0==t.code?(e.$queue.setData("phone",t.user.phone),e.$queue.setData("userId",t.user.userId),e.$queue.setData("token",t.token),e.$queue.setData("userName",t.user.userName),e.$queue.setData("avatar",t.user.avatar),e.$queue.setData("invitationCode",t.user.invitationCode),e.$queue.setData("inviterCode",t.user.inviterCode),uni.switchTab({url:"/pages/my/index"})):uni.navigateTo({url:"/pages/public/bind"})}))}))}},bingwx:function(){var e=this;if(0==this.checked)return uni.showToast({icon:"none",position:"bottom",title:"请同意《用户协议》和《隐私政策》"}),!1;var t=navigator.userAgent.toLowerCase();if(-1!==t.indexOf("micromessenger")){var n,i=uni.getStorageSync("openid");uni.getStorageSync("userId");if(i)this.$Request.get("/app/Login/openid/login?openId="+i).then((function(t){0==t.code?(e.$queue.setData("phone",t.user.phone),e.$queue.setData("userId",t.user.userId),e.$queue.setData("token",t.token),e.$queue.setData("userName",t.user.userName),e.$queue.setData("avatar",t.user.avatar),e.$queue.setData("invitationCode",t.user.invitationCode),e.$queue.setData("inviterCode",t.user.inviterCode),uni.switchTab({url:"/pages/index/index"})):uni.navigateTo({url:"/pages/public/bind"})}));else if(-1!==window.location.href.indexOf("?code=")||-1!==window.location.href.indexOf("&code="))n=-1!==window.location.href.indexOf("?code=")?window.location.href.split("?code=")[1].split("&")[0]:window.location.href.split("&code=")[1].split("&")[0],this.$Request.get("/app/Login/getOpenId?code="+n).then((function(t){uni.setStorageSync("openid",t.data),e.$Request.get("/app/Login/openid/login?openId="+t.data).then((function(t){0==t.code?(e.$queue.setData("phone",t.user.phone),e.$queue.setData("userId",t.user.userId),e.$queue.setData("token",t.token),uni.setStorageSync("sex",t.user.sex),uni.setStorageSync("openId",t.user.openId),e.$queue.setData("userName",t.user.userName),e.$queue.setData("avatar",t.user.avatar),e.$queue.setData("invitationCode",t.user.invitationCode),e.$queue.setData("inviterCode",t.user.inviterCode),uni.switchTab({url:"/pages/index/index"})):uni.navigateTo({url:"/pages/public/bind"})}))}));else window.location.href="https://open.weixin.qq.com/connect/oauth2/authorize?appid="+this.$queue.getWxAppid()+"&redirect_uri="+window.location.href.split("#")[0]+"&response_type=code&scope=snsapi_userinfo#wechat_redirect"}},gotoxieyi:function(e){uni.navigateTo({url:e})},wxGetUserInfo:function(e){var t=this;0!=this.checked?wx.getUserProfile({desc:"业务需要",success:function(n){console.log("infoRes.encryptedData__________:"+JSON.stringify(n.userInfo));var i=n.userInfo.nickName,a=n.userInfo.avatarUrl,o=n.userInfo.gender;try{t.$queue.showLoading("正在登录中..."),t.login(i,a,o)}catch(e){}}}):uni.showToast({title:"请先同意《隐私政策》和《用户服务协议》",icon:"none"})},login:function(e,t,n){var i=this;uni.login({provider:"weixin",success:function(a){console.log(a,"************");var o={code:a.code};i.$Request.get("/app/Login/wxLogin",o).then((function(a){if(0==a.code){uni.hideLoading(),uni.setStorageSync("openId",a.data.open_id),uni.setStorageSync("unionId",a.data.unionId),i.sessionkey=a.data.session_key;var o="";uni.getStorageSync("inviterCode")&&(o=uni.getStorageSync("inviterCode"));var r={openId:uni.getStorageSync("openId"),unionId:uni.getStorageSync("unionId"),userName:e,avatar:t,sex:n,inviterCode:o};i.sendDataList=r,i.flag=a.data.isPhone,"2"==i.flag&&i.phoneNum?i.weixinPhone=!0:i.getWeixinInfo(r)}else uni.showToast({icon:"none",title:a.msg,duration:2e3}),console.log(a,"失败")}))}})},getPhoneNumber:function(e){"getPhoneNumber:fail user deny"==e.detail.errMsg?console.log("用户拒绝提供手机号"):(console.log("用户同意提供手机号"),console.log(e),this.setPhoneByInsert(e.detail.encryptedData,e.detail.iv))},setPhoneByInsert:function(e,t){var n=this,i={decryptData:e,key:this.sessionkey,iv:t};this.$Request.postJson("/app/Login/selectPhone",i).then((function(e){0==e.code?(n.phone=e.data.phoneNumber,n.getWeixinInfo(n.sendDataList)):uni.showToast({title:e.msg,icon:"none",duration:2e3})}))},getWeixinInfo:function(e){uni.showLoading({title:"登录中..."});var t={openId:e.openId,unionId:e.unionId,userName:e.userName,avatar:e.avatar,sex:e.sex,phone:this.phone,inviterCode:e.inviterCode};this.$Request.postJson("/app/Login/insertWxUser",t).then((function(e){uni.hideLoading(),0==e.code?(uni.setStorageSync("token",e.token),uni.setStorageSync("userName",e.user.userName),uni.setStorageSync("avatar",e.user.avatar),uni.setStorageSync("phone",e.user.phone),uni.setStorageSync("invitationCode",e.user.invitationCode),uni.setStorageSync("sex",e.user.sex),uni.setStorageSync("userId",e.user.userId),uni.setStorageSync("openId",e.user.openId),uni.navigateBack()):uni.showModal({showCancel:!1,title:"登录失败",content:e.msg})}))},weixinLo:function(){var e=this;uni.login({provider:"weixin",success:function(t){e.$queue.showLoading("正在登录中..."),console.error(t.authResult),e.$queue.setData("weixinToken",t.authResult.access_token),e.$queue.setData("unionid",t.authResult.unionid),e.$queue.setData("weixinOpenid",t.authResult.openid),e.$Request.postJson("/app/login/loginApp",{token:t.authResult.access_token,unionid:t.authResult.unionid,openid:t.authResult.openid}).then((function(t){if(console.log(JSON.stringify(t)),0===t.code){if("android"==uni.getSystemInfoSync().platform){var n=plus.push.getClientInfo().clientid;e.$Request.postT("/app/login/updateClientId?clientId="+n+"&userId="+t.userId).then((function(e){}))}e.$queue.setData("token",t.uuid),e.$queue.setData("userId",t.userId),e.getUserInfo(t.userId,t.token)}else uni.hideLoading(),uni.navigateTo({url:"/pages/public/wxmobile"})}))}})},forget:function(){uni.navigateTo({url:"/pages/public/pwd"})},register:function(){this.checked?uni.navigateTo({url:"/pages/public/loginphone"}):uni.showToast({title:"请先同意《隐私政策》和《用户服务协议》",icon:"none"})},inputChange:function(e){var t=e.currentTarget.dataset.key;this[t]=e.detail.value},navBack:function(){uni.navigateBack()},getUserInfo:function(e,t){var n=this;this.$Request.postJson("/app/selectUserById?userId="+e).then((function(e){0===e.code?(n.$queue.setData("token",e.data.uuid),n.$queue.setData("image_url",e.data.imageUrl?e.data.imageUrl:"/static/img/common/logo.jpg"),n.$queue.setData("inviterCode",e.data.inviterCode),n.$queue.setData("invitationCode",e.data.invitationCode),n.$queue.setData("grade",e.data.grade),n.$queue.setData("mobile",e.data.mobile),n.$queue.setData("isInvitation",e.data.isInvitation),n.$queue.setData("nickName",e.data.nickName?e.data.nickName:e.data.phone),n.$queue.setData("gender",parseInt(e.data.gender)),uni.switchTab({url:"/pages/index/index"})):(uni.showModal({showCancel:!1,title:"登录失败",content:e.msg}),n.$queue.logout()),uni.hideLoading()}))}}};t.default=i},"83f7":function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){return i}));var i={uIcon:n("3688").default},a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"u-checkbox",style:[e.checkboxStyle]},[n("v-uni-view",{staticClass:"u-checkbox__icon-wrap",class:[e.iconClass],style:[e.iconStyle],on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toggle.apply(void 0,arguments)}}},[n("u-icon",{staticClass:"u-checkbox__icon-wrap__icon",attrs:{name:"checkbox-mark",size:e.checkboxIconSize,color:e.iconColor}})],1),n("v-uni-view",{staticClass:"u-checkbox__label",style:{fontSize:e.$u.addUnit(e.labelSize)},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onClickLabel.apply(void 0,arguments)}}},[e._t("default")],2)],1)},o=[]},c754:function(e,t,n){"use strict";n.r(t);var i=n("f000"),a=n("25a8");for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);n("02eb");var r=n("828b"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"601f627b",null,!1,i["a"],void 0);t["default"]=s.exports},ce40:function(e,t,n){"use strict";var i=n("0cfc"),a=n.n(i);a.a},f000:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){return i}));var i={uCheckbox:n("2633").default},a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"container"},[i("v-uni-view",{staticStyle:{"text-align":"center"}},[i("v-uni-image",{staticStyle:{width:"120upx",height:"120upx","margin-top":"140upx","border-radius":"20upx"},attrs:{src:n("0eeb")}}),i("v-uni-button",{staticClass:"confirm-btn",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.register.apply(void 0,arguments)}}},[e._v("手机号登录")]),i("v-uni-view",{staticClass:"footer"},[i("u-checkbox",{attrs:{"label-size":"26rpx",shape:"circle"},model:{value:e.checked,callback:function(t){e.checked=t},expression:"checked"}},[i("v-uni-view",{staticClass:"flex"},[e._v("我已认真阅读"),i("v-uni-text",{staticStyle:{color:"#4e86f8"},on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.gotoxieyi("/my/setting/mimi")}}},[e._v("《隐私政策》")]),e._v("和"),i("v-uni-text",{staticStyle:{color:"#4e86f8"},on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.gotoxieyi("/my/setting/xieyi")}}},[e._v("《用户服务协议》")])],1)],1)],1),e.isopen?i("v-uni-view",{staticStyle:{"margin-top":"50rpx"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.bingwx.apply(void 0,arguments)}}},[i("v-uni-image",{staticStyle:{width:"80rpx",height:"80rpx"},attrs:{src:n("327c")}}),i("v-uni-view",{staticStyle:{color:"#000","margin-top":"10rpx"}},[e._v("微信登录")])],1):e._e()],1)],1)},o=[]},f1a1:function(e,t,n){"use strict";n.r(t);var i=n("4cfb"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);t["default"]=a.a}}]);