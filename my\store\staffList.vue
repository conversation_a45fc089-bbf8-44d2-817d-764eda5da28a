<template>
	<view class="container">
		<!-- 员工列表 -->
		<view class="staff-list">
			<view class="add-staff">添加员工</view>
			<view class="staff-item" v-for="(staff, index) in staffList" :key="index">
				<view class="staff-avatar">
					<image :src="staff.avatar" mode="aspectFill"></image>
				</view>
				<view class="staff-info">
					<view class="staff-name">{{ staff.name }}</view>
					<view class="staff-join-time">加入时间：{{ staff.joinTime }}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			storeId: '',
			storeName: '',
			staffList: [
				{
					name: '韩寒',
					joinTime: '2025-02-15',
					avatar: 'https://photo.zastatic.com/images/common-cms/it/20250527/1748338545852_26938_t.png'
				},
				{
					name: '陈晨晨',
					joinTime: '2025-02-15',
					avatar: 'https://photo.zastatic.com/images/common-cms/it/20250527/1748338545852_26938_t.png'
				},
				{
					name: '陈晨晨',
					joinTime: '2025-02-15',
					avatar: 'https://photo.zastatic.com/images/common-cms/it/20250527/1748338545852_26938_t.png'
				},
				{
					name: '陈晨晨',
					joinTime: '2025-02-15',
					avatar: 'https://photo.zastatic.com/images/common-cms/it/20250527/1748338545852_26938_t.png'
				},
				{
					name: '陈晨晨',
					joinTime: '2025-02-15',
					avatar: 'https://photo.zastatic.com/images/common-cms/it/20250527/1748338545852_26938_t.png'
				},
				{
					name: '陈晨晨',
					joinTime: '2025-02-15',
					avatar: 'https://photo.zastatic.com/images/common-cms/it/20250527/1748338545852_26938_t.png'
				},
				{
					name: '陈晨晨',
					joinTime: '2025-02-15',
					avatar: 'https://photo.zastatic.com/images/common-cms/it/20250527/1748338545852_26938_t.png'
				},
				{
					name: '陈晨晨',
					joinTime: '2025-02-15',
					avatar: 'https://photo.zastatic.com/images/common-cms/it/20250527/1748338545852_26938_t.png'
				}
			]
		}
	},
	onLoad(options) {
		// 获取传递的门店信息
		if (options.storeId) {
			this.storeId = options.storeId
			this.storeName = options.storeName
		}
		this.loadStaffData()
	},
	methods: {
		loadStaffData() {
			// 这里可以调用API获取员工数据
			// this.$Request.get("/app/store/staff", {
			//     storeId: this.storeId
			// }).then(res => {
			//     if (res.code == 0) {
			//         this.staffList = res.data.list
			//     }
			// });
		}
	}
}
</script>

<style scoped>
page {
	background: #f5f5f5;
}

.container {
	min-height: 100vh;
	padding: 20rpx;
}

.staff-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}
.add-staff {
	position: fixed;
	top: 20rpx;
	right: 32rpx;
	z-index: 10000;
}
.staff-item {
	background: #FFFFFF;
	border-radius: 12rpx;
	padding: 30rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.staff-avatar {
	width: 80rpx;
	height: 80rpx;
	margin-right: 30rpx;
}

.staff-avatar image {
	width: 100%;
	height: 100%;
	border-radius: 50%;
}

.staff-info {
	flex: 1;
}

.staff-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
	margin-bottom: 8rpx;
}

.staff-join-time {
	font-size: 28rpx;
	color: #666666;
}
</style>
