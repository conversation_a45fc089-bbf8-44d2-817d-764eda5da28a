{
    "name" : "幸福汇",
    "appid" : "__UNI__B825803",
    "description" : "",
    "versionName" : "1.0.1",
    "versionCode" : 101,
    "transformPx" : false,
    "sassImplementationName" : "node-sass",
    /* 5+App特有相关 */
    "app-plus" : {
        "privacy" : {
            "prompt" : "template",
            "template" : {
                "title" : "服务协议和隐私政策",
                "message" : "　　请你务必审慎阅读、充分理解“服务协议”和“隐私政策”各条款，包括但不限于：为了更好的向你提供服务，我们需要收集你的设备标识、操作日志等信息用于分析、优化应用性能。<br/>　　你可阅读<a href=\"http://xiangqin.unvue.cn/my/setting/xieyi\">《服务协议》</a>和<a href=\"http://xiangqin.unvue.cn/my/setting/mimi\">《隐私政策》</a>了解详细信息。如果你同意，请点击下面同意按钮开始接受我们的服务。",
                "buttonAccept" : "同意",
                "buttonRefuse" : "不同意"
            }
        },
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        /* 模块配置 */
        "modules" : {
            "Payment" : {},
            "Share" : {},
            "OAuth" : {},
            "Maps" : {},
            "VideoPlayer" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.INTERNET\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>"
                ],
                "autoSdkPermissions" : true
            },
            /* ios打包配置 */
            "ios" : {
                "dSYMs" : false
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "payment" : {
                    "weixin" : {
                        "__platform__" : [ "android" ],
                        "appid" : "wxdbd888f4bc8f08cd",
                        "UniversalLinks" : "https://tk.gomyorder.cn/"
                    },
                    "alipay" : {
                        "__platform__" : [ "android" ]
                    }
                },
                "share" : {
                    "weixin" : {
                        "appid" : "wxdbd888f4bc8f08cd",
                        "UniversalLinks" : "https://tk.gomyorder.cn/"
                    }
                },
                "oauth" : {
                    "weixin" : {
                        "appid" : "wxdbd888f4bc8f08cd",
                        "appsecret" : "d73468b34d33de7e31b0e508e7acf130",
                        "UniversalLinks" : "https://tk.gomyorder.cn/"
                    }
                },
                "ad" : {},
                "maps" : {
                    "amap" : {
                        "appkey_ios" : "16357572a10630ce4894a69934298e1b",
                        "appkey_android" : "36d71986db0b3ada5470f2c8c4586db2",
                        "name" : "amapB6suWzr5h"
                    }
                }
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/icons/72x72.png",
                    "xhdpi" : "unpackage/icons/96x96.png",
                    "xxhdpi" : "unpackage/icons/144x144.png",
                    "xxxhdpi" : "unpackage/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/icons/76x76.png",
                        "app@2x" : "unpackage/icons/152x152.png",
                        "notification" : "unpackage/icons/20x20.png",
                        "notification@2x" : "unpackage/icons/40x40.png",
                        "proapp@2x" : "unpackage/icons/167x167.png",
                        "settings" : "unpackage/icons/29x29.png",
                        "settings@2x" : "unpackage/icons/58x58.png",
                        "spotlight" : "unpackage/icons/40x40.png",
                        "spotlight@2x" : "unpackage/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/icons/120x120.png",
                        "app@3x" : "unpackage/icons/180x180.png",
                        "notification@2x" : "unpackage/icons/40x40.png",
                        "notification@3x" : "unpackage/icons/60x60.png",
                        "settings@2x" : "unpackage/icons/58x58.png",
                        "settings@3x" : "unpackage/icons/87x87.png",
                        "spotlight@2x" : "unpackage/icons/80x80.png",
                        "spotlight@3x" : "unpackage/icons/120x120.png"
                    }
                }
            }
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "wx9d2aafedc37441a7",
        "setting" : {
            "urlCheck" : false,
            "minified" : true,
            "postcss" : false,
            "es6" : true
        },
        "usingComponents" : true,
        "optimization" : {
            "subPackages" : true
        },
        "permission" : {
            "scope.userLocation" : {
                "desc" : "获取用户位置"
            }
        },
        "requiredPrivateInfos" : [ "getLocation", "chooseLocation" ]
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "uniStatistics" : {
        "enable" : false
    },
    "h5" : {
        "router" : {
            "mode" : "history"
        },
        "devServer" : {
            "https" : false,
            "port" : "",
            "disableHostCheck" : true,
            "proxy" : {
                // 可代理多个
                "/TencentGet" : {
                    "target" : "https://apis.map.qq.com/ws/geocoder/v1/", // 腾讯地图逆地址解析
                    "changeOrigin" : true,
                    "secure" : false,
                    "pathRewrite" : {
                        "^/TencentGet" : ""
                    }
                }
            }
        },
        "sdkConfigs" : {
            "maps" : {
                "qqmap" : {
                    "key" : "BFBBZ-X6Z3B-DN2UX-JAWGE-E5QKJ-AJF53"
                }
            }
        },
        "title" : "小码哥婚恋"
    }
}
