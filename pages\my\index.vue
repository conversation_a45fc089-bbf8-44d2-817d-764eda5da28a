<template>
	<view class="text-white ">
		<!-- #ifndef H5 -->
		<!-- 导航栏 -->
		<u-navbar title="我的" :is-back="false" :background="background" title-color="#111224"
			:border-bottom="false"></u-navbar>
		<!-- #endif -->

		<view class="bgjb">
			<view class=" u-flex u-p-l-30 u-p-t-30 ">
				<view class="u-m-r-10" @click.stop="goNavs('/pages/my/userinfo')">
					<image :src="avatar" style="width: 170rpx;height: 200rpx;border-radius:30rpx;" mode="aspectFill">
					</image>
				</view>
				<view class="u-flex-1 u-m-l-10 text-white" v-if="!isLogin">
					<view class="u-font-18  text-bold flex align-center">{{userName}}
						<image src="../../static/images/my/rzicon.png" v-if="statusStauts==2"
							style="width: 40rpx;height: 40rpx;margin-left: 8rpx;"></image>
					</view>
					<view class="renzheng" @click.stop="goRenzheng">
						<image src="../../static/images/my/renz.png"
							style="width: 32rpx;height: 32rpx;margin-right:8rpx"></image>
						<view>
							<text v-if="statusStauts==-1">去认证</text>
							<text v-if="statusStauts==1">待审核</text>
							<text v-if="statusStauts==2">认证中心</text>
							<text v-if="statusStauts==3">已拒绝</text>
						</view>


					</view>

					<view class="" style="color: #999999;font-size: 26rpx;" @click.stop="gouserind">
						完善资料
						<u-icon name="arrow-right" color="#999999" size="26"></u-icon>
					</view>
				</view>
				<view v-else class="text-xl u-p-l-20 text-bold" @click="goLogin('/pages/public/login')">
					登录
				</view>
			</view>
			<view class="flex justify-around" style="margin-top:30rpx;">
				<view style="text-align: center;" @click="goNav('/my/gird/guanzhu?name=我的粉丝&type=1')">
					<view class="text-xl">{{fans}}</view>
					<view style="color:#666666;font-size: 24rpx;" class="margin-top-xs">我的粉丝</view>
				</view>
				<view style="text-align: center;" @click="goNav('/my/gird/guanzhu?name=我的关注&type=2')">
					<view class="text-xl">{{follow}}</view>
					<view style="color:#666666;font-size:24rpx;" class="margin-top-xs">我的关注</view>
				</view>
				<view style="text-align: center;" @click="goNav('/my/gird/like')">
					<view class="text-xl">{{likeMyCount}}</view>
					<view style="color:#666666;font-size:24rpx;" class="margin-top-xs">喜欢我的</view>
				</view>
				<view style="text-align: center;" @click="goNav('/my/gird/mylike')">
					<view class="text-xl">{{myLikeCount}}</view>
					<view style="color:#666666;font-size:24rpx;" class="margin-top-xs">我喜欢的</view>
				</view>
			</view>
		</view>

		<view class="vip flex justify-center">
			<image class="vip-bg" src="../../static/images/my/bg.png" mode=""></image>
			<view class="vip-sub flex justify-center align-center" @click="goNav('/my/vip/index')">
				{{isVip?'已开通':'开通'}}
			</view>
		</view>

		<!-- <view :class="huiyuanSelect != '否'?'':'margin-top'">
			<view class="select flex justify-center" v-if="shangxianSelect!= '否'">
				<view class="select-box flex justify-between align-center">
					<image v-for="(item,index) in classType" :key="index" @click="goNavs(item.url,index)"
						:src="item.imageUrl" mode="">
					</image>
				</view>
			</view>
		</view> -->


		<!-- <view class="bgbox flex align-center justify-between" @click="goNavs('/package/pages/play/index')"
			v-if="shangxianSelect!= '否'">
			<view class="peiwan_center">
				<image src="../../static/images/my/peiwanzhongxin.png"></image>
				陪玩中心
			</view>
			<image src="../../static/images/my/right.png" style="width: 15rpx;height: 30rpx;"></image>
		</view> -->

		<view class="bgbox1 flex align-center justify-between  padding-top">

			<view class=" text-center image_tus" @click="goNav('/package/pages/detail/dongtai')">
				<image src="../../static/images/my/dongtai.png"></image>
				<view>我的动态</view>
			</view>
			<view class=" text-center image_tus" @click="goNav('/my/wallet/qianbao')">
				<image src="../../static/images/my/ianbao.png"></image>
				<view>我的钱包</view>

			</view>
			<view class=" image_tus text-center" @click="goNav('/package/pages/detail/tuijian')">
				<image src="../../static/images/my/tuijian.png"></image>
				<view>超级推荐</view>
			</view>

			<view class=" image_tus text-center" @click="goNav('/my/Events/huodong')">
				<image src="../../static/images/my/qianxian.png"></image>
				<view>我的活动</view>
			</view>
			<!-- <view class=" image_tus text-center" @click="goNav('/package/pages/game/pay')">
				<image src="../../static/images/my/qianxian.png"></image>
				<view>购买记录</view>
			</view> -->
			<!-- <view class=" image_tus text-center" @click="goNav('/package/pages/detail/qianxian')">
				<image src="../../static/images/my/qianxian.png"></image>
				<view>红娘牵线</view>
			</view> -->
		</view>

		<view class="bgbox2 padding-tb-xs">
			<view class="text-lg text-bold padding-lr padding-top">推荐工具</view>
			<view class=" flex align-center flex-wrap" style="margin-top: 30rpx;">
				<view class=" image_tu text-center" @click="goChat">
					<image src="../../static/images/my/kefu.png"></image>
					<view>客服中心</view>
				</view>
				<view class=" image_tu text-center" @click="goNav('/my/team/team')" v-if="userId &&  zhiRate != 0">
					<image src="../../static/images/my/team.png"></image>
					<view>我的下线</view>
				</view>
				<view class=" image_tu text-center" @click="goNavs('/pages/my/invitationUser')">
					<image src="../../static/images/my/yao.png"></image>
					<view>邀请好友</view>
				</view>
				<!-- <view class=" text-center image_tu" @click="goNav('/package/pages/detail/goreport')"
					v-if="yueSelect != '否'">
					<image src="../../static/images/my/tousu.png"></image>
					<view>我的投诉</view>
				</view> -->

				<view class=" text-center image_tu" @click="goNavs('/package/pages/feedbackIndex/feedbackIndex')">
					<image src="../../static/images/my/help.png"></image>
					<view>帮助中心</view>
				</view>
				<view class=" text-center image_tu" @click="goNavs('/my/feedback/index')">
					<image src="../../static/images/my/yijian.png"></image>
					<view>意见反馈</view>
				</view>
				<view class=" text-center image_tu" @click="goNavs('/my/setting/index')">
					<image src="../../static/images/my/set.png"></image>
					<view>设置中心</view>
				</view>
				<view class=" text-center image_tu" @click="goNavs('/my/store/info')">
					<image src="https://photo.zastatic.com/images/common-cms/it/20250527/1748338175292_597458_t.png"></image>
					<view>我的门店</view>
				</view>
				<view class=" text-center image_tu" @click="goNavs('/my/setting/index')">
					<image src="https://photo.zastatic.com/images/common-cms/it/20250527/1748338175289_448837_t.png"></image>
					<view>我的订单</view>
				</view>

			</view>
		</view>
		<!-- modal弹窗 -->
		<u-modal v-model="meShowModel" :content="meContent" :title="meTitle" :show-cancel-button='meShowCancel'
			@cancel="meHandleClose" @confirm="meHandleBtn" :confirm-text='meConfirmText'
			:cancel-text='meCancelText'></u-modal>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				background: {
					backgroundImage: 'linear-gradient(90deg, #E4E7F8 0%, #F1E3F4 46%, #FDE1EF 100%)'
				},
				//弹窗
				meShowModel: false, //是否显示弹框
				meShowCancel: true, //是否显示取消按钮
				meTitle: '提示', //弹框标题
				meContent: '', //弹框内容
				meConfirmText: '确认', //确认按钮的文字
				meCancelText: '取消', //关闭按钮的文字
				meIndex: '', //弹窗的key
				zhiRate: 0,
				teamSelect: '否',
				yhqSelect: '否',
				YaoqingShangJin: false,
				huiyuanSelect: '否',
				yueSelect: '否',
				shangxianSelect: '否',
				avatar: '../../static/logo.png',
				isLogin: true,
				userName: '匿名',
				// userType: '', //0  未认证   1 实名认证   2学生认证
				statusStauts: -1, //认证状态
				myLikeCount: 0, //我喜欢的
				fans: 0, //粉丝数
				follow: 0, //关注数
				likeMyCount: 0, //喜欢我的
				userId: '',
				isVip: false,
				invitationCode: '', //邀请码
				list: [],
				arr: [],
				showModal: true,
				Huizhang: false,
				consortiaId: '',
				classType: [],
				SelKeFu: '2',

			}
		},
		onLoad() {

			this.teamSelect = this.$queue.getData('teamSelect');
			this.yhqSelect = this.$queue.getData('yhqSelect');
			this.shangxianSelect = this.$queue.getData('shangxianSelect');
			this.huiyuanSelect = this.$queue.getData('huiyuanSelect');
			this.yueSelect = this.$queue.getData('yueSelect');


		},
		onShow() {
			this.SelKeFu = this.$queue.getData('SelKeFu');
			this.getClassList()
			this.consortiaId = this.$queue.getData('consortiaId');
			let YaoqingShangJin = this.$queue.getData('YaoqingShangJin');
			if (YaoqingShangJin && YaoqingShangJin != 0) {
				this.YaoqingShangJin = true;
			} else {
				this.YaoqingShangJin = false;
			}
			this.userId = uni.getStorageSync('userId')

			if (this.userId) {
				this.isLogin = false
				this.getUserInfo()
				this.getRenZheng()
				this.getAmount()

				// #ifdef MP-WEIXIN
				//订阅
				if (this.userId) {
					if (this.showModal) {
						setTimeout(d => {
							this.openMsg()
						}, 2000)
					}
				}
				// #endif
			} else {

				this.isLogin = true
				this.isVip = false
				this.userName = '匿名'
				this.myLikeCount = 0
				this.fans = 0
				this.follow = 0
				this.likeMyCount = 0
				this.avatar = '../../static/logo.png'
			}

		},
		methods: {

			//分类
			getClassList() {
				this.$Request.get("/app/banner/selectBannerList", {
					classify: 8
				}).then(res => {
					if (res.code == 0) {
						this.classType = res.data
						// this.swiperList = res.data
					}
				});
			},
			goNavs(e, index) {
				if (this.userId) {
					// #ifdef MP-WEIXIN
					if (uni.getStorageSync('sendMsg')) {
						wx.requestSubscribeMessage({
							tmplIds: this.arr,
							success(re) {
								if (datas.indexOf("accept") != -1) {
									console.log(re)
								}
							},
							fail: (res) => {
								console.log(res)
							}
						})
					}
					// #endif

					uni.navigateTo({
						url: e
					})

				} else {
					this.meShowModel = true
					this.meTitle = '提示'
					this.meContent = '您还未登录,请先登录'
					this.meIndex = 'm1'
					this.meShowCancel = true
				}
			},
			//确认
			meHandleBtn() {
				let that = this
				if (that.meIndex == 'm1') {
					uni.navigateTo({
						url: '/pages/public/login'
					})
				} else if (that.meIndex == 'm2') {
					uni.navigateTo({
						url: '/my/renzheng/index'
					})
				} else if (that.meIndex == 'm3') {
					uni.navigateTo({
						url: '/my/renzheng/index'
					})
				} else if (that.meIndex == 'm9') {
					uni.navigateTo({
						url: '/pages/my/userinfo'
					})
				} else if (that.meIndex == 'm6') {
					// #ifdef MP-WEIXIN
					if (uni.getStorageSync('sendMsg')) {
						wx.requestSubscribeMessage({
							tmplIds: that.arr,
							success(re) {
								if (datas.indexOf("accept") != -1) {
									console.log(re)
								}
							},
							fail: (res) => {
								console.log(res)
							}
						})
					}
					// #endif
				}
			},
			//取消
			meHandleClose() {
				let that = this
				if (that.meIndex == 'm1') {

				}
			},

			gouserind() {
				if (this.userId) {
					this.$Request.get("/app/userCertification/getMyUserCertification", {
						authType: 1
					}).then(res => {
						if (res.code == 0 && res.data) {
							// 0审核中 1通过 2拒绝
							if (res.data.status == 0) {
								this.statusStauts = 1 //审核中
							} else if (res.data.status == 1) {
								this.statusStauts = 2 //已实名
							} else if (res.data.status == 2) {
								this.statusStauts = 3 //已拒绝
							}
						} else {
							this.statusStauts = -1 //未实名
						}
						if (this.statusStauts == 1) { //待审核
							this.meShowModel = true
							this.meTitle = '提示'
							this.meContent = '实名认证审核中'
							this.meIndex = 'm2'
							this.meConfirmText = '去查看'
							this.meShowCancel = true
						} else if (this.statusStauts == 2) { //已通过
							uni.navigateTo({
								url: '/my/setting/userinfo'
							})
						} else if (this.statusStauts == 3) { //已通过
							this.meShowModel = true
							this.meTitle = '提示'
							this.meContent = '实名审核被拒绝'
							this.meIndex = 'm3'
							this.meConfirmText = '去认证'
							this.meShowCancel = true
						} else if (this.statusStauts == -1) { //已通过
							uni.navigateTo({
								url: '/my/renzheng/index'
							})
						}


					});
				} else {
					this.meShowModel = true
					this.meTitle = '提示'
					this.meContent = '您还未登录,请先登录'
					this.meIndex = 'm1'
					this.meShowCancel = true
				}
			},
			//实名认证
			goRenzheng() {
				if (this.userId) {
					this.$Request.get("/app/user/selectUserById").then(res => {
						if (res.code == 0 && res.data) {
							if (res.data.phone) {
								this.$Request.get("/app/userCertification/getMyUserCertification", {
									authType: 1
								}).then(res => {
									if (res.code == 0 && res.data) {
										// 0审核中 1通过 2拒绝
										if (res.data.status == 0) {
											this.statusStauts = 1 //审核中
										} else if (res.data.status == 1) {
											this.statusStauts = 2 //已实名
										} else if (res.data.status == 2) {
											this.statusStauts = 3 //已拒绝
										}
									} else {
										this.statusStauts = -1 //未实名
									}
									if (this.statusStauts == 1) { //待审核
										this.meShowModel = true
										this.meTitle = '提示'
										this.meContent = '实名认证审核中'
										this.meIndex = 'm2'
										this.meConfirmText = '去查看'
										this.meShowCancel = true
									} else if (this.statusStauts == 2) { //已通过
										uni.navigateTo({
											url: '/my/renzheng/index'
										})
									} else if (this.statusStauts == 3) { //已通过
										this.meShowModel = true
										this.meTitle = '提示'
										this.meContent = '实名审核被拒绝'
										this.meIndex = 'm3'
										this.meConfirmText = '去认证'
										this.meShowCancel = true
									} else if (this.statusStauts == -1) { //已通过
										uni.navigateTo({
											url: '/my/renzheng/index'
										})
									}

								});
							} else {
								this.meShowModel = true
								this.meTitle = '提示'
								this.meContent = '请先去完善个人信息'
								this.meIndex = 'm9'
								this.meConfirmText = '去完善'
								this.meShowCancel = true
							}
						}
					})

				} else {
					this.meShowModel = true
					this.meTitle = '提示'
					this.meContent = '您还未登录,请先登录'
					this.meIndex = 'm1'
					this.meShowCancel = true
				}
			},
			goNav(e) {
				if (this.userId) {
					this.$Request.get("/app/user/selectUserById").then(res => {
						if (res.code == 0 && res.data) {
							if (res.data.phone) {
								// #ifdef MP-WEIXIN
								if (uni.getStorageSync('sendMsg')) {
									wx.requestSubscribeMessage({
										tmplIds: this.arr,
										success(re) {
											if (datas.indexOf("accept") != -1) {
												console.log(re)
											}
										},
										fail: (res) => {
											console.log(res)
										}
									})
								}
								// #endif
								uni.navigateTo({
									url: e
								})
							} else {
								this.meShowModel = true
								this.meTitle = '提示'
								this.meContent = '请先去完善个人信息'
								this.meIndex = 'm9'
								this.meConfirmText = '去完善'
								this.meShowCancel = true
							}
						}
					})

				} else {
					this.meShowModel = true
					this.meTitle = '提示'
					this.meContent = '您还未登录,请先登录'
					this.meIndex = 'm1'
					this.meShowCancel = true
				}
			},
			goLogin(e) {
				uni.navigateTo({
					url: e
				})
			},
			getAmount() {
				this.$Request.get("/app/user/selectAmount").then(res => {
					if (res.code == 0) {
						this.myLikeCount = res.data.myLikeCount
						this.fans = res.data.careMyCount
						this.follow = res.data.myCareCount
						this.likeMyCount = res.data.likeMyCount
					}
				});
			},
			// 开启订阅消息
			openMsg() {
				console.log('订阅消息')
				var that = this
				uni.getSetting({
					withSubscriptions: true, //是否获取用户订阅消息的订阅状态，默认false不返回
					success(ret) {
						console.log(ret.subscriptionsSetting, '------------------')
						// if (ret.subscriptionsSetting.itemSettings && Object.keys(ret.subscriptionsSetting.itemSettings).length == 2) {
						if (ret.subscriptionsSetting.itemSettings) {
							uni.setStorageSync('sendMsg', true)
							uni.openSetting({ // 打开设置页
								success(rea) {
									console.log(rea.authSetting)
								}
							});
						} else { // 用户没有点击“总是保持以上，不再询问”则每次都会调起订阅消息
							console.log(99999)
							uni.setStorageSync('sendMsg', false)
							that.meShowModel = true
							that.meTitle = '提示'
							that.meContent = '为了更好的体验,请绑定消息推送'
							that.meConfirmText = '确认'
							that.meIndex = 'm6'
						}
					}
				})
			},
			getUserInfo() {
				this.$Request.get("/app/user/selectUserById").then(res => {
					if (res.code == 0) {
						// 会员  0不是  1是
						if (res.data.isVip && res.data.isVip == 1) {
							this.isVip = true
							uni.setStorageSync('isVIP', this.isVip)
							this.$Request.getT('/app/common/type/294').then(res => { //vip到期提醒
								if (res.code == 0) {
									if (res.data && res.data.value) {
										this.arr.push(res.data.value)
									}
								}
							})
						} else {
							this.isVip = false
							uni.setStorageSync('isVIP', this.isVip)
						}

						this.zhiRate = res.data.zhiRate ? res.data.zhiRate : 0;
						uni.setStorageSync('zhiRate', res.data.zhiRate)
						this.consortiaId = res.data.consortiaId
						uni.setStorageSync('consortiaId', res.data.consortiaId)

						this.userName = res.data.userName
						this.invitationCode = res.data.invitationCode
						this.avatar = res.data.avatar ? res.data.avatar : '../../static/logo.png'

						uni.setStorageSync('avatar', res.data.avatar)
						uni.setStorageSync('sex', res.data.sex)
						uni.setStorageSync('invitationCode', res.data.invitationCode)
						uni.setStorageSync('zhiFuBao', res.data.zhiFuBao)
						uni.setStorageSync('zhiFuBaoName', res.data.zhiFuBaoName)
					}
				});
			},
			getRenZheng() {
				this.$Request.get("/app/userCertification/getMyUserCertification", {
					authType: 1
				}).then(res => {
					if (res.code == 0 && res.data) {
						// 0审核中 1通过 2拒绝
						if (res.data.status == 0) {
							this.statusStauts = 1 //审核中
							uni.setStorageSync('statusStauts', this.statusStauts)
						} else if (res.data.status == 1) {
							this.statusStauts = 2 //已实名
							uni.setStorageSync('statusStauts', this.statusStauts)
						} else if (res.data.status == 2) {
							this.statusStauts = 3 //已拒绝
							uni.setStorageSync('statusStauts', this.statusStauts)
						}
					} else {
						this.statusStauts = -1 //未实名
						uni.setStorageSync('statusStauts', this.statusStauts)
					}
				});
			},

			// 在线客服
			goChat() {
				let that = this
				console.log(that.SelKeFu, 'that.SelKeFu ')
				if (that.SelKeFu === '1') {
					uni.makePhoneCall({
						phoneNumber: that.$queue.getData('kefuPhone') //仅为示例
					})

				} else if (that.SelKeFu === '2') {
					// #ifdef MP-WEIXIN
					try {
						wx.openCustomerServiceChat({
							extInfo: {
								url: that.$queue.getData('SelKeFuLink')
							},
							corpId: that.$queue.getData('SelKeFuAppId'),
							success(res) {},
							fail(res) {
								console.error(res)
							}
						})
					} catch (error) {
						console.error("catchcatch" + error)
						uni.showToast({
							title: '请更新至微信最新版本'
						});
					}
					// #endif
					// #ifndef MP-WEIXIN
					let url = that.$queue.getData('SelKeFuLink');
					if (url.indexOf('/pages/') !== -1 || url.indexOf('/my/') !== -1 || url.indexOf('/package/') !== -1) {
						uni.navigateTo({
							url
						});
					} else {
						//#ifndef H5
						uni.navigateTo({
							url: '/pages/index/webView?url=' + url
						});
						//#endif
						//#ifdef H5
						window.location.href = url;
						//#endif
					}
					// #endif
				} else {
					let token = that.$queue.getData('token');
					if (token) {
						uni.navigateTo({
							url: '/my/setting/chat'
						});
					} else {
						this.meShowModel = true
						this.meTitle = '提示'
						this.meContent = '您还未登录,请先登录'
						this.meIndex = 'm1'
						this.meShowCancel = true
					}
				}
			},
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #F7F7F7;
	}

	.bg {
		background-color: #FFFFFF;
	}

	.bgjb {
		// background: linear-gradient(to bottom, #e2d6feff, #F7F7F7);
		background-image: url('../../static/images/bgImg.png');
		background-size: 100% 100%;

	}

	.renzheng {
		// width: 165rpx;
		height: 50rpx;
		background: #FFFFFF;
		border-radius: 25rpx;
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);
		;
		display: inline-flex;
		align-items: center;
		justify-content: center;
		padding: 0 18rpx;
		margin: 15rpx 0;
	}

	.select {
		width: 100%;
		height: 170rpx;

		.select-box {
			width: 686rpx;
			height: 100%;

			image:nth-of-type(1) {
				width: 338rpx;
				height: 100%;
				border-radius: 24rpx;
			}

			image:nth-of-type(2) {
				width: 170rpx;
				height: 100%;
				border-radius: 24rpx;
			}

			image:nth-of-type(3) {
				width: 170rpx;
				height: 100%;
				border-radius: 24rpx;
			}
		}
	}

	.vip {
		// width: 100%;
		height: 100rpx;
		margin-top: 20rpx;
		margin: 20rpx 30rpx;
		position: relative;

		.vip-bg {
			width: 100%;
			height: 100%;
			position: absolute;
			top: 0%;
			left: 0;
		}

		.vip-sub {
			width: 100rpx;
			height: 48rpx;
			background: linear-gradient(90deg, #3C3D3C 0%, #5F5A56 100%);
			border-radius: 24rpx;
			position: absolute;
			top: 30rpx;
			right: 40rpx;
			font-size: 24rpx;
			color: #F5E2C6;
		}
	}

	.camera {
		width: 54px;
		height: 44px;

		&:active {
			background-color: #ededed;
		}
	}

	.btn-bg {
		width: 64px;
		height: 28px;
		background: linear-gradient(90deg, #CDA26E 0%, #DCB78A 100%);
		border-radius: 28px;
		text-align: center;
		line-height: 28px;
		margin-top: 4px;
		color: '#604320'
	}

	.lovip {
		width: 100rpx;
		height: 35rpx;
		position: relative;
		top: -39rpx;
	}

	.bgbox {
		background: #FFFFFF;
		padding: 20upx 30upx;
		border-radius: 24upx;
		margin: 30upx;
	}

	.bgbox1 {
		background: #FFFFFF;
		border-radius: 24upx;
		margin: 30upx;
	}

	.image_tus {
		width: 25%;
		margin-bottom: 30rpx;
		position: relative;
	}

	.image_tus image {
		width: 82upx;
		height: 82upx;
		margin-bottom: 5upx;
	}

	.bgbox2 {
		background: #FFFFFF;
		// padding: 40upx 30upx;
		border-radius: 24upx;
		margin: 30upx;
	}

	.image_tu {
		width: 25%;
		margin-bottom: 30rpx;
		position: relative;
	}

	.image_tu image {
		width: 48upx;
		height: 48upx;
		margin-bottom: 5upx;
	}

	.peiwan_center {
		display: flex;
		align-items: center;
		font-size: 32upx;
		font-weight: bold;
	}

	.peiwan_center image {
		width: 75upx;
		height: 75upx;
		margin-right: 13upx;
	}

	.borderRa {
		position: absolute;
		top: 0rpx;
		right: 30rpx;
		background: red;
		border-radius: 60rpx;
		/* padding: 2rpx 10rpx; */
		width: 35rpx;
		text-align: center;
		color: #FFFFFF;

	}
</style>