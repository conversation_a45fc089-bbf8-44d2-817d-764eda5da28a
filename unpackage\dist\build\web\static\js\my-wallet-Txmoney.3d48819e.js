(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["my-wallet-Txmoney"],{"1d0e":function(t,A,e){"use strict";e.r(A);var n=e("e8cd"),a=e("3f36");for(var i in a)["default"].indexOf(i)<0&&function(t){e.d(A,t,(function(){return a[t]}))}(i);e("8f3c");var o=e("828b"),r=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"2bb950ff",null,!1,n["a"],void 0);A["default"]=r.exports},"1f30":function(t,A,e){"use strict";e("6a54"),Object.defineProperty(A,"__esModule",{value:!0}),A.default=void 0,e("5ef2"),e("d4b5");var n={data:function(){return{title_color:1,money:0,avatar:"",Profit:0,openLists:[{image:"../static/zhifubao.png",text:"支付宝",id:1},{image:"../static/icon_weixin.png",text:"微信",id:2}],openWay:1,wallet:[],wallIndex:0,price:0,moneyNum:null,charge:0,maxMoney:0,minMoney:0,ratio:"",placeholder:"",sp:0,rechargeId:""}},onLoad:function(){var t=this;this.avatar=uni.getStorageSync("avatar");var A=navigator.userAgent.toLowerCase();-1!==A.indexOf("micromessenger")?this.$Request.get("/app/common/type/333").then((function(A){A.data&&A.data.value&&"是"==A.data.value?(t.openLists=[{image:"../static/zhifubao.png",text:"支付宝",id:1},{image:"../static/icon_weixin.png",text:"微信",id:2}],t.openWay=2):(t.openLists=[{image:"../static/zhifubao.png",text:"支付宝",id:1}],t.openWay=1)})):(this.openLists=[{image:"../static/zhifubao.png",text:"支付宝",id:1}],this.openWay=1)},onShow:function(){this.getMoney(),this.getUserInfo(),this.getMoneyList()},methods:{getMoneyList:function(){var t=this;this.$Request.getT("/app/rechargeConfig/getConfigInfoList").then((function(A){0==A.code&&(t.wallet=A.data.records,t.price=t.wallet[0].money,t.rechargeId=t.wallet[0].rechargeId)}))},getUserInfo:function(){var t=this;this.$Request.getT("/app/user/selectUserById").then((function(A){0==A.code&&(t.avatar=A.data.avatar?A.data.avatar:"../../static/image/logo.png",uni.setStorageSync("avatar",A.data.avatar))}))},cut:function(t){this.title_color=t},goNav:function(t){uni.navigateTo({url:t})},active:function(t,A){this.wallIndex=A,this.price=t.money,this.rechargeId=t.rechargeId},getMoney:function(){var t=this;this.$Request.getT("/app/userMoney/selectMyMoney").then((function(A){0==A.code&&A.data&&(console.log(A.data.money),t.money=A.data.money)}))},selectWay:function(t){this.openWay=t.id},payH5:function(){var t=this;if(1==this.openWay){var A={money:t.price,classify:5};this.$Request.postT("/app/userMoney/rechargeMoney",A).then((function(t){if(0==t.code){var A=document.createElement("div");A.innerHTML=t.data,document.body.appendChild(A),document.forms[0].submit(),uni.hideLoading()}else uni.showToast({icon:"none",title:"支付失败!"})}))}else{var e={money:t.price,classify:3};this.$Request.postT("/app/userMoney/rechargeMoney",e).then((function(A){0==A.code?(uni.hideLoading(),t.callPay(A.data)):uni.showToast({icon:"none",title:"支付失败!"})}))}},payApp:function(){var t=this;if(1==this.openWay){var A={money:t.price,classify:4};t.$Request.postT("/app/userMoney/rechargeMoney",A).then((function(A){console.log(A),t.isCheckPay(A.code,"alipay",A.data)}))}else{var e={money:t.price,classify:1};t.$Request.postT("/app/userMoney/rechargeMoney",e).then((function(A){console.log(A,"retretretretretret"),t.isCheckPay(A.code,"wxpay",JSON.stringify(A.data))}))}},payMP:function(t){var A={money:this.price,classify:2};this.$Request.postT("/app/userMoney/rechargeMoney",A).then((function(t){uni.hideLoading(),uni.requestPayment({provider:"wxpay",timeStamp:t.data.timestamp,nonceStr:t.data.noncestr,package:t.data.package,signType:t.data.signType,paySign:t.data.sign,success:function(t){console.log("success:"+JSON.stringify(t)),uni.showToast({title:"支付成功",icon:"success"}),setTimeout((function(t){uni.navigateBack(1)}),1e3)},fail:function(t){console.log("fail:"+JSON.stringify(t)),uni.showToast({title:"支付失败",icon:"none"})}})}))},pay:function(){uni.showLoading({title:"支付中..."}),this.payH5()},callPay:function(t){console.log(t),"undefined"===typeof WeixinJSBridge?document.addEventListener?document.addEventListener("WeixinJSBridgeReady",this.onBridgeReady(t),!1):document.attachEvent&&(document.attachEvent("WeixinJSBridgeReady",this.onBridgeReady(t)),document.attachEvent("onWeixinJSBridgeReady",this.onBridgeReady(t))):(console.log(1),this.onBridgeReady(t))},onBridgeReady:function(t){t.package&&(console.log(t,"++++++++"),WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:t.appid,timeStamp:t.timestamp,nonceStr:t.noncestr,package:t.package,signType:t.signType,paySign:t.sign},(function(A){console.log(A,"/*-/*-/*-"),"get_brand_wcpay_request:ok"===A.err_msg?(uni.showLoading({title:"支付成功"}),setTimeout((function(){uni.hideLoading()}),1e3)):uni.hideLoading(),WeixinJSBridge.log(t.err_msg)})))},isCheckPay:function(t,A,e){0==t?this.setPayment(A,e):(uni.hideLoading(),uni.showToast({title:"支付信息有误",icon:"none"}))},setPayment:function(t,A){console.log("*-*-*"),uni.requestPayment({provider:t,orderInfo:A,success:function(t){console.log(t),uni.hideLoading(),uni.showLoading({title:"支付成功"})},fail:function(t){console.log(t),uni.hideLoading()},complete:function(){uni.hideLoading()}})}}};A.default=n},"3f36":function(t,A,e){"use strict";e.r(A);var n=e("1f30"),a=e.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){e.d(A,t,(function(){return n[t]}))}(i);A["default"]=a.a},4568:function(t,A,e){var n=e("67b1");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=e("967d").default;a("65d19c56",n,!0,{sourceMap:!1,shadowMode:!1})},4688:function(t,A,e){"use strict";e.r(A);var n=e("89d2"),a=e.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){e.d(A,t,(function(){return n[t]}))}(i);A["default"]=a.a},5371:function(t,A,e){var n=e("c86c");A=n(!1),A.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.u-avatar[data-v-21bdd9ea]{display:inline-flex;align-items:center;justify-content:center;font-size:%?28?%;color:#606266;border-radius:10px;position:relative}.u-avatar__img[data-v-21bdd9ea]{width:100%;height:100%}.u-avatar__sex[data-v-21bdd9ea]{position:absolute;width:%?32?%;color:#fff;height:%?32?%;display:flex;flex-direction:row;justify-content:center;align-items:center;border-radius:%?100?%;top:5%;z-index:1;right:-7%;border:1px #fff solid}.u-avatar__sex--man[data-v-21bdd9ea]{background-color:#2979ff}.u-avatar__sex--woman[data-v-21bdd9ea]{background-color:#fa3534}.u-avatar__sex--none[data-v-21bdd9ea]{background-color:#f90}.u-avatar__level[data-v-21bdd9ea]{position:absolute;width:%?32?%;color:#fff;height:%?32?%;display:flex;flex-direction:row;justify-content:center;align-items:center;border-radius:%?100?%;bottom:5%;z-index:1;right:-7%;border:1px #fff solid;background-color:#f90}',""]),t.exports=A},"5c16":function(t,A,e){"use strict";var n=e("d487"),a=e.n(n);a.a},"67b1":function(t,A,e){var n=e("c86c");A=n(!1),A.push([t.i,"uni-page-body[data-v-2bb950ff]{background-color:#f7f7f7}body.?%PAGE?%[data-v-2bb950ff]{background-color:#f7f7f7}.bgCol2[data-v-2bb950ff]{color:#5074ff}.bg[data-v-2bb950ff]{background-color:#fff}.active[data-v-2bb950ff]{border:1px solid #5074ff!important;color:#5074ff!important;background:#fff!important}.title_btn[data-v-2bb950ff]{height:%?78?%;line-height:%?78?%\n\t/* background: #f7f7f7; */}.btn[data-v-2bb950ff]{width:100%;height:%?88?%;background:linear-gradient(114deg,#ff6f9c,#ff98bd);border-radius:%?44?%;text-align:center;line-height:%?88?%;margin-top:%?40?%;font-size:%?28?%;color:#fff}",""]),t.exports=A},"822e":function(t,A,e){"use strict";e.r(A);var n=e("c436"),a=e("4688");for(var i in a)["default"].indexOf(i)<0&&function(t){e.d(A,t,(function(){return a[t]}))}(i);e("5c16");var o=e("828b"),r=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"21bdd9ea",null,!1,n["a"],void 0);A["default"]=r.exports},"89d2":function(t,A,e){"use strict";e("6a54"),Object.defineProperty(A,"__esModule",{value:!0}),A.default=void 0,e("64aa");var n="data:image/jpg;base64,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",a={name:"u-avatar",props:{bgColor:{type:String,default:"transparent"},src:{type:String,default:""},size:{type:[String,Number],default:"default"},mode:{type:String,default:"circle"},text:{type:String,default:""},imgMode:{type:String,default:"aspectFill"},index:{type:[String,Number],default:""},sexIcon:{type:String,default:"man"},levelIcon:{type:String,default:"level"},levelBgColor:{type:String,default:""},sexBgColor:{type:String,default:""},showSex:{type:Boolean,default:!1},showLevel:{type:Boolean,default:!1}},data:function(){return{error:!1,avatar:this.src?this.src:n}},watch:{src:function(t){t?(this.avatar=t,this.error=!1):(this.avatar=n,this.error=!0)}},computed:{wrapStyle:function(){var t={};return t.height="large"==this.size?"120rpx":"default"==this.size?"90rpx":"mini"==this.size?"70rpx":this.size+"rpx",t.width=t.height,t.flex="0 0 ".concat(t.height),t.backgroundColor=this.bgColor,t.borderRadius="circle"==this.mode?"500px":"5px",this.text&&(t.padding="0 6rpx"),t},imgStyle:function(){var t={};return t.borderRadius="circle"==this.mode?"500px":"5px",t},uText:function(){return String(this.text)[0]},uSexStyle:function(){var t={};return this.sexBgColor&&(t.backgroundColor=this.sexBgColor),t},uLevelStyle:function(){var t={};return this.levelBgColor&&(t.backgroundColor=this.levelBgColor),t}},methods:{loadError:function(){this.error=!0,this.avatar=n},click:function(){this.$emit("click",this.index)}}};A.default=a},"8f3c":function(t,A,e){"use strict";var n=e("4568"),a=e.n(n);a.a},c436:function(t,A,e){"use strict";e.d(A,"b",(function(){return a})),e.d(A,"c",(function(){return i})),e.d(A,"a",(function(){return n}));var n={uIcon:e("3688").default},a=function(){var t=this,A=t.$createElement,e=t._self._c||A;return e("v-uni-view",{staticClass:"u-avatar",style:[t.wrapStyle],on:{click:function(A){arguments[0]=A=t.$handleEvent(A),t.click.apply(void 0,arguments)}}},[!t.uText&&t.avatar?e("v-uni-image",{staticClass:"u-avatar__img",style:[t.imgStyle],attrs:{src:t.avatar,mode:t.imgMode},on:{error:function(A){arguments[0]=A=t.$handleEvent(A),t.loadError.apply(void 0,arguments)}}}):t.uText?e("v-uni-text",{staticClass:"u-line-1",style:{fontSize:"38rpx"}},[t._v(t._s(t.uText))]):t._t("default"),t.showSex?e("v-uni-view",{staticClass:"u-avatar__sex",class:["u-avatar__sex--"+t.sexIcon],style:[t.uSexStyle]},[e("u-icon",{attrs:{name:t.sexIcon,size:"20"}})],1):t._e(),t.showLevel?e("v-uni-view",{staticClass:"u-avatar__level",style:[t.uLevelStyle]},[e("u-icon",{attrs:{name:t.levelIcon,size:"20"}})],1):t._e()],2)},i=[]},d487:function(t,A,e){var n=e("5371");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=e("967d").default;a("61deeba9",n,!0,{sourceMap:!1,shadowMode:!1})},e8cd:function(t,A,e){"use strict";e.d(A,"b",(function(){return a})),e.d(A,"c",(function(){return i})),e.d(A,"a",(function(){return n}));var n={uAvatar:e("822e").default},a=function(){var t=this,A=t.$createElement,e=t._self._c||A;return e("v-uni-view",[e("v-uni-view",{staticClass:"padding"},[e("v-uni-view",{staticClass:"flex  justify-between radius bg "},[e("v-uni-view",{staticClass:" u-flex u-p-l-30 u-p-t-30 u-p-b-30"},[e("v-uni-view",{staticClass:"u-m-r-20"},[e("u-avatar",{attrs:{src:t.avatar,size:"80"}})],1),e("v-uni-view",{staticClass:"u-flex-1 "},[e("v-uni-view",{staticClass:"u-font-16 text-bold"},[t._v(t._s(t.money)+"元")]),e("v-uni-view",{staticClass:"u-font-14 u-m-t-10"},[t._v("可用于平台消费")])],1)],1),e("v-uni-view",{staticClass:"margin-right margin-top-sm",on:{click:function(A){arguments[0]=A=t.$handleEvent(A),t.goNav("/my/wallet/mymoneydetail")}}},[t._v("钱包明细")])],1),e("v-uni-view",{staticClass:"margin-top radius bg flex justify-between flex-wrap padding-lr padding-bottom"},t._l(t.wallet,(function(A,n){return e("v-uni-view",{key:n,staticClass:"flex justify-between align-center padding-sm radius margin-top",class:t.wallIndex==n?"active":"",staticStyle:{color:"#1E1F31","background-color":"#F7F7F7",width:"46%",border:"1px solid #F7F7F7"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.active(A,n)}}},[e("v-uni-view",[t._v(t._s(A.money)+"元")])],1)})),1),e("v-uni-view",{staticClass:"margin-top",staticStyle:{width:"100%","padding-left":"40rpx"}},[t._v("充值后仅可用于消费，不支持退款。")]),e("v-uni-view",{staticClass:"bg margin-top padding-lr radius"},[e("v-uni-view",t._l(t.openLists,(function(A,n){return e("v-uni-view",{key:n,staticStyle:{display:"flex",height:"100upx","align-items":"center",padding:"20upx 0"}},[e("v-uni-image",{staticStyle:{width:"55upx",height:"55upx","border-radius":"50upx"},attrs:{src:A.image}}),e("v-uni-view",{staticStyle:{"font-size":"30upx","margin-left":"20upx",width:"70%"}},[t._v(t._s(A.text))]),e("v-uni-radio-group",{staticStyle:{"margin-left":"20upx"},attrs:{name:"openWay"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectWay(A)}}},[e("v-uni-label",{staticClass:"tui-radio"},[e("v-uni-radio",{attrs:{color:"#5074FF",checked:t.openWay===A.id}})],1)],1)],1)})),1)],1),e("v-uni-view",{staticClass:"btn",on:{click:function(A){arguments[0]=A=t.$handleEvent(A),t.pay.apply(void 0,arguments)}}},[t._v("确认支付")])],1),e("v-uni-view",{staticClass:"flex justify-around margin-top text-white"})],1)},i=[]}}]);