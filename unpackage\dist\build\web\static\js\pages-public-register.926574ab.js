(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-public-register"],{2323:function(e,t,n){"use strict";n.r(t);var i=n("eaf0"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);t["default"]=a.a},6983:function(e,t,n){"use strict";n.r(t);var i=n("efae"),a=n("2323");for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);n("b70a");var s=n("828b"),u=Object(s["a"])(a["default"],i["b"],i["c"],!1,null,"87d9ee98",null,!1,i["a"],void 0);t["default"]=u.exports},"6f30":function(e,t,n){var i=n("c86c");t=i(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */uni-page-body[data-v-87d9ee98]{height:100%;background:#fff!important}body.?%PAGE?%[data-v-87d9ee98]{background:#fff!important}.footer[data-v-87d9ee98]{padding-left:%?140?%;margin-top:%?32?%;text-align:center;display:flex}.send-msg[data-v-87d9ee98]{border-radius:30px;color:#fff;height:30px;font-size:10px;line-height:30px;background:linear-gradient(114deg,#ff6f9c,#ff98bd)}.container[data-v-87d9ee98]{padding-top:%?32?%;position:relative;width:100%;height:100%;overflow:hidden;background:#fff!important}.wrapper[data-v-87d9ee98]{position:relative;z-index:90;background:#fff;padding-bottom:20px}.input-content[data-v-87d9ee98]{padding:%?32?% %?80?%}.confirm-btn[data-v-87d9ee98]{width:%?600?%;height:%?80?%;line-height:%?80?%;border-radius:%?60?%;margin-top:%?32?%;background:linear-gradient(114deg,#ff6f9c,#ff98bd);color:#fff;font-size:%?32?%}.confirm-btn[data-v-87d9ee98]:after{border-radius:60px}',""]),e.exports=t},7169:function(e,t,n){var i=n("6f30");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var a=n("967d").default;a("7dda24cc",i,!0,{sourceMap:!1,shadowMode:!1})},b70a:function(e,t,n){"use strict";var i=n("7169"),a=n.n(i);a.a},eaf0:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={data:function(){return{meShowModel:!1,meShowCancel:!0,meTitle:"提示",meContent:"",meConfirmText:"确认",meCancelText:"取消",meIndex:"",userName:"",showAgree:!1,code:"",phone:"",password:"",required:"否",sending:!1,sendTime:"获取验证码",count:60,relation:"",state:"",invitation:"",platform:""}},onLoad:function(){var e=this;this.platform="H5";var t=this.$queue.getData("inviterCode")?this.$queue.getData("inviterCode"):"";t?this.invitation=t:this.$Request.getT("/app/common/type/3").then((function(t){0==t.code&&t.data&&(e.required=t.data.value,"是"==e.required?e.$Request.getT("/app/common/type/88").then((function(t){0==t.code&&t.data&&(e.invitation=t.data.value)})):e.invitation="")}))},methods:{meHandleBtn:function(){this.meIndex},meHandleClose:function(){this.meIndex},newMa:function(){var e=this;this.$Request.getT("/app/common/type/119").then((function(t){0==t.code&&(e.amount=t.data.value)}))},isShowAgree:function(){this.showAgree=!this.showAgree},sendMsg:function(){var e=this,t=this.phone;t?11!==t.length?this.$queue.showToast("请输入正确的手机号"):(this.$queue.showLoading("正在发送验证码..."),this.$Request.getT("/app/Login/sendMsg/"+t+"/1").then((function(t){0===t.code?(e.sending=!0,e.$queue.showToast("验证码发送成功请注意查收"),e.countDown(),uni.hideLoading()):(uni.hideLoading(),e.meShowModel=!0,e.meTitle="短信发送失败",e.meContent=t.msg?t.msg:"请一分钟后再获取验证码",e.meIndex="m0",e.meShowCancel=!1)}))):this.$queue.showToast("请输入手机号")},countDown:function(){var e=this.count;1===e?(this.count=60,this.sending=!1,this.sendTime="获取验证码"):(this.count=e-1,this.sending=!0,this.sendTime=e-1+"秒后重新获取",setTimeout(this.countDown.bind(this),1e3))},inputChange:function(e){var t=e.currentTarget.dataset.key;this[t]=e.detail.value},navBack:function(){uni.navigateBack()},navTo:function(e){uni.navigateTo({url:e})},toLogin:function(){var e=this,t=this.userName,n=this.phone,i=this.password,a=this.code,o=this.showAgree,s=this.invitation;t?n?a?i?i.length<6?this.$queue.showToast("密码位数必须大于六位"):o?s||"是"!=this.required?s||"否"!=this.required?(this.logining=!0,this.$queue.showLoading("注册中..."),this.$Request.post("/app/Login/registApp?msg=".concat(a),{userName:t,password:i,phone:n,openId:this.$queue.getData("openid")?this.$queue.getData("openid"):"",invitation:this.invitation,platform:this.platform}).then((function(t){0===t.code?(e.$queue.setData("token",t.token),e.$queue.setData("userId",t.user.userId),e.$queue.setData("userName",t.user.userName),e.$queue.setData("phone",t.user.phone),e.$queue.setData("avatar",t.user.avatar?t.user.avatar:"../../static/logo.png"),e.$queue.setData("invitationCode",t.user.invitationCode),e.$queue.setData("inviterCode",t.user.inviterCode),e.getUserInfo(),setTimeout((function(){uni.switchTab({url:"/pages/index/index"})}),1e3)):(e.meShowModel=!0,e.meTitle="注册失败",e.meContent=t.msg,e.meIndex="m0",e.meShowCancel=!1),uni.hideLoading()}))):(this.invitation="",this.logining=!0,this.$queue.showLoading("注册中..."),this.$Request.post("/app/Login/registApp",{userName:t,password:i,phone:n,openId:this.$queue.getData("openid")?this.$queue.getData("openid"):"",invitation:this.invitation,platform:this.platform,msg:a}).then((function(t){0===t.code?(e.$queue.showToast("注册成功"),e.$queue.setData("token",t.token),e.$queue.setData("userId",t.user.userId),e.$queue.setData("userName",t.user.userName),e.$queue.setData("phone",t.user.phone),e.$queue.setData("avatar",t.user.avatar?t.user.avatar:"../../static/logo.png"),e.getUserInfo(),setTimeout((function(){uni.switchTab({url:"/pages/index/index"})}),1e3)):(uni.hideLoading(),e.meShowModel=!0,e.meTitle="注册失败",e.meContent=t.msg,e.meIndex="m0",e.meShowCancel=!1)}))):this.$queue.showToast("请填写邀请码"):this.$queue.showToast("请先同意《协议》"):this.$queue.showToast("请设置密码"):this.$queue.showToast("请输入验证码"):this.$queue.showToast("请输入手机号"):this.$queue.showToast("请输入用户名")},getUserInfo:function(){var e=this;this.$Request.get("/app/user/selectUserById").then((function(t){0==t.code?(uni.hideLoading(),e.$queue.setData("avatar",t.data.avatar?t.data.avatar:"../../static/logo.png"),e.$queue.setData("userId",t.data.userId),e.$queue.setData("userName",t.data.userName),e.$queue.setData("phone",t.data.phone),e.$queue.setData("invitationCode",t.user.invitationCode),e.$queue.setData("inviterCode",t.user.inviterCode),e.userName=t.data.userName,e.invitationCode=t.data.invitationCode,uni.setStorageSync("invitationCode",t.data.invitationCode),setTimeout((function(){uni.switchTab({url:"/pages/index/index"})}),1e3)):(e.meShowModel=!0,e.meTitle="登录失败",e.meContent=t.msg,e.meIndex="m0",e.meShowCancel=!1,e.$queue.logout())}))}}};t.default=i},efae:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){return i}));var i={uModal:n("7e01").default},a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"container"},[n("v-uni-view",{staticClass:"wrapper"},[n("v-uni-view",{staticClass:"input-content"},[n("v-uni-view",{staticClass:"cu-form-group",staticStyle:{border:"2upx solid whitesmoke","margin-bottom":"20px","border-radius":"30px"}},[n("v-uni-view",{staticClass:"title text-black"},[e._v("用户名")]),n("v-uni-input",{attrs:{value:e.userName,placeholder:"请输入用户名","data-key":"userName"},on:{input:function(t){arguments[0]=t=e.$handleEvent(t),e.inputChange.apply(void 0,arguments)}}})],1),n("v-uni-view",{staticClass:"cu-form-group",staticStyle:{border:"2upx solid whitesmoke","margin-bottom":"20px","border-radius":"30px"}},[n("v-uni-view",{staticClass:"title text-black"},[e._v("手机号")]),n("v-uni-input",{attrs:{type:"number",value:e.phone,placeholder:"请输入手机号",maxlength:"11","data-key":"phone"},on:{input:function(t){arguments[0]=t=e.$handleEvent(t),e.inputChange.apply(void 0,arguments)}}})],1),n("v-uni-view",{staticClass:"cu-form-group padding-right-xs",staticStyle:{border:"2upx solid whitesmoke","margin-bottom":"20px","border-radius":"30px"}},[n("v-uni-text",{staticClass:"title text-black"},[e._v("验证码")]),n("v-uni-input",{attrs:{type:"number",value:e.code,placeholder:"请输入验证码",maxlength:"6","data-key":"code"},on:{input:function(t){arguments[0]=t=e.$handleEvent(t),e.inputChange.apply(void 0,arguments)},confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.toLogin.apply(void 0,arguments)}}}),n("v-uni-button",{staticClass:"send-msg",attrs:{disabled:e.sending},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.sendMsg.apply(void 0,arguments)}}},[e._v(e._s(e.sendTime))])],1),n("v-uni-view",{staticClass:"cu-form-group",staticStyle:{border:"2upx solid whitesmoke","margin-bottom":"20px","border-radius":"30px"}},[n("v-uni-text",{staticClass:"title text-black"},[e._v("设置密码")]),n("v-uni-input",{attrs:{type:"password",value:e.password,placeholder:"请设置密码","placeholder-class":"input-empty",maxlength:"20",minlength:"6","data-key":"password"},on:{input:function(t){arguments[0]=t=e.$handleEvent(t),e.inputChange.apply(void 0,arguments)},confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.toLogin.apply(void 0,arguments)}}})],1),"是"==e.required?n("v-uni-view",{staticClass:"cu-form-group",staticStyle:{border:"2upx solid whitesmoke","margin-bottom":"20px","border-radius":"30px"}},[n("v-uni-text",{staticClass:"title text-black"},[e._v("邀请码")]),n("v-uni-input",{attrs:{type:"",maxlength:"6",value:e.invitation,placeholder:"请填写邀请码(必填)","data-key":"invitation"},on:{input:function(t){arguments[0]=t=e.$handleEvent(t),e.inputChange.apply(void 0,arguments)},confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.toLogin.apply(void 0,arguments)}}})],1):e._e(),"否"==e.required?n("v-uni-view",{staticClass:"cu-form-group",staticStyle:{border:"2upx solid whitesmoke","margin-bottom":"20px","border-radius":"30px"}},[n("v-uni-text",{staticClass:"title text-black"},[e._v("邀请码")]),n("v-uni-input",{attrs:{type:"",maxlength:"6",value:e.invitation,placeholder:"请填写邀请码(选填)","data-key":"invitation"},on:{input:function(t){arguments[0]=t=e.$handleEvent(t),e.inputChange.apply(void 0,arguments)},confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.toLogin.apply(void 0,arguments)}}})],1):e._e()],1),n("v-uni-button",{staticClass:"confirm-btn",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toLogin.apply(void 0,arguments)}}},[e._v("立即注册")]),n("v-uni-view",{staticClass:"footer"},[n("v-uni-text",{staticClass:"cuIcon",class:e.showAgree?"cuIcon-radiobox":"cuIcon-round",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.isShowAgree.apply(void 0,arguments)}}},[e._v("同意")]),n("v-uni-navigator",{attrs:{url:"/my/setting/mimi","open-type":"navigate"}},[e._v("《隐私政策》")]),e._v("和"),n("v-uni-navigator",{attrs:{url:"/my/setting/xieyi","open-type":"navigate"}},[e._v("《用户服务协议》")])],1)],1),n("u-modal",{attrs:{content:e.meContent,title:e.meTitle,"show-cancel-button":e.meShowCancel,"confirm-text":e.meConfirmText,"cancel-text":e.meCancelText},on:{cancel:function(t){arguments[0]=t=e.$handleEvent(t),e.meHandleClose.apply(void 0,arguments)},confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.meHandleBtn.apply(void 0,arguments)}},model:{value:e.meShowModel,callback:function(t){e.meShowModel=t},expression:"meShowModel"}})],1)},o=[]}}]);