<template>
	<view class="container">
		<view class="store-list">
			<!-- 门店项 -->
			<view class="store-item" v-for="(store, index) in storeList" :key="index">
				<view class="store-header">
					<image class="store-icon" :src="store.icon" mode="aspectFit"></image>
					<view class="store-info">
						<view class="store-name">{{ store.name }}</view>
						<view class="store-address">{{ store.address }}</view>
					</view>
				</view>

				<view class="store-actions">
					<view class="action-btn" @click="handleAction('offline', store)">
						<view class="action-text">门店下线</view>
					</view>
					<view class="action-btn" @click="handleAction('orders', store)">
						<view class="action-text">门店订单</view>
					</view>
					<view class="action-btn" @click="handleAction('revenue', store)">
						<view class="action-text">门店收益</view>
					</view>
					<view class="action-btn" @click="handleAction('staff', store)">
						<view class="action-text">门店员工</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			storeList: [
				{
					id: 1,
					name: '幸福汇西丽店',
					address: '深圳市南山区西丽塘朗城A座3楼',
					icon: 'https://photo.zastatic.com/images/common-cms/it/20250527/1748338545852_26938_t.png'
				},
				{
					id: 2,
					name: '幸福汇西丽店',
					address: '深圳市南山区西丽塘朗城A座3楼',
					icon: 'https://photo.zastatic.com/images/common-cms/it/20250527/1748338545852_26938_t.png'
				},
				{
					id: 3,
					name: '幸福汇西丽店',
					address: '深圳市南山区西丽塘朗城A座3楼',
					icon: 'https://photo.zastatic.com/images/common-cms/it/20250527/1748338545852_26938_t.png'
				},
				{
					id: 4,
					name: '幸福汇西丽店',
					address: '深圳市南山区西丽塘朗城A座3楼',
					icon: 'https://photo.zastatic.com/images/common-cms/it/20250527/1748338545852_26938_t.png'
				}
			]
		}
	},
	onLoad() {
		this.loadStoreList()
	},
	methods: {
		loadStoreList() {
			// 这里可以调用API获取门店列表数据
			// this.$Request.get("/app/store/list").then(res => {
			//     if (res.code == 0) {
			//         this.storeList = res.data
			//     }
			// });
		},
		handleAction(action, store) {
			console.log('操作类型:', action, '门店信息:', store)

			switch(action) {
				case 'offline':
					this.handleOffline(store)
					break
				case 'orders':
					this.handleOrders(store)
					break
				case 'revenue':
					this.handleRevenue(store)
					break
				case 'staff':
					this.handleStaff(store)
					break
			}
		},
		handleOffline(store) {
			uni.showModal({
				title: '提示',
				content: `确定要将${store.name}下线吗？`,
				success: (res) => {
					if (res.confirm) {
						// 调用下线接口
						uni.showToast({
							title: '门店已下线',
							icon: 'success'
						})
					}
				}
			})
		},
		handleOrders(store) {
			// 跳转到门店订单页面
			uni.navigateTo({
				url: `/my/store/orders?storeId=${store.id}&storeName=${store.name}`
			})
		},
		handleRevenue(store) {
			// 跳转到门店收益页面
			uni.navigateTo({
				url: `/my/store/revenue?storeId=${store.id}&storeName=${store.name}`
			})
		},
		handleStaff(store) {
			// 跳转到门店员工页面
			uni.navigateTo({
				url: `/my/store/staff?storeId=${store.id}&storeName=${store.name}`
			})
		}
	}
}
</script>

<style scoped>
page {
	background: #FFFFFF;
}

.container {
	padding: 20rpx;
	background: #f5f5f5;
	min-height: 100vh;
}

.store-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.store-item {
	background: #FFFFFF;
	border-radius: 12rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.store-header {
	display: flex;
	/* align-items: center; */
	margin-bottom: 30rpx;
}

.store-icon {
	width: 33rpx;
	height: 33rpx;
	border-radius: 8rpx;
	margin-right: 20rpx;
	background: #f0f0f0;
	margin-top: 8rpx;
}

.store-info {
	flex: 1;
}

.store-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 8rpx;
}

.store-address {
	font-size: 28rpx;
	color: #666666;
	line-height: 1.4;
}

.store-actions {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.action-row {
	display: flex;
	gap: 20rpx;
}

.action-btn {
	flex: 1;
	height: 80rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #FFFFFF;
	transition: all 0.3s ease;
}

.action-btn:active {
	background: #f5f5f5;
	transform: scale(0.98);
}

.action-text {
	font-size: 28rpx;
	color: #333333;
}
</style>
