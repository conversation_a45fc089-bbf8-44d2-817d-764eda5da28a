(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-my-invitationUser"],{"1bec":function(t,e,n){var i=n("45e5");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("967d").default;a("1a311e77",i,!0,{sourceMap:!1,shadowMode:!1})},"326b":function(t,e,n){"use strict";var i=n("1bec"),a=n.n(i);a.a},"40cd":function(t,e,n){var i=n("bdbb").default;n("bf0f"),n("d4b5"),n("bd06");var a=console.log,o={log:function(t){a(t)},showLoading:function(t,e){uni.showLoading({title:t,mask:e||!1})},hideLoading:function(){uni.hideLoading()},showToast:function(t,e){uni.showToast({title:t,icon:e||"none"})},getPosterUrl:function(t){var e=t.backgroundImage,n=t.type;t.formData;return new Promise((function(t,i){var a;if(e)a=e;else switch(n){case 1:a="";break;default:a="/static/logo.png";break}a?t(a):i("背景图片路径不存在")}))},shareTypeListSheetArray:{array:[0,1,2,3,4,5]},isArray:function(t){return"[object Array]"===Object.prototype.toString.call(t)},isObject:function(t){return"[object Object]"===Object.prototype.toString.call(t)},isPromise:function(t){return!!t&&("object"===i(t)||"function"===typeof t)&&"function"===typeof t.then},isNull:function(t){return null===t},isUndefined:function(t){return void 0===t},isUndef:function(t){return void 0===t},isNotNull_string:function(t){return null!==t&&void 0!==t&&""!==t},isFn:function(t){return t&&"function"===typeof t},getStorage:function(t,e,n){uni.getStorage({key:t,success:function(t){t.data&&""!=t.data?e&&e(t.data):n&&n()},fail:function(){n&&n()}})},setStorage:function(t,e){a("设置缓存"),a("key："+t),a("data："+JSON.stringify(e)),uni.setStorage({key:t,data:e})},setStorageSync:function(t,e){uni.setStorageSync(t,e)},getStorageSync:function(t){return uni.getStorageSync(t)},clearStorageSync:function(){uni.clearStorageSync()},removeStorageSync:function(t){uni.removeStorageSync(t)},getImageInfo:function(t,e,n){t=r(t),uni.getImageInfo({src:t,success:function(t){e&&"function"==typeof e&&e(t)},fail:function(t){n&&"function"==typeof n&&n(t)}})},downloadFile:function(t,e){t=r(t),uni.downloadFile({url:t,success:function(t){e&&"function"==typeof e&&e(t)}})},downloadFile_PromiseFc:function(t){return new Promise((function(e,n){"http"!==t.substring(0,4)?e(t):(t=r(t),a("url:"+t),uni.downloadFile({url:t,success:function(t){t&&t.tempFilePath?e(t.tempFilePath):n("not find tempFilePath")},fail:function(t){n(t)}}))}))},saveFile:function(t){uni.saveFile({tempFilePath:t,success:function(t){a("保存成功:"+JSON.stringify(t))}})},downLoadAndSaveFile_PromiseFc:function(t){return new Promise((function(e,n){a("准备下载并保存图片:"+t),"http"===t.substring(0,4)?(t=r(t),uni.downloadFile({url:t,success:function(t){a("下载背景图成功："+JSON.stringify(t)),t&&t.tempFilePath?e(t.tempFilePath):n("not find tempFilePath")},fail:function(t){n(t)}})):e(t)}))},checkFile_PromiseFc:function(t){return new Promise((function(e,n){uni.getSavedFileList({success:function(n){var i=n.fileList,a=i.findIndex((function(e){return e.filePath===t}));e(a)},fail:function(t){n(t)}})}))},removeSavedFile:function(t){uni.getSavedFileList({success:function(e){var n=e.fileList,i=n.findIndex((function(e){return e.filePath===t}));i>=0&&uni.removeSavedFile({filePath:t})}})},fileNameInPath:function(t){var e=t.split("/");return e[e.length-1]},getImageInfo_PromiseFc:function(t){return new Promise((function(e,n){a("准备获取图片信息:"+t),t=r(t),uni.getImageInfo({src:t,success:function(t){a("获取图片信息成功:"+JSON.stringify(t)),e(t)},fail:function(t){a("获取图片信息失败:"+JSON.stringify(t)),n(t)}})}))},previewImage:function(t){"string"==typeof t&&(t=[t]),uni.previewImage({urls:t})},actionSheet:function(t,e){for(var n=[],i=0;i<t.array.length;i++)switch(t.array[i]){case"sinaweibo":n[i]="新浪微博";break;case"qq":n[i]="QQ";break;case"weixin":n[i]="微信";break;case"WXSceneSession":n[i]="微信好友";break;case"WXSenceTimeline":n[i]="微信朋友圈";break;case"WXSceneFavorite":n[i]="微信收藏";break;case 0:n[i]="图文链接";break;case 1:n[i]="纯文字";break;case 2:n[i]="纯图片";break;case 3:n[i]="音乐";break;case 4:n[i]="视频";break;case 5:n[i]="小程序";break;default:break}this.showActionSheet(n,e)},showActionSheet:function(t,e){uni.showActionSheet({itemList:t,success:function(t){e&&"function"==typeof e&&e(t.tapIndex)}})},getProvider:function(t,e,n){var i=this;uni.getProvider({service:t,success:function(a){if(n){var o={};o.array=a.provider,i.actionSheet(o,(function(t){e&&"function"==typeof e&&e(a.provider[t])}))}else if("payment"==t){for(var r=a.provider,s=[],u=0;u<r.length;u++)"wxpay"==r[u]?s[u]={name:"微信支付",value:r[u],img:"/static/image/wei.png"}:"alipay"==r[u]&&(s[u]={name:"支付宝支付",value:r[u],img:"/static/image/ali.png"});e&&"function"==typeof e&&e(s)}else e&&"function"==typeof e&&e(a)}})}};function r(t){return t}t.exports=o},"45e5":function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,".modal[data-v-16af4d4a]{position:fixed;top:0;right:0;bottom:0;left:0;z-index:1110;opacity:0;outline:0;text-align:center;-ms-transform:scale(1.185);-webkit-transform:scale(1.185);transform:scale(1.185);-webkit-backface-visibility:hidden;backface-visibility:hidden;-webkit-perspective:%?2000?%;perspective:%?2000?%;background:rgba(0,0,0,.6);transition:all .3s ease-in-out 0s;pointer-events:none}.modal.show[data-v-16af4d4a]{opacity:1;transition-duration:.3s;-ms-transform:scale(1);-webkit-transform:scale(1);transform:scale(1);overflow-x:hidden;overflow-y:auto;pointer-events:auto}uni-page-body[data-v-16af4d4a]{background:#f5f5f5}body.?%PAGE?%[data-v-16af4d4a]{background:#f5f5f5}.view1[data-v-16af4d4a]{border-radius:%?15?%;background-size:100%;margin:%?20?% %?20?% 0 %?20?%}.hideCanvasView[data-v-16af4d4a]{position:relative}.hideCanvas[data-v-16af4d4a]{position:fixed;top:%?-99999?%;left:%?-99999?%;z-index:-99999}.flex_row_c_c[data-v-16af4d4a]{display:flex;flex-direction:row;justify-content:center;align-items:center}.modalView[data-v-16af4d4a]{width:100%;height:100%;position:fixed;top:0;left:0;right:0;bottom:0;opacity:0;outline:0;-webkit-transform:scale(1.2);transform:scale(1.2);-webkit-perspective:%?2500?%;perspective:%?2500?%;background:rgba(0,0,0,.6);transition:all .3s ease-in-out;pointer-events:none;-webkit-backface-visibility:hidden;backface-visibility:hidden;z-index:999}.modalView.show[data-v-16af4d4a]{opacity:1;-webkit-transform:scale(1);transform:scale(1);pointer-events:auto}.flex_column[data-v-16af4d4a]{display:flex;flex-direction:column}.backgroundColor-white[data-v-16af4d4a]{background-color:#fff}.border_radius_10px[data-v-16af4d4a]{border-radius:10px}.padding1vh[data-v-16af4d4a]{padding:1vh}.posterImage[data-v-16af4d4a]{width:60vw}.flex_row[data-v-16af4d4a]{display:flex;flex-direction:row}.marginTop2vh[data-v-16af4d4a]{margin-top:2vh}.poster_canvas[data-v-16af4d4a]{width:%?750?%;height:%?1334?%;position:fixed;top:%?-10000?%;left:0}",""]),t.exports=e},6416:function(t,e){},7825:function(t,e,n){"use strict";n.r(e);var i=n("800e"),a=n("eb48");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("326b");var r=n("828b"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"16af4d4a",null,!1,i["a"],void 0);e["default"]=s.exports},"7c95":function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("bf0f");var a=i(n("2634")),o=i(n("2fdc")),r=i(n("1497")),s=i(n("6416")),u=i(n("b929")),l=i(n("f022")),c=i(n("40cd")),d=i(n("b741")),f={components:{tkiQrcode:r.default,wmPoster:u.default},data:function(){return{meShowModel:!1,meShowCancel:!0,meTitle:"提示",meContent:"",meConfirmText:"确认",meCancelText:"取消",meIndex:"",erweimapath:"",poster:{},qrShow:!1,haibaoImg:null,haibaoShow:!1,modalName:"",canvasId:"default_PosterCanvasId",imageUrl:"",userImageUrl:"",isShowWxAPPShare:"否",nickName:"",invitationCode:"",backgroundImage:"",tuiguang:"快来和我一起打游戏吧!",tuiguang1:"快来和我一起打游戏吧!",url:""}},onLoad:function(){var t=this;this.getBackImageList();var e=this.$queue.getData("avatar");this.userImageUrl=e&&"undefined"!==e?e:"/static/logo.png",this.$Request.getT("/app/common/type/116").then((function(e){0===e.code&&e.data&&e.data.value&&(t.tuiguang=e.data.value)})),this.$Request.getT("/app/common/type/101").then((function(e){0===e.code&&e.data&&e.data.value&&(t.tuiguang1=e.data.value)})),this.$Request.getT("/app/common/type/136").then((function(e){0===e.code&&e.data&&e.data.value&&(t.isShowWxAPPShare=e.data.value)})),this.invitationCode=this.$queue.getData("invitationCode"),this.$Request.getT("/app/common/type/141").then((function(e){0===e.code&&(e.data&&e.data.value&&"是"==e.data.value?t.$Request.getT("/app/common/type/25").then((function(e){0===e.code&&e.data&&e.data.value&&(t.url=e.data.value)})):t.url=t.$queue.publicYuMing()+"/?invitation="+t.invitationCode)}));var n=this.$queue.getData("userName");this.nickName=n&&"undefined"!==n?n:"游客"},onShareAppMessage:function(t){return{path:"/pages/index/index?invitation="+this.invitationCode,title:this.tuiguang,imageUrl:this.bgImg}},onShareTimeline:function(t){return{path:"/pages/index/index?invitation="+this.invitationCode,title:this.tuiguang,imageUrl:this.bgImg}},methods:{posterSuccess:function(t){this.haibaoImg=t,this.modalName="Image"},showModal:function(){this.haibaoImg?this.modalName="Image":(this.haibaoShow=!0,this.$queue.showLoading("海报生成中..."))},hideModal:function(){this.modalName=null},qrR:function(t){this.erweimapath=t},getBackImageList:function(){var t=this;this.$Request.getT("/app/banner/selectBannerList?state=-1&classify=5").then((function(e){0===e.code&&(t.backgroundImage=e.data[0].imageUrl)})),this.make()},make:function(){l.default.make({canvasId:"default_PosterCanvasId",componentInstance:this,text:this.url,size:68,margin:4,backgroundColor:"#ffffff",foregroundColor:"#000000",fileType:"jpg",correctLevel:l.default.errorCorrectLevel.H,success:function(t){console.log(t)}})},shareWeiXin:function(){var t=this;this.$Request.getT("/app/common/type/103").then((function(e){if(0===e.code&&e.data&&e.data.value){var n=t.invitationCode,i={shareUrl:t.url,shareTitle:e.data.value,shareContent:"邀请码："+n+"，"+e.data.value,shareImg:t.$queue.publicYuMing()+"/logo.png",type:0};(0,s.default)(i,(function(t){console.log("分享成功回调",t)}))}}))},share:function(){this.sharurl()},meHandleBtn:function(){var t=this;if("m1"==this.meIndex){var e=this.invitationCode;uni.setClipboardData({data:t.tuiguang1+e+"\n"+t.url,success:function(){console.log("success"),t.$queue.showToast("文案复制成功")}})}"m2"==this.meIndex&&(uni.hideLoading(),uni.openSetting())},meHandleClose:function(){this.meIndex},sharAPPUrl:function(){var t=this.invitationCode;this.meShowModel=!0,this.meTitle="文案推广",this.meContent=this.tuiguang1+t+"\n"+this.url,this.meIndex="m1",this.meShowCancel=!0,this.meCancelText="关闭",this.meConfirmText="一键复制"},sharurl:function(){var t=this.invitationCode;this.meShowModel=!0,this.meTitle="文案推广",this.meContent=this.tuiguang1+t+"\n"+this.url,this.meIndex="m1",this.meShowCancel=!0,this.meCancelText="关闭",this.meConfirmText="一键复制"},logoTime:function(t){uni.previewImage({current:0,urls:t,loop:!0,longPressActions:{itemList:["收藏"],itemColor:"#007AFF"}})},goList:function(){var t=this,e=this.$queue.getData("userId");this.$Request.getT("/app/invite/selectInviteAndPoster?userId="+e).then((function(e){0===e.code&&(e.data.user.imageUrl?t.userImageUrl=e.data.user.imageUrl:t.userImageUrl="/static/img/common/logo.jpg",e.data.user.nickName?t.nickName=e.data.user.nickName:t.nickName=e.data.user.phone,t.invitationCode=e.data.user.invitationCode,t.imageUrl=e.data.url)}))},shareFc:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){var n,i;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=t,e.prev=1,e.next=4,getSharePoster({type:"testShareType",backgroundImage:n.backgroundImage,posterCanvasId:n.canvasId,delayTimeScale:20,drawArray:function(t){var e=t.bgObj,i=(t.type,t.bgScale,e.width,.045*e.width),a=.04*e.height;return new Promise((function(t,o){t([{type:"custom",setDraw:function(t){t.setFillStyle("black"),t.setGlobalAlpha(.3),t.fillRect(0,e.height-.2*e.height,e.width,.2*e.height),t.setGlobalAlpha(1)}},{type:"text",fontStyle:"italic",text:"邀请码:"+n.invitationCode,size:i,color:"white",alpha:1,textAlign:"left",textBaseline:"middle",infoCallBack:function(t){return{dx:e.width-t-i,dy:e.height-3*a}},serialNum:0,id:"tag1"},{type:"qrcode",text:n.url,size:.2*e.width,dx:.05*e.width,dy:e.height-.25*e.width}])}))},setCanvasWH:function(t){var e=t.bgObj;t.type,t.bgScale;n.poster=e}});case 4:i=e.sent,n.poster.finalPath=i.poster.tempFilePath,n.qrShow=!0,e.next=12;break;case 9:e.prev=9,e.t0=e["catch"](1),c.default.hideLoading();case 12:case"end":return e.stop()}}),e,null,[[1,9]])})))()},saveImage:function(){uni.saveImageToPhotosAlbum({filePath:this.poster.finalPath,success:function(t){c.default.showToast("保存成功")}})},hideQr:function(){this.qrShow=!1},onSaveImg:function(){return(0,o.default)((0,a.default)().mark((function t(){return(0,a.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:case"end":return t.stop()}}),t)})))()},createPoster:function(){var t=this;return new Promise((function(e,n){uni.showLoading({title:"海报生成中"});var i=uni.createCanvasContext("poster");i.fillRect(0,0,375,673),i.setFillStyle("#FFF"),i.fillRect(0,0,375,673);var a=t.backgroundImage;uni.downloadFile({url:a,success:function(a){200===a.statusCode?(console.log(t.config("APIHOST1")+"/app/invite/mpCreateQr?invitationCode="+t.invitationCode),uni.downloadFile({url:t.config("APIHOST1")+"/app/invite/mpCreateQr?invitationCode="+t.invitationCode,success:function(t){if(console.log(t),uni.hideLoading(),200===a.statusCode){i.drawImage(a.tempFilePath,0,0,375,500);i.setFontSize(19),i.setFillStyle("#333"),i.fillText("长按识别图中二维码",17,590),i.drawImage(t.tempFilePath,238,526,120,120),i.draw(!0,(function(){uni.canvasToTempFilePath({canvasId:"poster",width:375,height:673,success:function(t){console.log("海报制作成功！"),e(t.tempFilePath)},fail:function(){uni.hideLoading(),n()}})}))}else uni.hideLoading(),uni.showToast({title:"海报制作失败，图片下载失败",icon:"none"})},fail:function(t){console.log(t),uni.hideLoading(),uni.showToast({title:"海报制作失败，图片下载失败",icon:"none"})},complete:function(t){console.log(t),uni.showToast({title:t,icon:"none"})}})):(uni.hideLoading(),uni.showToast({title:"海报制作失败，图片下载失败",icon:"none"}))},fail:function(t){console.log(t),uni.hideLoading(),uni.showToast({title:"海报制作失败，图片下载失败",icon:"none"})}})}))},config:function(t){var e=null;if(t){var n=t.split(".");if(e=n.length>1?d.default[n[0]][n[1]]||null:d.default[t]||null,null==e){var i=cache.get("web_config");i&&(e=n.length>1?i[n[0]][n[1]]||null:i[t]||null)}}return e}}};e.default=f},"800e":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={tkiQrcode:n("1497").default,wmPoster:n("b134").default,uModal:n("7e01").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"content"},[n("v-uni-view",{staticClass:"view1",style:{backgroundImage:"url("+t.backgroundImage+")"}},[n("v-uni-view",{staticStyle:{"padding-top":"820upx"},on:{longpress:function(e){arguments[0]=e=t.$handleEvent(e),t.logoTime(t.userImageUrl)}}},[n("v-uni-view",{staticStyle:{width:"100%",height:"150upx",display:"flex",background:"#FFFFFF",padding:"20upx 10upx"}},[n("v-uni-image",{staticStyle:{"border-radius":"100upx",width:"100upx",height:"100upx","margin-left":"30upx"},attrs:{src:t.userImageUrl}}),n("v-uni-view",{staticClass:"login-view-text1",staticStyle:{"margin-left":"30upx",width:"59%"}},[n("v-uni-view",{staticStyle:{color:"#000000","font-size":"16px"}},[t._v(t._s(t.nickName))]),n("v-uni-view",{staticStyle:{color:"#000000","font-size":"12px","margin-top":"20upx"}},[t._v("邀请码:"+t._s(t.invitationCode))])],1),n("v-uni-canvas",{staticStyle:{width:"140upx",height:"130upx"},attrs:{"canvas-id":"qrcode"}})],1)],1)],1),n("v-uni-view",{staticStyle:{display:"flex","flex-direction":"row",padding:"40upx","justify-content":"center"}},[n("v-uni-button",{staticStyle:{"background-color":"#FFCB49","font-size":"16px","font-weight":"bold",color:"#FFFFFF",width:"50%"},attrs:{type:"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.share()}}},[t._v("文案推广")]),n("v-uni-button",{staticStyle:{"background-color":"#FF5808","font-size":"16px","font-weight":"bold",color:"#FFFFFF",width:"50%","margin-left":"40upx"},attrs:{type:"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showModal()}}},[t._v("生成海报")]),n("v-uni-view",{staticClass:"flex_row_c_c modalView",class:t.qrShow?"show":"",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.hideQr()}}},[n("v-uni-view",{staticClass:"flex_column"},[n("v-uni-view",{staticClass:"backgroundColor-white padding1vh border_radius_10px"},[n("v-uni-image",{staticClass:"posterImage",attrs:{src:t.poster.finalPath||"",mode:"widthFix"}})],1),n("v-uni-view",{staticClass:"flex_row marginTop2vh"},[n("v-uni-button",{attrs:{type:"primary",size:"mini"}},[t._v("长按上方图片保存")])],1)],1)],1),n("v-uni-view",{staticClass:"hideCanvasView"},[n("v-uni-canvas",{staticClass:"hideCanvas",style:{width:(t.poster.width||10)+"px",height:(t.poster.height||10)+"px"},attrs:{"canvas-id":"default_PosterCanvasId"}})],1)],1),n("tki-qrcode",{ref:"qrcode",attrs:{val:t.url,size:200,background:"#fff",foreground:"#000",pdground:"#000",onval:!0,loadMake:!0,show:!1},on:{result:function(e){arguments[0]=e=t.$handleEvent(e),t.qrR.apply(void 0,arguments)}}}),n("v-uni-view",{staticClass:"cu-modal",class:"Image"==t.modalName?"show":"",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.hideModal.apply(void 0,arguments)}}},[t.backgroundImage&&t.erweimapath&&t.haibaoShow?n("v-uni-view",{staticClass:"cu-dialog",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.hideModal.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"bg-img"},[n("wm-poster",{attrs:{imgSrc:t.backgroundImage,Referrer:"我的邀请码:"+t.invitationCode,QrSrc:t.erweimapath,Title:t.tuiguang,LineType:!1},on:{success:function(e){arguments[0]=e=t.$handleEvent(e),t.posterSuccess.apply(void 0,arguments)}}})],1)],1):t._e()],1),n("u-modal",{attrs:{content:t.meContent,title:t.meTitle,"show-cancel-button":t.meShowCancel,"confirm-text":t.meConfirmText,"cancel-text":t.meCancelText},on:{cancel:function(e){arguments[0]=e=t.$handleEvent(e),t.meHandleClose.apply(void 0,arguments)},confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.meHandleBtn.apply(void 0,arguments)}},model:{value:t.meShowModel,callback:function(e){t.meShowModel=e},expression:"meShowModel"}})],1)},o=[]},eb48:function(t,e,n){"use strict";n.r(e);var i=n("7c95"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},f022:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("aa9c"),n("7a76"),n("c9b5");var i={};(function(){function t(t){this.mode=n.MODE_8BIT_BYTE,this.data=t}function e(t,e){this.typeNumber=t,this.errorCorrectLevel=e,this.modules=null,this.moduleCount=0,this.dataCache=null,this.dataList=new Array}t.prototype={getLength:function(t){return this.data.length},write:function(t){for(var e=0;e<this.data.length;e++)t.put(this.data.charCodeAt(e),8)}},e.prototype={addData:function(e){var n=new t(e);this.dataList.push(n),this.dataCache=null},isDark:function(t,e){if(t<0||this.moduleCount<=t||e<0||this.moduleCount<=e)throw new Error(t+","+e);return this.modules[t][e]},getModuleCount:function(){return this.moduleCount},make:function(){if(this.typeNumber<1){var t=1;for(t=1;t<40;t++){for(var e=c.getRSBlocks(t,this.errorCorrectLevel),n=new d,i=0,a=0;a<e.length;a++)i+=e[a].dataCount;for(a=0;a<this.dataList.length;a++){var o=this.dataList[a];n.put(o.mode,4),n.put(o.getLength(),r.getLengthInBits(o.mode,t)),o.write(n)}if(n.getLengthInBits()<=8*i)break}this.typeNumber=t}this.makeImpl(!1,this.getBestMaskPattern())},makeImpl:function(t,n){this.moduleCount=4*this.typeNumber+17,this.modules=new Array(this.moduleCount);for(var i=0;i<this.moduleCount;i++){this.modules[i]=new Array(this.moduleCount);for(var a=0;a<this.moduleCount;a++)this.modules[i][a]=null}this.setupPositionProbePattern(0,0),this.setupPositionProbePattern(this.moduleCount-7,0),this.setupPositionProbePattern(0,this.moduleCount-7),this.setupPositionAdjustPattern(),this.setupTimingPattern(),this.setupTypeInfo(t,n),this.typeNumber>=7&&this.setupTypeNumber(t),null==this.dataCache&&(this.dataCache=e.createData(this.typeNumber,this.errorCorrectLevel,this.dataList)),this.mapData(this.dataCache,n)},setupPositionProbePattern:function(t,e){for(var n=-1;n<=7;n++)if(!(t+n<=-1||this.moduleCount<=t+n))for(var i=-1;i<=7;i++)e+i<=-1||this.moduleCount<=e+i||(this.modules[t+n][e+i]=0<=n&&n<=6&&(0==i||6==i)||0<=i&&i<=6&&(0==n||6==n)||2<=n&&n<=4&&2<=i&&i<=4)},getBestMaskPattern:function(){for(var t=0,e=0,n=0;n<8;n++){this.makeImpl(!0,n);var i=r.getLostPoint(this);(0==n||t>i)&&(t=i,e=n)}return e},createMovieClip:function(t,e,n){var i=t.createEmptyMovieClip(e,n);this.make();for(var a=0;a<this.modules.length;a++)for(var o=1*a,r=0;r<this.modules[a].length;r++){var s=1*r,u=this.modules[a][r];u&&(i.beginFill(0,100),i.moveTo(s,o),i.lineTo(s+1,o),i.lineTo(s+1,o+1),i.lineTo(s,o+1),i.endFill())}return i},setupTimingPattern:function(){for(var t=8;t<this.moduleCount-8;t++)null==this.modules[t][6]&&(this.modules[t][6]=t%2==0);for(var e=8;e<this.moduleCount-8;e++)null==this.modules[6][e]&&(this.modules[6][e]=e%2==0)},setupPositionAdjustPattern:function(){for(var t=r.getPatternPosition(this.typeNumber),e=0;e<t.length;e++)for(var n=0;n<t.length;n++){var i=t[e],a=t[n];if(null==this.modules[i][a])for(var o=-2;o<=2;o++)for(var s=-2;s<=2;s++)this.modules[i+o][a+s]=-2==o||2==o||-2==s||2==s||0==o&&0==s}},setupTypeNumber:function(t){for(var e=r.getBCHTypeNumber(this.typeNumber),n=0;n<18;n++){var i=!t&&1==(e>>n&1);this.modules[Math.floor(n/3)][n%3+this.moduleCount-8-3]=i}for(n=0;n<18;n++){i=!t&&1==(e>>n&1);this.modules[n%3+this.moduleCount-8-3][Math.floor(n/3)]=i}},setupTypeInfo:function(t,e){for(var n=this.errorCorrectLevel<<3|e,i=r.getBCHTypeInfo(n),a=0;a<15;a++){var o=!t&&1==(i>>a&1);a<6?this.modules[a][8]=o:a<8?this.modules[a+1][8]=o:this.modules[this.moduleCount-15+a][8]=o}for(a=0;a<15;a++){o=!t&&1==(i>>a&1);a<8?this.modules[8][this.moduleCount-a-1]=o:a<9?this.modules[8][15-a-1+1]=o:this.modules[8][15-a-1]=o}this.modules[this.moduleCount-8][8]=!t},mapData:function(t,e){for(var n=-1,i=this.moduleCount-1,a=7,o=0,s=this.moduleCount-1;s>0;s-=2){6==s&&s--;while(1){for(var u=0;u<2;u++)if(null==this.modules[i][s-u]){var l=!1;o<t.length&&(l=1==(t[o]>>>a&1));var c=r.getMask(e,i,s-u);c&&(l=!l),this.modules[i][s-u]=l,a--,-1==a&&(o++,a=7)}if(i+=n,i<0||this.moduleCount<=i){i-=n,n=-n;break}}}}},e.PAD0=236,e.PAD1=17,e.createData=function(t,n,i){for(var a=c.getRSBlocks(t,n),o=new d,s=0;s<i.length;s++){var u=i[s];o.put(u.mode,4),o.put(u.getLength(),r.getLengthInBits(u.mode,t)),u.write(o)}var l=0;for(s=0;s<a.length;s++)l+=a[s].dataCount;if(o.getLengthInBits()>8*l)throw new Error("code length overflow. ("+o.getLengthInBits()+">"+8*l+")");o.getLengthInBits()+4<=8*l&&o.put(0,4);while(o.getLengthInBits()%8!=0)o.putBit(!1);while(1){if(o.getLengthInBits()>=8*l)break;if(o.put(e.PAD0,8),o.getLengthInBits()>=8*l)break;o.put(e.PAD1,8)}return e.createBytes(o,a)},e.createBytes=function(t,e){for(var n=0,i=0,a=0,o=new Array(e.length),s=new Array(e.length),u=0;u<e.length;u++){var c=e[u].dataCount,d=e[u].totalCount-c;i=Math.max(i,c),a=Math.max(a,d),o[u]=new Array(c);for(var f=0;f<o[u].length;f++)o[u][f]=255&t.buffer[f+n];n+=c;var h=r.getErrorCorrectPolynomial(d),g=new l(o[u],h.getLength()-1),m=g.mod(h);s[u]=new Array(h.getLength()-1);for(f=0;f<s[u].length;f++){var v=f+m.getLength()-s[u].length;s[u][f]=v>=0?m.get(v):0}}var p=0;for(f=0;f<e.length;f++)p+=e[f].totalCount;var w=new Array(p),b=0;for(f=0;f<i;f++)for(u=0;u<e.length;u++)f<o[u].length&&(w[b++]=o[u][f]);for(f=0;f<a;f++)for(u=0;u<e.length;u++)f<s[u].length&&(w[b++]=s[u][f]);return w};for(var n={MODE_NUMBER:1,MODE_ALPHA_NUM:2,MODE_8BIT_BYTE:4,MODE_KANJI:8},a={L:1,M:0,Q:3,H:2},o={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7},r={PATTERN_POSITION_TABLE:[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],G15:1335,G18:7973,G15_MASK:21522,getBCHTypeInfo:function(t){var e=t<<10;while(r.getBCHDigit(e)-r.getBCHDigit(r.G15)>=0)e^=r.G15<<r.getBCHDigit(e)-r.getBCHDigit(r.G15);return(t<<10|e)^r.G15_MASK},getBCHTypeNumber:function(t){var e=t<<12;while(r.getBCHDigit(e)-r.getBCHDigit(r.G18)>=0)e^=r.G18<<r.getBCHDigit(e)-r.getBCHDigit(r.G18);return t<<12|e},getBCHDigit:function(t){var e=0;while(0!=t)e++,t>>>=1;return e},getPatternPosition:function(t){return r.PATTERN_POSITION_TABLE[t-1]},getMask:function(t,e,n){switch(t){case o.PATTERN000:return(e+n)%2==0;case o.PATTERN001:return e%2==0;case o.PATTERN010:return n%3==0;case o.PATTERN011:return(e+n)%3==0;case o.PATTERN100:return(Math.floor(e/2)+Math.floor(n/3))%2==0;case o.PATTERN101:return e*n%2+e*n%3==0;case o.PATTERN110:return(e*n%2+e*n%3)%2==0;case o.PATTERN111:return(e*n%3+(e+n)%2)%2==0;default:throw new Error("bad maskPattern:"+t)}},getErrorCorrectPolynomial:function(t){for(var e=new l([1],0),n=0;n<t;n++)e=e.multiply(new l([1,s.gexp(n)],0));return e},getLengthInBits:function(t,e){if(1<=e&&e<10)switch(t){case n.MODE_NUMBER:return 10;case n.MODE_ALPHA_NUM:return 9;case n.MODE_8BIT_BYTE:return 8;case n.MODE_KANJI:return 8;default:throw new Error("mode:"+t)}else if(e<27)switch(t){case n.MODE_NUMBER:return 12;case n.MODE_ALPHA_NUM:return 11;case n.MODE_8BIT_BYTE:return 16;case n.MODE_KANJI:return 10;default:throw new Error("mode:"+t)}else{if(!(e<41))throw new Error("type:"+e);switch(t){case n.MODE_NUMBER:return 14;case n.MODE_ALPHA_NUM:return 13;case n.MODE_8BIT_BYTE:return 16;case n.MODE_KANJI:return 12;default:throw new Error("mode:"+t)}}},getLostPoint:function(t){for(var e=t.getModuleCount(),n=0,i=0;i<e;i++)for(var a=0;a<e;a++){for(var o=0,r=t.isDark(i,a),s=-1;s<=1;s++)if(!(i+s<0||e<=i+s))for(var u=-1;u<=1;u++)a+u<0||e<=a+u||0==s&&0==u||r==t.isDark(i+s,a+u)&&o++;o>5&&(n+=3+o-5)}for(i=0;i<e-1;i++)for(a=0;a<e-1;a++){var l=0;t.isDark(i,a)&&l++,t.isDark(i+1,a)&&l++,t.isDark(i,a+1)&&l++,t.isDark(i+1,a+1)&&l++,0!=l&&4!=l||(n+=3)}for(i=0;i<e;i++)for(a=0;a<e-6;a++)t.isDark(i,a)&&!t.isDark(i,a+1)&&t.isDark(i,a+2)&&t.isDark(i,a+3)&&t.isDark(i,a+4)&&!t.isDark(i,a+5)&&t.isDark(i,a+6)&&(n+=40);for(a=0;a<e;a++)for(i=0;i<e-6;i++)t.isDark(i,a)&&!t.isDark(i+1,a)&&t.isDark(i+2,a)&&t.isDark(i+3,a)&&t.isDark(i+4,a)&&!t.isDark(i+5,a)&&t.isDark(i+6,a)&&(n+=40);var c=0;for(a=0;a<e;a++)for(i=0;i<e;i++)t.isDark(i,a)&&c++;var d=Math.abs(100*c/e/e-50)/5;return n+=10*d,n}},s={glog:function(t){if(t<1)throw new Error("glog("+t+")");return s.LOG_TABLE[t]},gexp:function(t){while(t<0)t+=255;while(t>=256)t-=255;return s.EXP_TABLE[t]},EXP_TABLE:new Array(256),LOG_TABLE:new Array(256)},u=0;u<8;u++)s.EXP_TABLE[u]=1<<u;for(u=8;u<256;u++)s.EXP_TABLE[u]=s.EXP_TABLE[u-4]^s.EXP_TABLE[u-5]^s.EXP_TABLE[u-6]^s.EXP_TABLE[u-8];for(u=0;u<255;u++)s.LOG_TABLE[s.EXP_TABLE[u]]=u;function l(t,e){if(void 0==t.length)throw new Error(t.length+"/"+e);var n=0;while(n<t.length&&0==t[n])n++;this.num=new Array(t.length-n+e);for(var i=0;i<t.length-n;i++)this.num[i]=t[i+n]}function c(t,e){this.totalCount=t,this.dataCount=e}function d(){this.buffer=new Array,this.length=0}l.prototype={get:function(t){return this.num[t]},getLength:function(){return this.num.length},multiply:function(t){for(var e=new Array(this.getLength()+t.getLength()-1),n=0;n<this.getLength();n++)for(var i=0;i<t.getLength();i++)e[n+i]^=s.gexp(s.glog(this.get(n))+s.glog(t.get(i)));return new l(e,0)},mod:function(t){if(this.getLength()-t.getLength()<0)return this;for(var e=s.glog(this.get(0))-s.glog(t.get(0)),n=new Array(this.getLength()),i=0;i<this.getLength();i++)n[i]=this.get(i);for(i=0;i<t.getLength();i++)n[i]^=s.gexp(s.glog(t.get(i))+e);return new l(n,0).mod(t)}},c.RS_BLOCK_TABLE=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]],c.getRSBlocks=function(t,e){var n=c.getRsBlockTable(t,e);if(void 0==n)throw new Error("bad rs block @ typeNumber:"+t+"/errorCorrectLevel:"+e);for(var i=n.length/3,a=new Array,o=0;o<i;o++)for(var r=n[3*o+0],s=n[3*o+1],u=n[3*o+2],l=0;l<r;l++)a.push(new c(s,u));return a},c.getRsBlockTable=function(t,e){switch(e){case a.L:return c.RS_BLOCK_TABLE[4*(t-1)+0];case a.M:return c.RS_BLOCK_TABLE[4*(t-1)+1];case a.Q:return c.RS_BLOCK_TABLE[4*(t-1)+2];case a.H:return c.RS_BLOCK_TABLE[4*(t-1)+3];default:return}},d.prototype={get:function(t){var e=Math.floor(t/8);return 1==(this.buffer[e]>>>7-t%8&1)},put:function(t,e){for(var n=0;n<e;n++)this.putBit(1==(t>>>e-n-1&1))},getLengthInBits:function(){return this.length},putBit:function(t){var e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),t&&(this.buffer[e]|=128>>>this.length%8),this.length++}},i={errorCorrectLevel:a,defaults:{size:256,margin:0,backgroundColor:"#ffffff",foregroundColor:"#000000",fileType:"png",errorCorrectLevel:a.H,typeNumber:-1},make:function(t){var n={canvasId:t.canvasId,componentInstance:t.componentInstance,text:t.text,size:this.defaults.size,margin:this.defaults.margin,backgroundColor:this.defaults.backgroundColor,foregroundColor:this.defaults.foregroundColor,fileType:this.defaults.fileType,errorCorrectLevel:this.defaults.errorCorrectLevel,typeNumber:this.defaults.typeNumber};if(t)for(var i in t)n[i]=t[i];t=n,t.canvasId?function(){var n=new e(t.typeNumber,t.errorCorrectLevel);n.addData(function(t){for(var e,n="",i=0;i<t.length;i++)e=t.charCodeAt(i),e>=1&&e<=127?n+=t.charAt(i):e>2047?(n+=String.fromCharCode(224|e>>12&15),n+=String.fromCharCode(128|e>>6&63),n+=String.fromCharCode(128|e>>0&63)):(n+=String.fromCharCode(192|e>>6&31),n+=String.fromCharCode(128|e>>0&63));return n}(t.text)),n.make();var i=uni.createCanvasContext(t.canvasId,t.componentInstance);i.setFillStyle(t.backgroundColor),i.fillRect(0,0,t.size,t.size);for(var a=(t.size-2*t.margin)/n.getModuleCount(),o=a,r=0;r<n.getModuleCount();r++)for(var s=0;s<n.getModuleCount();s++){var u=n.isDark(r,s)?t.foregroundColor:t.backgroundColor;i.setFillStyle(u);var l=Math.round(s*a)+t.margin,c=Math.round(r*o)+t.margin,d=Math.ceil((s+1)*a)-Math.floor(s*a),f=Math.ceil((r+1)*a)-Math.floor(r*a);i.fillRect(l,c,d,f)}setTimeout((function(){i.draw(!1,function(){setTimeout((function(){uni.canvasToTempFilePath({canvasId:t.canvasId,fileType:t.fileType,width:t.size,height:t.size,destWidth:t.size,destHeight:t.size,success:function(e){t.success&&t.success(e.tempFilePath)},fail:function(e){t.fail&&t.fail(e)},complete:function(e){t.complete&&t.complete(e)}},t.componentInstance)}),t.text.length+100)}())}),150)}():console.error("uQRCode: Please set canvasId!")}}})();var a=i;e.default=a}}]);