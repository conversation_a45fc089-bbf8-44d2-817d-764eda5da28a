<template>
	<view class="">
		<view v-if="indexSelect=='是'">


			<!-- #ifndef H5 -->
			<!-- 导航栏 -->
			<u-navbar title="首页" :is-back="false" :background="background"></u-navbar>
			<!-- #endif -->
			<!-- 背景 -->
			<view class="bgs">
				<!-- #ifdef H5 -->
				<image src="../../static/images/bgImg.png" style="width: 100%;height: 189px;"></image>
				<!-- #endif -->
				<!-- #ifndef H5 -->
				<image src="../../static/images/bgImg.png" style="width: 100%;height: 279px;"></image>
				<!-- #endif -->
			</view>
			<!-- 内容 -->
			<view class="content">
				<view class="headtop">
					<view class="flex align-center">
						<view class="headleft" :class="listIndex==index?'actleft':''" v-for="(item,index) in listtab"
							:key="index" @click="bindisttab(index)">
							{{item.name}}
						</view>
					</view>
					<view class="headright" @click="opensx">
						<image src="../../static/images/index/shaix.png" style="width: 36rpx;height: 40rpx;"></image>
					</view>
				</view>

				<view class="my-box">
					<sliderSwiper @closeSlider="closeSlider" @clickSlider="clickSlider" :list="listImg" />
					<!-- <vastwuImgbox ref="vastwuImgbox" :list="list" :listImg="listImg" :auto="sauto" :isQh="isQh" :offsetX="0"
					:offsetY="-16" @slideclick="slideclick" @onclickImg="onclickImg">
				</vastwuImgbox> -->
				</view>
			</view>
		</view>
		<view v-else style="padding-bottom: 120rpx;">
			<!-- #ifndef H5 -->
			<!-- 导航栏 -->
			<u-navbar title="首页" :is-back="false" :border-bottom="false"
				:background="!fixeds?backgrounds:backgroundsl"></u-navbar>
			<!-- #endif -->

			<view class="swiper-banner">
				<swiper class="swiper-container" :autoplay="true" :interval="4000" :circular="true"
					:indicator-dots="false" indicator-active-color="#f2ad44" indicator-color="#cccccc"
					style="height: 420rpx;">
					<block v-for="(item, index3) in bannerImg" :key="index3">
						<swiper-item class="swiper-wrapper" @tap='goNav(item)' v-if="item">
							<image lazy-load="true" fade-show="true" :src="item.imageUrl"
								style="width: 100%; height:420rpx;" mode="aspectFill">
							</image>
						</swiper-item>
					</block>
				</swiper>
			</view>

			<!-- #ifndef H5 -->
			<view class="flex margin-lr justify-between align-center" style="margin-top: 180rpx;">
			<!-- #endif -->
				<!-- #ifdef H5 -->
				<view class="flex margin-lr justify-between align-center" style="margin-top: -30rpx;">
				<!-- #endif -->
					<view v-for="(item,index) in navlist" :key="index" @tap='goNav(item)'>
						<image :src="item.imageUrl" style="width: 160rpx;height:160rpx;"></image>
					</view>
				</view>

				<view class="headtops">
					<view class="flex align-center">
						<view class="headleft" :class="listIndex==index?'actleft':''" v-for="(item,index) in listtab"
							:key="index" @click="bindisttab(index)">
							{{item.name}}

							<view class="line" v-if="listIndex==index"></view>

						</view>
					</view>
					<view class="headright" @click="opensx">
						<image src="../../static/images/index/shaix.png" style="width: 36rpx;height: 40rpx;"></image>
					</view>
				</view>

				<view class="litix">
					<view class="listiem" v-for="(item,index) in listImg" :key="index" @click="goDetail(item.userId)">
						<image class="main-img" mode="aspectFill"
							:src="item.userImg?item.userImg:'../../static/logo.png'">
						</image>
						<view class="margin-left-sm flex-sub">
							<view class="flex align-center justify-between">
								<view class="flex align-center ">
									<view class="yhm">{{item.realName}}</view>
									<image src="../../static/images/my/rzicon.png" style="width: 40rpx;height: 40rpx;">
									</image>
								</view>
								<view class="wezhi">

									<text v-if="item.locationCity!='市辖区'"> {{item.locationCity}}</text>
									<text v-else> {{item.locationProvince}}</text>
									{{item.locationCounty}}
								</view>
							</view>
							<view class="flex align-center margin-tb-xs">
								<view class="sexicon" v-if="item.sex==1">
									<u-icon name="man" color="#FFFFFF" size="18"></u-icon>
									{{item.age}}岁
								</view>
								<view class="sexicons" v-if="item.sex==2">
									<u-icon name="woman" color="#FFFFFF" size="18"></u-icon>
									{{item.age}}岁
								</view>
								<view class="labe">{{item.education}}</view>
								<view class="labe" v-if="item.marriageStatus==1">未婚</view>
								<view class="labe" v-if="item.marriageStatus==2">离异</view>
								<view class="labe">{{item.userHeight}}CM</view>
							</view>
							<view class="remk">{{item.idealAspect}}</view>
						</view>
					</view>
				</view>
			</view>

			<!-- modal弹窗 -->
			<u-modal v-model="meShowModel" :content="meContent" :title="meTitle" :show-cancel-button='meShowCancel'
				@cancel="meHandleClose" @confirm="meHandleBtn" :confirm-text='meConfirmText'
				:cancel-text='meCancelText'></u-modal>


			<u-popup v-model="show" mode="bottom" border-radius="24" :closeable="true" close-icon="close-circle"
				height="800rpx" close-icon-size="45">
				<view class=" " style="padding: 40rpx 40rpx 230rpx 40rpx;">
					<view class="text-center tites1">筛选</view>
					<view class="" style="margin-top:80rpx">
						<view class="tites">性别</view>
						<view class="padding-tb-sm flex">
							<view class="xlbox" :class="sex==-1?'xlboxAct':''" @click="changsex(-1)">不限</view>
							<view class="xlbox" :class="sex==1?'xlboxAct':''" @click="changsex(1)">男</view>
							<view class="xlbox" :class="sex==2?'xlboxAct':''" @click="changsex(2)">女</view>
						</view>
					</view>


					<view class="margin-top">
						<view class="tites">年龄</view>
						<view class="padding-tb-sm flex flex-wrap">
							<view v-for="(item,index) in agelist" :key="index" class="xlbox margin-bottom-sm"
								@click="changAge(index,item)" :class="ageIndex==index?'xlboxAct':''">
								<text
									v-if="item.minMoney!=0&&item.maxMoney!=0">{{item.minMoney}}-{{item.maxMoney}}</text>
								<text v-if="item.minMoney==0">{{item.maxMoney}}</text>
								<text v-if="item.maxMoney==0">{{item.minMoney}}</text>
							</view>

						</view>
						<!-- <view class="padding-tb-sm">
						<slider :value="popupage" :min="minAge" :max="maxAge" step="1" @change="sliderChange"
							activeColor="linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);" backgroundColor="#E0E3EA" block-size="20" show-value="true" />

						<view class="flex align-center justify-between margin-top-xs">
							<view>{{minAge}}</view>
							<view>{{maxAge}}</view>
						</view>
					</view> -->
					</view>


					<view class="margin-top-xs">
						<view class="tites">身高</view>
						<view class="padding-tb-sm flex flex-wrap">
							<view v-for="(item,index) in heightlist" :key="index" class="xlbox margin-bottom-sm"
								@click="changHeight(index,item)" :class="heightIndex==index?'xlboxAct':''">
								<text
									v-if="item.minMoney!=0&&item.maxMoney!=0">{{item.minMoney}}-{{item.maxMoney}}</text>
								<text v-if="item.minMoney==0">{{item.maxMoney}}</text>
								<text v-if="item.maxMoney==0">{{item.minMoney}}</text>
							</view>

						</view>
						<!-- <view class="padding-tb-sm">
						<slider :value="popupcm" :min="mixHeight" :max="maxHeight" step="1" @change="sliderChanges"
							activeColor="linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);" backgroundColor="#E0E3EA" block-size="20" show-value="true" />

						</u-slider>
						<view class="flex align-center justify-between margin-top-xs">
							<view>{{mixHeight}}</view>
							<view>{{maxHeight}}</view>
						</view>
					</view> -->
					</view>

					<view class="margin-top-xs">
						<view class="tites">学历</view>
						<view class="padding-tb-sm flex flex-wrap">
							<view v-for="(item,index) in xllist" :key="index" class="xlbox margin-bottom-sm"
								@click="changexl(index)" :class="xllistIndex==index?'xlboxAct':''">
								{{item.name}}
							</view>

						</view>
					</view>

					<view class="margin-top-xs">
						<view class="tites">推荐地区</view>
						<view class="padding-tb-sm flex">
							<view class="xlbox" :class="dqIndex==0?'xlboxAct':''" @click="changesq(0)">不限</view>
							<view class="xlbox" :class="dqIndex==1?'xlboxAct':''" @click="changesq(1)">
								{{city?city:'选择地区'}}
							</view>
							<!-- <view class="xlbox margin-left-sm " style="padding:0 55rpx;">
							<u-icon name="plus" color="#CCCCCC" size="35"></u-icon>
						</view> -->
						</view>
					</view>

					<view class="flex align-center justify-between tanbers">
						<view class="btn" @click="chongzhi">重置</view>
						<view class="btn" @click="shaixuan">保存</view>
					</view>
				</view>
			</u-popup>
			<!-- 选择地区 -->
			<u-picker mode="region" v-model="dqshow" :params="params" @confirm="confirmdq"
				@cancel="canceldq"></u-picker>

			<!-- modal弹窗 -->
			<u-modal v-model="meShowModel" :content="meContent" :title="meTitle" :show-cancel-button='meShowCancel'
				@cancel="meHandleClose" @confirm="meHandleBtn" :confirm-text='meConfirmText'
				:cancel-text='meCancelText'></u-modal>
		</view>
</template>

<script>
	import empty from '@/components/empty'
	import vastwuImgbox from '@/components/vastwu-imgbox/vastwu-imgbox.vue'
	import sliderSwiper from '@/components/sliderSiper/sliderSiper.vue'
	export default {
		components: {
			vastwuImgbox,
			empty,
			sliderSwiper
		},
		data() {
			return {
				background: {
					backgroundImage: 'linear-gradient(90deg, #E4E7F8 0%, #F1E3F4 46%, #FDE1EF 100%)'
				},
				backgrounds: {
					backgroundColor: 'transparent',
				},
				backgroundsl: {
					backgroundColor: '#FFFFFF',
				},

				listtab: [{
					name: '推荐',
					id: 1
				}, {
					name: '附近',
					id: 2
				}],
				listIndex: 0,
				//弹窗
				meShowModel: false, //是否显示弹框
				meShowCancel: true, //是否显示取消按钮
				meTitle: '提示', //弹框标题
				meContent: '', //弹框内容
				meConfirmText: '确认', //确认按钮的文字
				meCancelText: '取消', //关闭按钮的文字
				meIndex: '', //弹窗的key

				list: [],
				listImg: [],
				show: false,
				// popupage: '',
				agelist: [],
				ageIndex: -1,
				heightlist: [],
				heightIndex: -1,

				popupcm: '',
				xllist: [],
				xllistIndex: -1,
				dqIndex: 0,
				dqshow: false,
				params: {
					province: true,
					city: true,
					area: false
				},
				city: '',
				province: '',
				minAge: '',
				maxAge: '',
				mixHeight: '',
				maxHeight: '',
				sxIstrue: 0,

				HeightIstrue: 0,
				statusStauts: '',
				formData: {},
				sauto: false,
				firstlogin: true,
				arr: [],
				showModal: true,
				sex: '-1',
				indexSelect: '否',
				fixeds: false,
				bannerImg: [],
				navlist: []

			}
		},
		onShareAppMessage(res) {
			return {
				path: '/pages/index/index?invitation=' + this.invitationCode, //这是为了传参   onload(data){let id=data.id;}
				title: this.tuiguang,
				imageUrl: this.bgImg
			}
		},
		/*
		 * uniapp微信小程序分享页面到微信朋友圈
		 */
		onShareTimeline(res) {
			return {
				path: '/pages/index/index?invitation=' + this.invitationCode, //这是为了传参   onload(data){let id=data.id;}
				title: this.tuiguang,
				imageUrl: this.bgImg
			}
		},
		//判断粘性布局是否触顶
		onPageScroll(e) {
			console.log(e.scrollTop)
			if (Number(e.scrollTop) >= 80) {
				this.fixeds = true
			} else {
				this.fixeds = false
			}
		},
		onLoad(e) {
			this.indexSelect = this.$queue.getData('indexSelect');
			this.invitationCode = this.$queue.getData('invitationCode');
			this.shangxianSelect = this.$queue.getData('shangxianSelect');
			this.myId = this.$queue.getData('userId');

			let that = this




			that.$Request.getT('/app/common/type/292').then(res => { //审核状态通知
				if (res.code == 0) {
					if (res.data && res.data.value) {
						that.arr.push(res.data.value)
					}
				}
			})

			if (that.myId) {

				that.$Request.getT('/app/common/type/116').then(res => { //分享文字
					if (res.code === 0) {
						if (res.data && res.data.value) {
							that.tuiguang = res.data.value;
						}
					}
				});
				that.$Request.getT('/app/common/type/313').then(res => { //分享图片配置
					if (res.code === 0) {
						if (res.data && res.data.value) {
							that.bgImg = res.data.value;
						}
					}
				});


			}

			uni.getLocation({
				type: 'gcj02',
				geocode: true, //设置该参数为true可直接获取经纬度及城市信息
				success: function(res) {
					console.log(res, '地理位置')
					that.latitude = res.latitude
					that.longitude = res.longitude
					uni.setStorageSync('latitude', res.latitude)
					uni.setStorageSync('longitude', res.longitude)

					that.selectCity(that.longitude, that.latitude);
				},
				fail: function() {
					console.log('获取地址失败')
				}
			})
			// #ifdef MP-WEIXIN
			if (e.scene) {
				const scene = decodeURIComponent(e.scene);
				that.$queue.setData('inviterCode', scene.split(',')[0]);
			}
			// #endif

			// 获取邀请码保存到本地
			if (e.invitation) {
				that.$queue.setData('inviterCode', e.invitation);
			}
			if (uni.getStorageSync('sex')) {
				that.sex = uni.getStorageSync('sex') == 1 ? 2 : 1
			}
			that.getlist()
		},
		onShow() {
			let that = this
			if (uni.getStorageSync('sex')) {
				that.sex = uni.getStorageSync('sex') == 1 ? 2 : 1
			}
			//在详情中设置为不喜欢返回后重新回去数据
			uni.$once('closeCard', data => {
				that.getlist()
			})
			if (uni.getStorageSync('token')) {
				that.getRenZheng()
				that.getIsVip()
			}
			that.myId = uni.getStorageSync('userId')
			that.getjingang()
			that.getBnner()
			that.getxllist()
			that.getAge()
			that.getHeight()
			// #ifdef MP-WEIXIN
			//订阅
			if (that.myId) {
				if (that.showModal) {
					setTimeout(d => {
						that.openMsg()
					}, 2000)
				}
			}
			// #endif
		},

		methods: {
			// 跳转配置路由
			goNav(item) {

				if (item.name === '跳转小程序') {
					// #ifdef MP-WEIXIN
					let wxitem = item.describes.split('~');
					uni.navigateToMiniProgram({
						appId: wxitem[0],
						path: wxitem[1],
						envVersion: 'release',
						success(res) {
							// 打开成功
							console.log(res);
						}
					})
					return;
					// #endif
				}
				let url = item.url
				if (url) {
					if (url.indexOf('/pages/') !== -1 || url.indexOf('/my/') !== -1 || url.indexOf('/package/') !== -1) {
						if (url == '/pages/hongniang/index') {
							uni.switchTab({
								url
							})
						} else {
							uni.navigateTo({
								url
							});
						}
					} else {
						if (url) {
							//#ifndef H5
							uni.navigateTo({
								url: '/pages/index/webView?url=' + url
							});
							//#endif
							//#ifdef H5
							window.location.href = url;
							//#endif
						}
					}
				}

			},
			getjingang() {
				this.$Request.getT('/app/banner/selectBannerList?classify=2').then(res => {
					if (res.code == 0) {
						this.navlist = res.data
					}
				})
			},
			getBnner() {
				this.$Request.getT('/app/banner/selectBannerList?classify=8').then(res => {
					if (res.code == 0) {
						this.bannerImg = res.data
					}
				})
			},
			closeSlider() {
				console.log('bbbbbbbbbbbbbbb')
				this.meShowModel = true
				this.meTitle = '提示'
				this.meContent = '您还未登录,请先登录'
				this.meIndex = 'm1'
				this.meShowCancel = true
			},
			goDetail(userId) {
				if (uni.getStorageSync('token')) {
					uni.navigateTo({
						url: '/package/pages/game/detail?byUserId=' + userId
					})
				} else {
					this.meShowModel = true
					this.meTitle = '提示'
					this.meContent = '您还未登录,请先登录'
					this.meIndex = 'm1'
					this.meShowCancel = true
				}
			},
			clickSlider(userId) {
				uni.navigateTo({
					url: '/package/pages/game/detail?byUserId=' + userId
				})
				// if (uni.getStorageSync('token')) {
				// 	uni.navigateTo({
				// 		url: '/package/pages/game/detail?byUserId=' + userId
				// 	})
				// } else {

				// 	this.meShowModel = true
				// 	this.meTitle = '提示'
				// 	this.meContent = '您还未登录,请先登录'
				// 	this.meIndex = 'm1'
				// 	this.meShowCancel = true
				// }
			},
			getRenZheng() {
				this.$Request.get("/app/userCertification/getMyUserCertification", {
					authType: 1
				}).then(res => {
					if (res.code == 0 && res.data) {
						// 0审核中 1通过 2拒绝
						if (res.data.status == 0) {
							this.statusStauts = 1 //审核中
							uni.setStorageSync('statusStauts', this.statusStauts)
						} else if (res.data.status == 1) {
							this.statusStauts = 2 //已实名
							uni.setStorageSync('statusStauts', this.statusStauts)
						} else if (res.data.status == 2) {
							this.statusStauts = 3 //已拒绝
							uni.setStorageSync('statusStauts', this.statusStauts)
						}
					} else {
						this.statusStauts = 0 //未实名
						uni.setStorageSync('statusStauts', this.statusStauts)
					}
				});
			},
			opensx() {
				let that = this
				// #ifdef MP-WEIXIN
				wx.requestSubscribeMessage({
					tmplIds: that.arr,
					success(re) {
						console.log(JSON.stringify(re),
							'++++++++++++++')
						var datas = JSON.stringify(re);
						if (datas.indexOf("accept") != -1) {
							console.log(re)
							// uni.setStorageSync('sendMsg', true)
						}
					},
					fail: (res) => {
						console.log(res)
					}
				})
				// #endif
				if (that.myId) {
					that.show = true
				} else {
					that.meShowModel = true
					that.meTitle = '提示'
					that.meContent = '您还未登录,请先登录'
					that.meIndex = 'm1'
					that.meShowCancel = true
				}

			},
			changAge(index, item) { //选择年龄

				this.ageIndex = index
				this.minAge = item.minMoney
				this.maxAge = item.maxMoney
			},
			changHeight(index, item) { //选择身高
				this.heightIndex = index
				this.mixHeight = item.minMoney
				this.maxHeight = item.maxMoney
			},
			bindisttab(index) {
				let that = this
				// #ifdef MP-WEIXIN
				wx.requestSubscribeMessage({
					tmplIds: that.arr,
					success(re) {
						console.log(JSON.stringify(re),
							'++++++++++++++')
						var datas = JSON.stringify(re);
						if (datas.indexOf("accept") != -1) {
							console.log(re)
							// uni.setStorageSync('sendMsg', true)
						}
					},
					fail: (res) => {
						console.log(res)
					}
				})
				// #endif
				that.listIndex = index
				that.listImg = []
				that.list = []
				that.getlist()
			},
			canceldq(e) {
				this.dqIndex = 0
				this.city = ''
				this.province = ''
			},
			confirmdq(e) {
				console.log(e)
				this.city = e.city.label
				this.province = e.province.label
			},
			changesq(index) { //选择地区
				this.dqIndex = index
				if (this.dqIndex == 1) {
					this.dqshow = true
				} else {
					this.city = ''
					this.province = ''
				}
			},
			changsex(index) {
				this.sex = index

			},
			changexl(index) { //选择学历
				this.xllistIndex = index
			},
			// 获取年龄  type:1年龄筛选 2身高筛选
			getAge() {
				this.$Request.get("/app/screenMoney/getScreenMoneyList?type=1").then(res => {
					uni.hideLoading();
					if (res.code === 0) {
						this.agelist = res.data.records
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					}
				});
			},
			getHeight() { //获取身高
				this.$Request.get("/app/screenMoney/getScreenMoneyList?type=2").then(res => {
					uni.hideLoading();
					if (res.code === 0) {
						this.heightlist = res.data.records
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					}
				});
			},
			getxllist() { //学历
				this.$Request.getT('/app/dict/getDictList?name=学历').then(res => {
					if (res.code == 0) {
						let arr = []
						res.data.map(item => {
							let data = {
								name: '不限',
								id: '',
								isTrue: false
							}
							data.name = item.value
							data.isTrue = false
							arr.push(data)

						})
						this.xllist = arr
						// console.log(this.xllist)
					}
				});
			},

			shaixuan() {
				this.show = false
				this.sxIstrue = 1
				this.getlist()
			},
			chongzhi() {
				this.show = false
				this.sex = '-1'
				this.xllistIndex = -1
				this.sxIstrue = 0
				this.ageIndex = -1
				this.heightIndex = -1
				this.dqIndex = 0
				this.city = ''
				this.province = ''
				this.getlist()
			},
			onclickImg(index) {
				if (this.myId) {
					if (this.list.length <= 1) {
						// if (!this.isVip) {
						// 	this.meShowModel = true
						// 	this.meTitle = '提示'
						// 	this.meContent = '暂无更多推荐数据，开通会员享更多特权'
						// 	this.meConfirmText = '去开通'
						// 	this.meIndex = 'm3'
						// } else {
						// 	uni.showToast({
						// 		title: '暂无更多推荐数据',
						// 		icon: 'none'
						// 	})
						// }
						uni.showToast({
							title: '暂无更多推荐数据',
							icon: 'none'
						})

					} else {
						// 通过ref调用自组件的action方法来关闭当前卡片
						this.$refs.vastwuImgbox.action(index)
						// this.list.splice(0, 1)
						// let indexs = this.listImg.findIndex(item => item.id == this.list[this
						// 	.list.length - 1].id)

						// if (indexs != -1 && this.listImg.length > indexs + 1) {
						// 	this.list.push(this.listImg[indexs + 1])
						// }
						// this.isQh = true
					}

				} else {
					this.meShowModel = true
					this.meTitle = '提示'
					this.meContent = '您还未登录,请先登录'
					this.meIndex = 'm1'
					this.meShowCancel = true
				}


			},
			//确认
			meHandleBtn() {
				let that = this
				// #ifdef MP-WEIXIN
				wx.requestSubscribeMessage({
					tmplIds: that.arr,
					success(re) {
						console.log(JSON.stringify(re),
							'++++++++++++++')
						var datas = JSON.stringify(re);
						if (datas.indexOf("accept") != -1) {
							console.log(re)
							// uni.setStorageSync('sendMsg', true)
						}
					},
					fail: (res) => {
						console.log(res)
					}
				})
				// #endif
				if (that.meIndex == 'm1') {
					uni.navigateTo({
						url: '/pages/public/login'
					})
				} else if (that.meIndex == 'm3') {
					uni.navigateTo({
						url: '/my/vip/index'
					})
				} else if (that.meIndex == 'm2') {

					// uni.setStorageSync('sendMsg', true)
					console.log('确认')
					that.showModal = false
				} else if (that.meIndex == 'm4') {
					uni.navigateTo({
						url: '/my/setting/userinfo'
					})
				} else if (that.meIndex == 'm6') {
					uni.navigateTo({
						url: '/my/renzheng/index'
					})
				} else if (that.meIndex == 'm9') {
					uni.navigateTo({
						url: '/pages/my/userinfo'
					})
				}

			},
			//取消
			meHandleClose() {
				if (this.meIndex = 'm2') {
					this.showModal = true
				}

			},
			selectCity(longitude, latitude) {
				this.$Request.get('/app/Login/selectCity?lat=' + latitude + '&lng=' + longitude).then(
					res => {
						if (res.code == 0) {
							this.city = res.data.city
							uni.setStorageSync('city', res.data.city)
						}
					});
			},
			// 开启订阅消息
			openMsg() {
				console.log('订阅消息')
				var that = this
				uni.getSetting({
					withSubscriptions: true, //是否获取用户订阅消息的订阅状态，默认false不返回
					success(ret) {
						console.log(ret.subscriptionsSetting, '------------------')
						// if (ret.subscriptionsSetting.itemSettings && Object.keys(ret.subscriptionsSetting.itemSettings).length == 2) {
						if (ret.subscriptionsSetting.itemSettings) {
							uni.setStorageSync('sendMsg', true)
							uni.openSetting({ // 打开设置页
								success(rea) {
									console.log(rea.authSetting)
								}
							});
						} else { // 用户没有点击“总是保持以上，不再询问”则每次都会调起订阅消息
							console.log(99999)
							uni.setStorageSync('sendMsg', false)
							that.meShowModel = true
							that.meTitle = '提示'
							that.meContent = '为了更好的体验,请绑定消息推送'
							that.meConfirmText = '确认'
							that.meIndex = 'm2'
						}
					}
				})
			},
			getUserInfo() {
				let userId = uni.getStorageSync('userId')
				this.$Request.get("/app/userData/getUserDataInfo?byUserId=" + userId).then(res => {
					if (res.code == 0 && res.data) {
						this.formData = res.data
					}
					uni.hideLoading();
				});


			},
			checkEmptyOrNullProperties(obj) {
				let keysWithEmptyOrNullValues = [];

				for (let key in obj) {
					if (obj.hasOwnProperty(key)) {
						if (obj[key] === null || obj[key] === '') {
							keysWithEmptyOrNullValues.push(key);
						}
					}
				}

				return keysWithEmptyOrNullValues;
			},
			getIsVip() {
				this.$Request.get("/app/user/selectUserById").then(res => {
					if (res.code == 0) {
						if (res.data.isVip && res.data.isVip == 1) {
							this.isVip = true
							uni.setStorageSync('isVIP', this.isVip)
						} else {
							this.isVip = false
							uni.setStorageSync('isVIP', this.isVip)
						}
					}
				});
			},
			getlist() {
				console.log(this.sex, '*********')
				let sortType = this.listtab[this.listIndex].id

				let data = {
					sortType: sortType,
					locationCity: this.sxIstrue == 1 || this.listIndex != 0 ? this.city : '',
					// city: this.sxIstrue == 1 || this.listIndex != 0 ? this.city : '',
					minAge: this.ageIndex != -1 ? this.minAge : '',
					maxAge: this.ageIndex != -1 ? this.maxAge : '',
					minUserHeight: this.heightIndex != -1 ? this.mixHeight : '',
					maxUserHeight: this.heightIndex != -1 ? this.maxHeight : '',
					excludeUserId: this.myId ? this.myId : '',
					education: this.xllistIndex != -1 ? this.xllist[this.xllistIndex].name : '',
					sex: this.sex != -1 ? this.sex : '',
					isFilter: 1
				}
				this.$Request.get("/app/userData/getAppUserDataList", data).then(res => {
					if (res.code == 0) {
						if (res.data.records.length > 0) {
							res.data.records.forEach((d, index) => {
								if (d.userImg) {
									let userImg = d.userImg.split(',')
									d.userImg = userImg[0]
								}
							})
							this.listImg = res.data.records
						} else {
							this.listImg = []
						}
						this.$nextTick(() => {
							this.$forceUpdate()
						})
					}
				});
			},

		}
	}
</script>

<style lang="scss">
	page {
		background-color: #F7F7F7;
	}

	/deep/uni-slider {
		margin: 10px 0px !important;
	}

	/deep/ uni-slider .uni-slider-handle-wrapper {
		height: 20rpx !important;
	}

	.bgs {
		width: 100%;
		height: 346rpx;
		// background: linear-gradient(90deg, #E4E7F8 0%, #F1E3F4 46%, #FDE1EF 100%);
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 9;
	}

	.content {
		position: relative;
		z-index: 99;
	}

	.headtop {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 35rpx 30rpx;
		position: fixed;
		/* #ifdef H5 */
		top: 0rpx;
		/* #endif */
		/* #ifndef H5 */
		top: 136rpx;
		/* #endif */
		left: 0;
		right: 0;
		z-index: 9999;
		background: linear-gradient(90deg, #E4E7F8 0%, #F1E3F4 46%, #FDE1EF 100%);

		.headleft {
			font-size: 34rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #999999;
			margin-right: 68rpx;
		}



		.actleft {
			font-size: 38rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #333333;
		}
	}

	.my-box {
		width: 686rpx;
		margin: 0rpx auto;
		position: fixed;
		/* #ifdef H5 */
		top: 130rpx;
		/* #endif */
		/* #ifndef H5 */
		top: 280rpx;
		/* #endif */
		left: 50%;
		transform: translate(-50%, 0);

	}

	.tites1 {
		font-size: 32rpx;
		font-family: PingFang SC;
		font-weight: 800;
		color: #333333;
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		background: #FFFFFF;
		padding: 30rpx 0;
	}

	.tites {
		font-size: 32rpx;
		font-family: PingFang SC;
		font-weight: 800;
		color: #333333;
	}

	.xlbox {
		height: 80rpx;
		background: #F7F7F7;
		border-radius: 12rpx;
		padding: 0 30rpx;
		display: flex;
		align-items: center;
		font-size: 26rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #666666;
		margin-right: 20rpx;
	}

	.xlboxAct {
		background-color: rgba(181, 203, 255, 0.5);
		color: #6265FF;
	}

	.btn {
		width: 48%;
		// width: 686rpx;
		height: 100rpx;
		background: linear-gradient(90deg, #787AFD, #6265FF);
		box-shadow: 3rpx 6rpx 12rpx 0rpx #D3D4FF;
		border-radius: 24rpx;
		font-size: 32rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: #FFFFFF;
		display: flex;
		align-items: center;
		justify-content: center;

	}

	.tanbers {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		z-index: 999;
		padding: 30rpx;
		background-color: #FFFFFF;
	}

	.swiper-banner {
		/* #ifndef H5 */
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		/* #endif */

	}

	.headtops {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 30rpx 0;


		.headleft {
			font-size: 34rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #999999;
			margin-right: 68rpx;
		}

		.line {
			width: 20rpx;
			margin: 0 auto;
			height: 6rpx;
			background: #010101;
			border-radius: 50rpx;

		}

		.actleft {
			font-size: 38rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #010101;
		}
	}

	.litix {

		.listiem {
			background: #FFFFFF;
			border-radius: 32rpx;
			margin: 20rpx 30rpx;
			padding: 30rpx 20rpx;
			display: flex;

			.main-img {
				width: 150rpx;
				height: 150rpx;
				border-radius: 50%;
			}

			.yhm {
				font-size: 32rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: #333333;
				margin-right: 10rpx;
			}

			.wezhi {
				font-family: PingFang SC;
				font-weight: 500;
				font-size: 24rpx;
				color: #999999;
			}

			.sexicon {
				background: #38CAFF;
				border-radius: 10rpx;
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #FFFFFF;
				padding: 4rpx 10rpx;
				margin-right: 10rpx;
			}

			.sexicons {
				background: #fbe2f4;
				border-radius: 10rpx;
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #FFFFFF;
				padding: 4rpx 10rpx;
				margin-right: 10rpx;
			}

			.labe {
				background: #F2F2F2;
				border-radius: 10rpx;
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #999999;
				padding: 4rpx 10rpx;
				margin-right: 15rpx;
			}

			.remk {
				font-family: PingFang SC;
				font-weight: 500;
				font-size: 24rpx;
				color: #666666;
				margin-top: 15rpx;
			}

		}
	}
</style>
